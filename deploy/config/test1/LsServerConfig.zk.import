# cat server/trunk/icefire-game/conf/LsServerConfig.zk.import | grep -v '^#' | grep -v '^$' | /data2/bin/apache-zookeeper-3.5.5-bin/bin/zkCli.sh


deleteall /LS_V1
create /LS_V1 ''

#==============================
# WebServer
#==============================
create /LS_V1/WebServer ''

create /LS_V1/WebServer/rpc_ip '*************'
create /LS_V1/WebServer/rpc_port '20010'
create /LS_V1/WebServer/rpc_bind_ip '0.0.0.0'
create /LS_V1/WebServer/rpc_bind_port '20010'

create /LS_V1/WebServer/Redis ''
create /LS_V1/WebServer/Redis/host '*************'
create /LS_V1/WebServer/Redis/port '6379'
create /LS_V1/WebServer/Redis/password 'redistest'

create /LS_V1/WebServer/MySql ''
create /LS_V1/WebServer/MySql/url '****************************************************************************************************************************'
create /LS_V1/WebServer/MySql/username 'root'
create /LS_V1/WebServer/MySql/password 'root'

#==============================
# 战斗服配置
#==============================
create /LS_V1/BattleServer ''
set /LS_V1/BattleServer/host 'http://*************:9000'

#==============================
# 导流配置
#==============================
create /LS_V1/NewPlayer ''
create /LS_V1/NewPlayer/server_settings '{"roleNumberLimitForResetOpentime":0}'
create /LS_V1/NewPlayer/auto_diversion_settings '{"minOpenServer":1, "detectionIntervalSecond":10, "maxRegistedRole":80000, "maxRegisterPerSecond":1, "maxOnlineRole":3000, "maxInMemoryRole":60000}'

#==============================
# 游戏服务器（组）
#==============================
create /LS_V1/GameServers ''

#====================
# 游戏1服
#====================
create /LS_V1/GameServers/1 ''

create /LS_V1/GameServers/1/Redis ''
create /LS_V1/GameServers/1/Redis/host '*************'
create /LS_V1/GameServers/1/Redis/port '6379'
create /LS_V1/GameServers/1/Redis/password 'redistest'

create /LS_V1/GameServers/1/MongoDB ''
create /LS_V1/GameServers/1/MongoDB/url '***************************************************************'
create /LS_V1/GameServers/1/MongoDB/db_name 'gameserver_1'

create /LS_V1/GameServers/1/game_host '133-warz-wss-test01.tytuyoo.com'
create /LS_V1/GameServers/1/game_ip '***************'
create /LS_V1/GameServers/1/game_port '443'
create /LS_V1/GameServers/1/game_bind_ip '0.0.0.0'
create /LS_V1/GameServers/1/game_bind_port '20009'
create /LS_V1/GameServers/1/rpc_ip '*************'
create /LS_V1/GameServers/1/rpc_port '20008'
create /LS_V1/GameServers/1/rpc_bind_ip '0.0.0.0'
create /LS_V1/GameServers/1/rpc_bind_port '20008'
create /LS_V1/GameServers/1/enable 'true'

# 一定要保证 GAME_CONFIG_END_FLAG 是这个文件的最后条目！
create /LS_V1/GameServers/1/GAME_CONFIG_END_FLAG 'GAME_CONFIG_END_FLAG'