{"env": "DEV", "clientIoWorkerCount": 2, "asyncThreadSize": 8, "bindThreadSize": 10, "saveThreadSize": 2, "biLogThreadSize": 4, "checkWord": false, "loadWorldLocation": true, "forCommandPlayer": false, "newPlayerCloseFunc": true, "newLoginFunc": true, "serverPushSwitch": false, "pushThreadSize": 10, "sendCityInfoAfterPlayerInfo": true, "jongoMapperFailOnUnknownPrpperties": false, "battleBindThreadSize": 8, "firebaseConfig": "", "firebaseDatabaseUrl": "", "migrateErrorDataHandleOpen": false, "tickCalcOpen": true, "gcMsgAsyncOpen": false, "asyncMailThreadSize": 12, "mailSdkAppId": "20001", "mailSdkAppSecret": "7NFQsnNs0IVg", "mailSdkUri": "https://pt110-mailcn-test.tuyoo.com", "asyncIOThreadSize": 10, "asyncRPCThreadSize": 10, "throwsExceptionIfDuplicatePrimaryKey": true, "cacheMongoClient": true, "newChatAppId": 35001, "newChatURL": "https://chatapi-cn-wzs-test.tuyoo.com", "platform": "CHINA"}