package com.lc.billion.icefire.web.bus.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.ApiConstants;
import com.lc.billion.icefire.web.bus.risk.service.IRestrictedUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.annotation.Resource;
import java.util.Arrays;

/**
 * @ClassName RestrictedUserController
 * @Description
 * <AUTHOR>
 * @Date 2024/11/27 14:37
 * @Version 1.0
 */
@RequestMapping(value = {ApiConstants.WARZ_BASE, ApiConstants.LEGACY_SANGUO2})
@Controller
public class RestrictedUserController {

    private final Logger logger = LoggerFactory.getLogger(RestrictedUserController.class);

    @Resource
    private IRestrictedUserService restrictedUserService;

    @RequestMapping(value = "/restricted/wordTrigger")
    public @ResponseBody String wordTrigger(Long roleId, String[] words, String context) {
        logger.info("wordTrigger roleId:{} word:{} context:{}", roleId, words, context);
        restrictedUserService.addUserByWords(roleId, Arrays.stream(words).toList(), context);
        JSONObject jo = new JSONObject();
        jo.put("ret", 0);
        jo.put("data", "");
        return jo.toJSONString();
    }
}
