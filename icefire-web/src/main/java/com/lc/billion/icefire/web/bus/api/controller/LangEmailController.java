package com.lc.billion.icefire.web.bus.api.controller;

import com.lc.billion.icefire.core.ApiConstants;
import jakarta.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.web.bus.gm.model.LanguageEmail;
import com.lc.billion.icefire.web.bus.gm.service.ILanguageEmailService;

/**
 * <AUTHOR>
 * @since 2016年11月20日下午4:23:32
 *
 */
@Controller
@RequestMapping(value = {ApiConstants.WARZ_BASE, ApiConstants.LEGACY_SANGUO2})
public class LangEmailController {

	private Logger LOG = LoggerFactory.getLogger(getClass());

	@Autowired
	private ILanguageEmailService langEmailSrv;

	@RequestMapping(value = "/langemail", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String getEmail(HttpServletRequest request) {
		String key = request.getParameter("key");
		JSONObject json = new JSONObject();
		LanguageEmail langEmail = langEmailSrv.getLanguageEmailByKey(key);
		if (null == langEmail) {
			LOG.error("language email error, not found key:{}", key);

			json.put("ret", -1);
			return json.toJSONString();
		}

		String lang = request.getParameter("lang");
		String content = langEmailSrv.getContectByLang(key, lang);
		String title = langEmailSrv.getTitileByLang(key, lang);

		// 没有该语言邮件，默认填充英语
		if (null == title || null == content) {
			content = langEmailSrv.getContectByLang(key, "EN");
			title = langEmailSrv.getTitileByLang(key, "EN");
		}

		// 没有英文，填充中文
		if (null == title || null == content) {
			content = langEmailSrv.getContectByLang(key, "ZN");
			title = langEmailSrv.getTitileByLang(key, "ZN");
		}

		json.put("ret", 0);
		json.put("title", title);
		json.put("content", content);

		LOG.info("receive request language email, key:{}, language:{}, title:{}, content:{}", key, lang, title, content);

		return json.toJSONString();
	}

}
