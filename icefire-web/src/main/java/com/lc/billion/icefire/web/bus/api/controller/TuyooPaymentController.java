package com.lc.billion.icefire.web.bus.api.controller;


import com.alibaba.fastjson.JSON;
import com.lc.billion.icefire.core.ApiConstants;
import com.lc.billion.icefire.core.common.PlatformType;
import com.lc.billion.icefire.web.bus.api.service.impl.OrderServiceImpl;
import com.lc.billion.icefire.web.bus.api.service.impl.tuyoo.TuyooPayment;
import com.lc.billion.icefire.web.bus.user.entity.User;
import com.lc.billion.icefire.web.bus.user.service.IUserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName TuyooPaymentController
 * @Description
 * <AUTHOR>
 * @Date 2023/12/5 11:35
 * @Version 1.0
 */
@Controller
@RequestMapping(value = {ApiConstants.WARZ_BASE, ApiConstants.LEGACY_SANGUO2})
public class TuyooPaymentController {
    private final Logger logger = LoggerFactory.getLogger(TuyooPaymentController.class);

    @Autowired
    private IUserService userService;

    @Autowired
    private TuyooPayment tuyooPayment;
    @Autowired
    private OrderServiceImpl orderService;



    /**
     * 订单
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/order", produces = "text/plain; charset=UTF-8")
    public @ResponseBody String order(HttpServletRequest request) {
        String roleIdStr = request.getParameter("roleId");
        long roleId = Long.parseLong(roleIdStr);

        User user = userService.findByRoleId(roleId);
        String productId = request.getParameter("productId");
        String currencyName = request.getParameter("currencyName");
        String localCurrency = request.getParameter("localCurrency");
        String extData = request.getParameter("extData");
        String appsFlyerId = request.getParameter("unionid");
        String seasonStr = request.getParameter("season");
        int season = 1;
        if (!StringUtils.isBlank(seasonStr)) {
            season = Integer.parseInt(seasonStr);
        }
        logger.info("order : productId={}, currencyName={}, localCurrency={}, extData={},appsFlyerId={}, season={}", productId, currencyName, localCurrency, extData, appsFlyerId, season);
        return tuyooPayment.order(user, PlatformType.TUYOO.getId(), productId, currencyName, localCurrency, extData, appsFlyerId, season);
    }


    /**
     * 发货
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/pay", produces = "text/plain; charset=UTF-8")
    public @ResponseBody String pay(HttpServletRequest request) {
        return tuyooPayment.pay(request);
    }
    /**
     * 订单
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryStock", produces = "text/plain; charset=UTF-8")
    public @ResponseBody String queryStock(HttpServletRequest request) {
        String roleIdStr = request.getParameter("roleId");
        long roleId = Long.parseLong(roleIdStr);
        User user = userService.findByRoleId(roleId);
        String appInfo = request.getParameter("appInfo");
        String productId = request.getParameter("productId");//这是计费点 实际的礼包ID从appInfo传过来
        String userId = request.getParameter("userId");
        boolean canBuy = tuyooPayment.queryStock(user, appInfo);
        logger.info("queryStock : productId={}, roleId={}, userId={}, appInfo={} canBuy={}", productId, roleId, userId, appInfo, canBuy);
        Map<String, Boolean> responseMap = new HashMap<>();
        responseMap.put("result", canBuy);
        return JSON.toJSONString(responseMap);
    }

    @RequestMapping(value = "/rolePlatform", produces = "text/plain; charset=UTF-8")
    public @ResponseBody String platformId(HttpServletRequest request) {
        return tuyooPayment.rolePlatform(request);
    }


    @RequestMapping(value = "/iosRefund", produces = "text/plain; charset=UTF-8",method = RequestMethod.POST)
    public ResponseEntity<String> iosRefund(HttpServletRequest request) {
        return tuyooPayment.iosRefund(request);
    }
}
