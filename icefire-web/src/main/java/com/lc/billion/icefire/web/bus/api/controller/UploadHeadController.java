package com.lc.billion.icefire.web.bus.api.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.ApiConstants;
import com.lc.billion.icefire.core.common.JsonUtils;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Utils;
import com.lc.billion.icefire.game.biz.redis.RedisClient;
import com.lc.billion.icefire.web.bus.api.service.lock.DistributedLock;
import com.lc.billion.icefire.web.bus.api.service.lock.LockKeyUtils;
import com.lc.billion.icefire.web.bus.gm.model.AvatarReportRecord;
import com.lc.billion.icefire.web.bus.gm.service.IAvatarService;
import com.lc.billion.icefire.web.bus.gm.service.impl.WebRPCService;
import com.lc.billion.icefire.web.bus.user.entity.User;
import com.lc.billion.icefire.web.bus.user.service.IUserService;
import com.lc.billion.icefire.web.utils.wx.AesException;
import com.simfun.sgf.utils.JavaUtils;
import jakarta.servlet.annotation.MultipartConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Controller
@MultipartConfig
@RequestMapping(value = {ApiConstants.WARZ_BASE, ApiConstants.LEGACY_SANGUO2})
public class UploadHeadController {
    public static final String WX_HEAD_REDIS_PREFIX = "wx:head:";
    private Logger LOG = LoggerFactory.getLogger(getClass());


    @Autowired
    @Qualifier("redisClient0")
    private RedisClient redisClient;

    @Autowired
    private IAvatarService avatarService;
    @Autowired
    private IUserService userService;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private WebRPCService webRPCService;

    /**
     *
     */
    @RequestMapping(value = "/wxMediaCheck")
    public @ResponseBody String wxMediaCheck(HttpServletRequest request) {
        String body = getRequestBody(request);
        JSONObject bodyObject = JSONObject.parseObject(body);
        if (bodyObject == null) {
            return "";
        }
        String traceId = bodyObject.getString("trace_id");
        var result = bodyObject.getJSONObject("result");
        var suggest = result.getString("suggest");
        String redisData = redisClient.get(WX_HEAD_REDIS_PREFIX + traceId);
        if (!"pass".equals(suggest)) {
            LOG.error("校验不通过 traceId={} data={} body={} ", traceId, redisData, body);
            //校验通过 从redis里拿到信息通知游戏服
            return "";
        } else {
            LOG.info("校验通过 traceId={} data={} body={} ", traceId, redisData, body);
        }
        if (!JavaUtils.bool(redisData)) {
            return "";
        }
        JSONObject gsData = JSON.parseObject(redisData);
        String roleId = gsData.getString("roleId");
        String serverId = gsData.getString("serverId");
        String head = gsData.getString("head");
        Map<Long, String> allowMap = new HashMap<>();
        allowMap.put(Long.valueOf(roleId), head);
        this.webRPCService.getProxy(Integer.parseInt(serverId)).allowAvatar(allowMap);
        return "";
    }


    /**
     * 头像举报
     *
     * @param request
     * @return http://10.1.14.94:8080/api/upload/headReport?targetId=6000060000002&signature=f2a0c9fd80118a7c3ecfd590258e5683&roleId=6000060000001&headURL=https://pics.javcdn.pw/cover/75wj_b.jpg&cTime=1596527372838&serverId=1&
     */
    @RequestMapping(value = "/headReport")
    public ResponseEntity<String> report(HttpServletRequest request) {
        long roleId = Long.parseLong(request.getParameter("roleId"));
        long targetId = Long.parseLong(request.getParameter("targetId"));
        int serverId = Integer.parseInt(request.getParameter("serverId"));
        String avatarURL = request.getParameter("headURL");
        String cTime = request.getParameter("cTime");
        String signature = request.getParameter("signature");
        LOG.info("head report:" + "roleId=" + roleId + " targetId=" + targetId + " serverId=" + serverId + "time=" + cTime + " signature=" + signature);
        if (StringUtils.isBlank(signature)) {
            LOG.error("get signature is empty");
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        if (!simpleVerify(cTime, signature, String.valueOf(roleId), String.valueOf(targetId), String.valueOf(serverId))) {
            return new ResponseEntity<>(HttpStatus.FORBIDDEN);
        }
        //判断被举报玩家是否正在更新头像，如果在更新头像中则当前这条举报可以忽略
        if (redisClient.exists(LockKeyUtils.getUpdateHeadKey(String.valueOf(targetId)))) {
            return new ResponseEntity<>(HttpStatus.OK);
        }
        DistributedLock lock = new DistributedLock(redisClient, LockKeyUtils.getUpdateHeadKey(String.valueOf(targetId)));
        try {
            if (lock.lock()) {
                AvatarReportRecord reportRecord = avatarService.getReportByRoleId(targetId);
                if (reportRecord == null) {
                    reportRecord = new AvatarReportRecord();
                    reportRecord.setRoleId(targetId);
                    reportRecord.setAmount(1);
                    reportRecord.setAvatarURL(avatarURL);
                    reportRecord.setLastTime(System.currentTimeMillis());
                    avatarService.saveReport(reportRecord);
                } else {
                    if (avatarURL.equals(reportRecord.getAvatarURL())) {
                        reportRecord.addAmount();
                        reportRecord.setLastTime(System.currentTimeMillis());
                    } else {
                        User user = userService.findByRoleId(targetId);
                        if (user != null && avatarURL.equals(user.getRoleHead())) {
                            reportRecord.setAmount(1);
                            reportRecord.setAvatarURL(avatarURL);
                            reportRecord.setLastTime(System.currentTimeMillis());
                        }
                    }
                    avatarService.updateReport(reportRecord);
                }
            } else {
                LOG.error("丢弃重复请求; head report target repeat request! role:{}, target:{}, headURL:{}", roleId, targetId, avatarURL);
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            LOG.error("delete upload file error.", e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } finally {
            lock.unlock();
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    private boolean simpleVerify(String cTime, String signature, String... params) {
        if (cTime.length() < 10) {
            return false;
        }
        String first = cTime.substring(0, 3);
        String end = cTime.substring(cTime.length() - 3);
        String secret = Utils.createMD5String(Utils.createMD5String(first) + Utils.createMD5String(end));
        StringBuilder paramStr = new StringBuilder();
        for (String param : params) {
            paramStr.append(param);
        }
        paramStr.append(secret);
        return Utils.authMD5String(signature, paramStr.toString());
    }

    private String getRequestBody(HttpServletRequest request) {
        StringBuilder body = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return body.toString();
    }

    public static void main(String args[]) throws AesException {
        WxPayController controller = new WxPayController();
        controller.init();
        String encrypt = "dnbxk1uR5Nc/x+0c3NCB3BMEO9pEwrgMc35NbQ3dQGzEP4qcM+oC4ckXzbxEtwLJ/DDb5eCBpBuSqK9wBs/fX5QLarvEhxscJ6DaWn3mf+8Ns0nNU6iApJ1rPYRBz2uXvxXGqL+cet1O/16zzvneVvQ2HInGTjLtys6CQgfqkeJSLphYrWQU+NRwp+NrAdSIvszsVwQqFfkZ/DF5cFlSL29kRFqBGyqBxgoNUsHJx9vceIUTc738mYF9+NH4slDUPCpatH5zvP6/RpRYAcoXXk+Qlox4MyoMkE3tpwYH3tlQsVv1GzvcTt3g6hcdzIpbOzoCAubvtLU5r6nWwOEBdLWpCr/YkPSt1FrgR08FwZc+1fEV0rYIv5fjIXtocI/LEkPllvi0OUudARlIcfUNJK5Nmlb0+yu3CJ7Oz99BSiFOkXzGze2Eh04rocbPMoLQ9DBroiDG1EthTmPeTG/MnBQMHkbzaLY6JOaRGeZ1FZVnlnMnzVQK/rGWyRM/459WgEW3CouDwdaiokdAk6l5XXuMiqTmdfpyrjvEKmuxXKbZUNzI5exzEiSRxLWbuTWKi8BgrpLvHq2MiFB9wZN7aPeZ91QB+aNNBeJRyLiQBcAiZjB8QaheT6/Kwz/EnmID5lkRJrw3iCS3DVD5aU3Whmz4Q07D/KDmRntMr/XmgbYbdY7zX8jat+fYz2TbyArpCID+tqs74IFRfJtL3JCiATtOyw1ibnRNDaYRL9Yln2OG1GcxjIcI3KPDq/BDZSKnrkB4BFaSv35bAuldUqVm7LyJWEWxkrlfaHRRuQfUXAKq/072DSrROjxtpeIbYKwAYS2adnurTSozuu1+YcgJHMXzeqIeKzz1HhBf4cNTw3sZPytc84uUjQlds0lc0zTvPHkrR0CWOfRlbhulGU0iwz1j9f3BPMvh4/9wgI+Cx30=";
        String nonce = "1106930668";
        String timestamp = "1716881805";
        String signature = "91699bf1e042f058f6d81176df8bbda18a91f4fb";
        String text = controller.getWxBizMsgCrypt().decryptMsg(signature, timestamp, nonce, encrypt);
        System.out.println(text);
//        System.out.println(msg);

//        System.out.println(msg);

    }

    @RequestMapping(value = "/chatImageCheck")
    public @ResponseBody String chatImageCheck(HttpServletRequest request) {
        String body = getRequestBody(request);
        JSONObject bodyObject = JSONObject.parseObject(body);
        if (bodyObject == null) {
            return "";
        }
        String traceId = bodyObject.getString("uid");
        var result = bodyObject.getInteger("state");
        String redisData = redisClient.get(WX_HEAD_REDIS_PREFIX + traceId);
        if (!(result == 0)) {
            LOG.error("chatImageCheck 校验不通过 traceId={} data={} body={} state={}", traceId, redisData, body, result );
            //校验通过 从redis里拿到信息通知游戏服
            return "";
        } else {
            LOG.info("chatImageCheck 校验通过 traceId={} data={} body={} ", traceId, redisData, body);
        }
        if (!JavaUtils.bool(redisData)) {
            return "";
        }
        JSONObject gsData = JSON.parseObject(redisData);
        String roleId = gsData.getString("roleId");
        String serverId = gsData.getString("serverId");
        String head = gsData.getString("head");
        Map<Long, String> allowMap = new HashMap<>();
        allowMap.put(Long.valueOf(roleId), head);
        this.webRPCService.getProxy(Integer.parseInt(serverId)).allowAvatar(allowMap);
        return "";
    }
}
