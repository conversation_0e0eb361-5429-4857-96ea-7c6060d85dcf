package com.lc.billion.icefire.web.bus.api.account.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.VersionManager;
import com.lc.billion.icefire.cache.CacheManager;
import com.lc.billion.icefire.core.ApiConstants;
import com.lc.billion.icefire.core.Constants;
import com.lc.billion.icefire.core.common.PlatformType;
import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.support.Utils;
import com.lc.billion.icefire.core.utils.ShortKeyUtil;
import com.lc.billion.icefire.web.bus.api.account.ApiErrorCode;
import com.lc.billion.icefire.web.bus.api.account.BaseAccountController;
import com.lc.billion.icefire.web.bus.api.account.Place;
import com.lc.billion.icefire.web.bus.api.account.entity.Account;
import com.lc.billion.icefire.web.bus.api.account.entity.AccountBind;
import com.lc.billion.icefire.web.bus.api.account.service.IAccountBindService;
import com.lc.billion.icefire.web.bus.api.account.service.IAccountService;
import com.lc.billion.icefire.web.bus.api.service.*;
import com.lc.billion.icefire.web.bus.api.service.impl.SpecialNewUserServiceImpl;
import com.lc.billion.icefire.web.bus.gm.service.IBillboardService;
import com.lc.billion.icefire.web.bus.gm.service.IEmailEndpointService;
import com.lc.billion.icefire.web.bus.gm.service.impl.VirtualThreadService;
import com.lc.billion.icefire.web.bus.risk.service.IRestrictedInfoService;
import com.lc.billion.icefire.web.bus.risk.service.IRestrictedUserService;
import com.lc.billion.icefire.web.bus.server.entity.Server;
import com.lc.billion.icefire.web.bus.server.entity.ServerStatusEnum;
import com.lc.billion.icefire.web.bus.server.service.impl.ServerServiceImpl;
import com.lc.billion.icefire.web.bus.user.entity.User;
import com.lc.billion.icefire.web.log.game.core.CoreLogUtil;
import com.lc.billion.icefire.web.utils.HttpUtils;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.GameServerConfig.KvkInstance;
import com.longtech.ls.zookeeper.WebServerConfig;
import com.simfun.sgf.utils.JavaUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.sql.Timestamp;
import java.util.*;

/**
 * 多角色账号登录系统
 *
 * <AUTHOR>
 * @since 2020-9-10
 */
@Controller
@RequestMapping(value = {ApiConstants.WARZ_BASE, ApiConstants.LEGACY_SANGUO2})
public class MultipleRoleAccountController extends BaseAccountController {

	@Autowired
	private IAccountBindService accountBindService;
	@Autowired
	private GeoIpService geoIpService;
	@Autowired
	private IBillboardService billboardService;
	@Autowired
	private IAccountService accountService;
	@Autowired
	private AuthRepository authRepository;
	@Autowired
	private IEmailEndpointService emailEndpointService;
	@Autowired
	private ConfigCenter configCenter;
	@Autowired
	private VersionManager versionManager;
	@Autowired
	private Ip2RegionService ip2RegionService;
    @Autowired
    private SpecialNewUserServiceImpl specialNewUserServiceImpl;
	@Autowired
	private VirtualThreadService virtualThreadService;
	@Autowired
	private IRestrictedUserService restrictedUserService;
	@Autowired
	private IRestrictedInfoService restrictedInfoService;
	@Autowired
	private CoreLogUtil coreLogUtil;

	@Override
	protected IAuthContext doAuth(String id, String token, int platform) {
		// 平台认证
		IAuthService authService = authRepository.getService(platform);
		return authService.auth(id, token);
	}

	@Override
	protected String doLogin1(String id, String token, String deviceId, int platform, String gaId, String appVersion, String os, int serverId, String wxOpenId, String wxUnionId, HttpServletRequest request) {
		long start = System.currentTimeMillis();
		logger.info("[doLogin] id: {}, token: {}, deviceId: {}, platform: {}, gaId: {}, appVersion: {}, os: {}, serverId: {}, wxOpenId: {}, wxUnionId: {}, request:{} start: {}",
				id, token, deviceId, platform, gaId, appVersion, os, serverId, wxOpenId, wxUnionId, request, start);
		JSONObject json = new JSONObject();

		IAuthContext authContext = doAuth(id, token, platform);
		if (!authContext.isSuccess()) {
			json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
			json.put("msg", "Failed user authentication.");
			logger.error("Failed user authentication. token: {}, platform: {}", token, platform);
			return json.toJSONString();
		}
		json.put("ret", 0);
		json.put("msg", "test success");
		logger.info("User authentication success. token: {}, platform: {}", token, platform);
		return json.toJSONString();
	}

	/**
	 *
	 * @param id
	 * @param token
	 * @param deviceId
	 * @param platform
	 * @param gaId
	 * @param appVersion
	 * @param os
	 * @param serverId
	 * @param request
	 * @return
	 */
	@Override
	protected String doLogin(String id, String token, String deviceId, int platform, String gaId, String appVersion, String os, int serverId, String wxOpenId, String wxUnionId, HttpServletRequest request) {
		long start = System.currentTimeMillis();
        logger.info("[doLogin] id: {}, token: {}, deviceId: {}, platform: {}, gaId: {}, appVersion: {}, os: {}, serverId: {}, wxOpenId: {}, wxUnionId: {}, request:{} start: {}",
                id, token, deviceId, platform, gaId, appVersion, os, serverId, wxOpenId, wxUnionId, request, start);
		JSONObject json = new JSONObject();

		IAuthContext authContext = doAuth(id, token, platform);
		if (!authContext.isSuccess()) {
			json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
			json.put("msg", "Failed user authentication.");
			logger.error("Failed user authentication. token: {}, platform: {}", token, platform);
			return json.toJSONString();
		}
		if (!JavaUtils.bool(gaId)) {
			gaId = id;
		}

        String platformUid = authContext.getUserId();

		// 本次登陆基本信息~记日志用
		String loginBaseInfo = new StringBuilder().append("token : [").append(token).append("], id [").append(id).append("], serverId").append(serverId).append("]").toString();
		Server server;
		User user;
		String ip = HttpUtils.getIpAddr(request);
		String geoIpCountry = geoIpService.getCountryCode(ip);
		AccountBind accountBind = accountBindService.findByPlatformIdAndPlatform(platformUid, platform);
		logger.info("[doLogin] getAccountBind info: {} loginBaseInfo: {}, use time {}", JSON.toJSONString(accountBind), loginBaseInfo, System.currentTimeMillis() - start);
		boolean register = false;
		String bundleId = request.getParameter("bundleid");
		String country = request.getParameter("country"); // 客户端传过来的是国家名称。
		String channel = request.getParameter("channel"); // 客户端传过来的是包类型。
		// 渠道字段
		String intClientId = request.getParameter("intClientId"); // 渠道类型
		if (StringUtils.isEmpty(intClientId)) {
			intClientId = "-1";
		}

		// 归因导量参数
		String subChannel = request.getParameter("subChannel");

		// TODO 测试代码,根据Id取模确定是哪个渠道
//		String[] testArray = new String[]{"", "JD-jrtt-xtzb-jccxy", "JD-jrtt-xtzb-01", "JD-jrtt-xtzb-lhh"};
//		int index = (Integer.parseInt(id)) % testArray.length;
//		subChannel = testArray[index];
		logger.info("[归因导量] 用户信息 id: {}, subChannel: {}", id, subChannel);

		Account account = null;

		boolean isNewRole = false;

        // 判断AccountBind是否存在
		if (accountBind != null) {
			// 获取Account
			account = accountService.findAccountById(accountBind.getAccId());
			if (account == null) {
				logger.error("platformUid[{}] get Account[{}] is null! please check", platformUid, accountBind.getAccId());
				json.put("ret", ApiErrorCode.ACCOUNT_NOT_FOUND);
				json.put("msg", "account not found.");
				return json.toJSONString();
			}
            long roleId = account.getMainRoleId();
            user = checkOtherUser(roleId, account, request, platformUid);
			if (user == null) {
				logger.error("{}getMainRoleId对应的角色[{}]不存在", platformUid, roleId);
				json.put("ret", -2);
				json.put("msg", "Please setting recommend server.");
				return json.toJSONString();
			}
			server = serverService.getById(user.getCurrentServerId());
			if (server == null) {
				logger.error(platformUid + "的角色" + roleId + "所在Server " + user.getCurrentServerId() + " 未找到");
				json.put("ret", ApiErrorCode.GAME_SERVER_ID_ERROR);
				json.put("msg", "Server " + user.getCurrentServerId() + " not found.");
				return json.toJSONString();
			}
			server = createKVKServer(server);
			if (server == null) {
				logger.error(platformUid + "的角色" + roleId + "所在Server " + user.getCurrentServerId() + " 的赛季服未找到");
				json.put("ret", ApiErrorCode.GAME_SERVER_ID_ERROR);
				json.put("msg", "Server " + user.getCurrentServerId() + " not found.");
				return json.toJSONString();
			}
			// 发送绑定奖励邮件
			if (server.getServerStatus() != ServerStatusEnum.CLOSE.getId() && account.isNeedSendBindMail() && !user.isSendBindMail()) {
				emailEndpointService.sendFirstBindEmail(user);
			}
			if (user.getLoginCnt() < 1) {
				isNewRole = true;
			}
		} else {
//			if ("CN".equalsIgnoreCase(geoIpCountry)) {
//				json.put("ret", ApiErrorCode.REGISTRATION_PROHIBITED);
//				json.put("msg", "中国地区暂时不开放注册");
//				logger.error("中国地区暂时不开放注册. token: {}, platform: {}", token, platform);
//				return json.toJSONString();
//			}
			// 新手服模式,最终目标服Id
			int targetServerId = 0;
			// else 分支是新号注册，优先进好友分享者的服
			try {
				String sharedIdStr = request.getParameter("shareRoleId");
				// 如果传递了分享者id, 就尝试选服逻辑
				if (sharedIdStr != null && !sharedIdStr.isEmpty() && !"undefined".equals(sharedIdStr)) {
					try {
						long sharedId = Long.parseLong(sharedIdStr);
						Integer[] serverIds = chooseServerByShareRole(sharedId); // 父类的方法
						serverId = serverIds[0];
						targetServerId = serverIds[1];
						logger.warn("客户端走分享登录，自动计算好友服 id={} sharedIdStr={} serverId={}", id, sharedIdStr, serverId);
					} catch (Exception e) {
						logger.warn("无效的分享参数，自动计算好友服 id={} sharedIdStr={} serverId={} sharedIdStr={}",
								id, sharedIdStr, serverId, sharedIdStr);
					}
				} else {
					logger.warn("无效的分享参数，自动计算好友服 id={} sharedIdStr={} serverId={}", id, sharedIdStr, serverId);
				}
			} catch (Exception e) {
				logger.error("自动选服抛异常 ", e);
			}
			logger.warn("before getServer serverId={}", serverId);
			// 未开启新手服,且开启了归因导量,直接计算归因导量服务器
			if (!configCenter.getLsConfig().isGuideServerOpen() && configCenter.getLsConfig().getNewPlayer().getServerSettings().getSubChannelDiversionOpen()) {
				// 归因导量开启，并且没有分配过targetServerId需要额外计算最终目标服务器
				serverId = getServerBySubChannelDiversion(serverId, subChannel);
			}
			// 正常导量
			server = getServer(serverId, geoIpCountry, deviceId, country, bundleId, platform, ip, appVersion, channel);
			if (server == null) {
				json.put("ret", ApiErrorCode.GAME_SERVER_ID_ERROR);
				json.put("msg", "Please setting recommend server.");
				logger.error("未找到推荐注册服务器. token: {}, platform: {}", token, platform);
				return json.toJSONString();
			}
			logger.warn("after getServer serverId={}", server.getId());
			// 处理创建Account流程
			// 如果新手服导量开启.并且没有分配过targetServerId需要额外计算最终目标服务器
			if (configCenter.getLsConfig().isGuideServerOpen()) {
				targetServerId = getNewPlayerTargetServer(targetServerId, subChannel);
			}
			// 如果目标服不是新手服,需要计算是否触发自动新手服逻辑
			if (!server.isGuideServer()) {
				Server guildServer = serverService.getAutoGuideServer();
				if (guildServer != null) {
					if (configCenter.getLsConfig().getNewPlayer().getServerSettings().getAutoGuideServerOpen()) {
						logger.info("[AutoGuideServer] after getAutoGuideServer |oldServerId={}, oldTargetServerId={}, newServerId={}, newTargetServerId={}", server.getId(), targetServerId, guildServer.getId(), server.getId());
						targetServerId = Math.toIntExact(server.getId());
						server = guildServer;
						// 更新目标服注册人数
						ServerServiceImpl.setServerRegister(targetServerId, redisClient.incr(Constants.REDIS_KEY_TARGET_SERVER_USER_COUNT + targetServerId));
					} else {
						logger.debug("[AutoGuideServer] after getAutoGuideServer |oldServerId={} oldTargetServerId={} newServerId={} newTargetServerId={} [自动新手服未开启,仅计算结果用于分析算法]", server.getId(), targetServerId, guildServer.getId(), server.getId());
					}
				}
			}
			// 这里是不是应该加入事务? 改为只有非线上环境才允许游客登录注册
			if(platform == PlatformType.VISITOR.getId()){
				if(versionManager.getServerRunEnv().equals("online")){
					//线上环境不允许游客注册
					json.put("ret", -16);
					json.put("msg", "not allowed");
					return json.toJSONString();
				}
				account = createAccount(gaId);
				createAccountBind(platformUid, platform, account.getId());
				user = createNewUser(deviceId, platform, server.getId().intValue(), platformUid, account.getId(),targetServerId);
				logger.warn("after createNewUser mainRoleId={}, serverId={}", user.getRoleId(), user.getServerId());
				account.setMainRoleId(user.getRoleId());
				account.setLastDeviceId(deviceId);
				account.setIp(ip);
				Account finalAccount = account;
				virtualThreadService.execute(()->accountService.updateAccount(finalAccount));
			}else{
				var accId = Long.parseLong(id);
				account = new Account();
				account.setId(accId);
				account.setAdvertisingId(gaId);
				account.setCreateTime(new Timestamp(System.currentTimeMillis()));

				createAccountBind(platformUid, platform, account.getId());
				user = createNewUser(deviceId, platform, server.getId().intValue(), platformUid, account.getId(),targetServerId);
				logger.warn("after createNewUser mainRoleId={}, serverId={}", user.getRoleId(), user.getServerId());
				account.setMainRoleId(user.getRoleId());
				account.setLastDeviceId(deviceId);
				account.setIp(ip);
				accountService.saveAccountWithId(account);

				if (JavaUtils.bool(wxOpenId)) {
					virtualThreadService.execute(()->createWxAccount(accId, wxOpenId, wxUnionId == null ? "" : wxUnionId));
				}
			}


			register = true;
			isNewRole = true;

			// 渠道计数写入redis
			String redisKey = Place.findById(Integer.parseInt(intClientId)).getName();
			// 渠道类型 -> serverId -> 计数
			redisClient.hincrby(redisKey, String.valueOf(server.getId()),1);
			// 如果有目标服，也同时记录一下目标服渠道计数
			if (targetServerId > 0 && server.getId() != targetServerId) {
				redisClient.hincrby(redisKey, String.valueOf(targetServerId), 1);
			}
			// 记录注册buffer
			serverService.addRegist(user.getId(), user.getCreateTime().getTime());
			if(configCenter.isDevKvk()){
				//尝试获取kvk服
				server = createKVKServer(server);
			}
		}
		logger.info("[doLogin] getAccount info: account: {}, : {}, use time {}", loginBaseInfo, account, System.currentTimeMillis() - start);


        // 处理私域拉新奖励，只有新用户才会记录。老用户不管，防止被刷
        if (register) {
            specialNewUserServiceImpl.saveSiYuLinkNewUserInfo(user.getId().toString(), request);
        }

        // 做一些登录前检查检查
        if (!preLoginCheck(authContext, id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json)) {
            return json.toJSONString();
        }

		/*
		 * sean: 这个rollback貌似目前没啥用，前端也只是接收后通过CgLogin消息再发给Game服务器
		 * 存在LoginExtParam和RoleDevice对象中，并无实际作用. 由于game中有保存，所以暂时保留
		 */
		boolean rollback = false;
		// 新注册用户
		if (register) {
			// 2021-1-7 sean 这个rollback 没有实际意义，所以这部分处理逻辑去掉
			// UserHistory userHistory = userHistoryService.findByDeviceId(deviceId);
			// rollback = userHistory != null;
			// serverStatus = server.getServerStatus();
//			user.setLoginCnt(user.getLoginCnt() + 1);
//			virtualThreadService.execute(() -> userService.updateUser(user));
		} else {// 有用户
			if (checkUserBanStatus(json, user)) {
				return json.toJSONString();
			}
			// serverStatus = checkServerStatus(deviceId, ip, server);
			// 更新登陆时间
			user.setLastLoginTime(new Timestamp(System.currentTimeMillis()));
			user.setLoginCnt(user.getLoginCnt() + 1);
			virtualThreadService.execute(
					() -> {
						userService.updateUser(user);
						// 判断是否需要风险管控
						if (restrictedInfoService.hasIp(ip)) {
							restrictedUserService.addUserByIp(user.getRoleId(), ip);
						}
					}
			);
		}
		// 记录登录日志
		coreLogUtil.web_login(user.getRoleId(), user.getServerId(), register ? 1 : 0, user.getLoginCnt(), account.getAdvertisingId(), user.getTargetServerId(), user.getAccId(), intClientId, subChannel, deviceId, ip);

		json.put("accountId", account.getId());
		logger.info("[doLogin] updateLastLoginTime info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		int serverStatus = checkServerStatus(deviceId, ip, server, platformUid);
		logger.info("[doLogin] getServerStatus info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);

        // 如果用户的渠道Client被禁止登录，则让用户认为服务器是关闭状态，显示停服维护公告
        boolean channelShutDown = !preLoginCheckClientId(authContext, id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json);
        if (channelShutDown) {
            serverStatus = ServerStatusEnum.CLOSE.getId();
        }
		logger.info("[doLogin] pre login check :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);

		fillResultInfo(geoIpCountry, json, server, user, authContext, register, rollback, serverStatus);
		logger.info("[doLogin] fillResultInfo info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);

        // 如果用户所在的server正在维护，则在维护公告界面显示其他服务器信息，让他们能到其它服玩
        if (!channelShutDown && serverStatus == ServerStatusEnum.CLOSE.getId()) {
            JSONArray otherServerInfo = getOtherServerInfo(account, loginBaseInfo, deviceId, ip, platformUid);
            if (otherServerInfo != null && !otherServerInfo.isEmpty()) {
                json.put("otherServerInfo", otherServerInfo);
            }
            logger.info("[doLogin] current server closed. getOtherServerInfo info :: {}, use time {}",
                    loginBaseInfo, System.currentTimeMillis() - start);
        }

        if(register){
			var platforms = new ArrayList<String>();
			if(platform != PlatformType.VISITOR.getId()){
				platforms.add(String.valueOf(platform));
			}
			platforms.add(String.valueOf(PlatformType.VISITOR.getId()));
			json.put("bindPlatform", platforms);
		}else{
			fillBindPlatformInfo(user.getAccId(), json);
		}
		logger.info("[doLogin] bindPlatformInfo info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		String lang = fillBillboard(platform, request, json, server, register, serverStatus);
		logger.info("[doLogin] getLang info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		List<User> existUserList = register ? List.of(user) : userService.findByAccId(user.getAccId());
		// 填充存在角色的服务器ID列表
		if (CollectionUtils.isNotEmpty(existUserList)) {
			List<Long> serverIdList = new ArrayList<>();
			existUserList.forEach(u -> {
				if (!serverIdList.contains(u.getCurrentServerId())) {
					serverIdList.add(u.getCurrentServerId());
				}
			});
			json.put("existRoleServerIds", serverIdList);
		} else {
			json.put("existRoleServerIds", Collections.emptyList());
		}

		logger.info("[doLogin] handleMultiUser info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);

		checkAndUpdateAccount(account, deviceId, platform, bundleId, appVersion, country, channel, ip);
		logger.info("[doLogin] updateAccountLoginDevice info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		// 设置玩家登录验证token
		String loginToken = syncLoginTokenToRedis(user, ip, register,loginBaseInfo,start);
		json.put("loginToken", loginToken);
		json.put("mailAppid", versionManager.mailAppid);
		json.put("chatAppid", versionManager.chatAppid);
		json.put("mail_url", versionManager.getMailServerUrlClient());
		json.put("mail_cdn_url", versionManager.getMailCDNUri());
		json.put("chat_url", versionManager.getChatUrlClient());
		json.put("showRoleId", ShortKeyUtil.parseShortKey(user.getRoleId()));
		json.put("chat_app_secret", versionManager.getChatAppSecret());
		// 客户端设置的绑定信息 2024年5月2日 绑定流程：客户端->SDK->客户端->服务端，是否绑定根据客户端
		json.put("isSetBindInfo", account.isNeedSendBindMail());
		json.put("isNewRole", isNewRole);

        // 前端根据这个判断有没有清库，有没有换号，进而判断要不要清本地数据
        String serverRunEnv = versionManager.getServerRunEnv();
        String serverDataMark = "";
        if (!"online".equals(serverRunEnv) && !"oversea-online".equals(serverRunEnv)) {
            serverDataMark = serverRunEnv + ":";
        }
        serverDataMark += server.getId() + ":" + server.getOpenTime().getTime();
        json.put("serverDataMark", serverDataMark);

		long end = System.currentTimeMillis();
		logger.info("[doLogin] Login success. username: {}, platform: {}, lang: {}, sysLang: {}, deviceId: {}, use time: {}, ip:{}, geoIpCountry: {}, serverId: {}, isNewRole: {}",
                platformUid, platform, lang, request.getParameter("sysLang"), deviceId,
				(end - start), ip, geoIpCountry, server.getId(), isNewRole);
		return json.toJSONString();
	}

    /**
     * 填充用户能登录的服务器信息
     *
     * @param account
     * @param loginBaseInfo 登录基础信息, for log
     */
    private JSONArray getOtherServerInfo(Account account, String loginBaseInfo, String deviceId, String ip, String uuid) {
        List<User> users = userService.findByAccId(account.getId());
        JSONArray otherServerInfo = new JSONArray();

        boolean hasOpenServer = false;

        for (User user: users) {
            // user.serverId 是原服的
            Server origServer = serverService.getById(user.getServerId());
            Server kServer;
            // 原服不能登录，可能是进赛季了，因此看下赛季服是否能登录
            // 这2个判断分别代表：进程是否已经起来并且准备好接受玩家登录，和是否被gm设置为可登录
            int serverStatus = checkServerStatus(deviceId, ip, origServer, uuid);
            if (serverStatus != ServerStatusEnum.OPEN.getId()) {
                kServer = createKVKServer(origServer);
                serverStatus = checkServerStatus(deviceId, ip, kServer, uuid);
                if (serverStatus == ServerStatusEnum.OPEN.getId()) {
                    hasOpenServer = true;
                }
            } else {
                hasOpenServer = true;
            }

            JSONObject serverInfo = getJsonObject(user, origServer, serverStatus);
            otherServerInfo.add(serverInfo);
        }

        logger.info("[doLogin] current server closed. getOtherServerInfo info :: {}, otherServerInfo={}, hasOpenServer={}",
                loginBaseInfo, otherServerInfo, hasOpenServer);

        // 如果所有服都不能登录，就返回null，不让用户选择了。否则也只能是看看
        if (hasOpenServer) {
            return otherServerInfo;
        } else {
            return null;
        }
    }

    @NotNull
    private static JSONObject getJsonObject(User user, Server server, int hasOpenServer) {
        JSONObject serverInfo = new JSONObject();
        serverInfo.put("id", user.getRoleId());
        serverInfo.put("name", user.getRoleName());
        serverInfo.put("head", user.getRoleHead());
        serverInfo.put("level", user.getRoleMainCityLevel());
        String allianeAliasName = user.getRoleAllianceAliasName();
        if (allianeAliasName != null && !allianeAliasName.isEmpty()) {
            serverInfo.put("allianeAliasName", allianeAliasName);
        }
        serverInfo.put("power", user.getPower());
        serverInfo.put("serverId", server.getId());
        serverInfo.put("serverStatus", hasOpenServer);
        return serverInfo;
    }

    /**
	 * 根据归因分流选择服务器
	 *
	 * @param serverId
	 * @param subChannel
	 * @return
	 */
	private int getServerBySubChannelDiversion(int serverId, String subChannel) {
		if (serverId > 0) {
			// 通过分享等情况已经分配targetServerId, 则检查一下合法性,如果不合法则返回0
			Server targetServer = serverService.getById(serverId);
			if (targetServer != null) {
				return serverId;
			}
			logger.error("[归因导量] 目标服务器服务器未找到:" + serverId);
		}

		Map<Integer, Integer> serverLimit = configCenter.getLsConfig().getSubChannelDiversionServerId(subChannel);

		// 如果没有找到符合条件的服务器,则按照userLimit权重随机选择服务器
		if (serverLimit.isEmpty()) {
			logger.debug("[归因导量] 玩家子渠道未配置分流服务器, 跳过归因导量");
			return serverId;
		}

		// 准备权重数组和服务器ID数组
		int[] weights = new int[serverLimit.size()];
		int[] serverIds = new int[serverLimit.size()];
		int index = 0;

		long serverRegisterCount;

		// 排序按照serverId从小到大
		List<Map.Entry<Integer, Integer>> sortedEntries = new ArrayList<>(serverLimit.entrySet());
		sortedEntries.sort(Map.Entry.comparingByKey());

		for (Map.Entry<Integer, Integer> entry : sortedEntries) {
			int serverIdTmp = serverIds[index] = entry.getKey();
			int userLimit = weights[index] = entry.getValue();
			index++;

			serverRegisterCount = ServerServiceImpl.getServerSourceRegisterMap(redisClient, serverIdTmp, subChannel);
			if (serverRegisterCount < userLimit) {
				// 服务有缓存并且缓存服务器没有满, redis自增
				serverRegisterCount = ServerServiceImpl.incServerSourceRegisterMap(redisClient, serverIdTmp, subChannel);
				logger.debug("[归因导量] 服务器归因分流选择成功, serverId:{}, serverRegisterCount:{}, userLimit:{}", serverIdTmp, serverRegisterCount, userLimit);
				return serverIdTmp;
			} else {
				logger.debug("[归因导量] 服务器已满,查询下一个, serverId:{}, serverRegisterCount:{}, userLimit:{}", serverIdTmp, serverRegisterCount, userLimit);
			}
		}

		// 如果没有找到符合条件的服务器,则按照userLimit权重随机选择服务器
		serverId = RandomUtils.randWeight(weights, serverIds);
		serverRegisterCount = ServerServiceImpl.incServerSourceRegisterMap(redisClient, serverId, subChannel);
		logger.debug("[归因导量] 归因服务器全部已满,按权重随机分配到归因服务器, serverId:{}, serverRegisterCount:{}", serverId, serverRegisterCount);
		return serverId;
	}

	private boolean preLoginCheck(IAuthContext authContext, String id, String token, String deviceId, int platform, String gaId, String appVersion, String os, int serverId, HttpServletRequest request, JSONObject json) {
        return (preLoginCheckVistorLogin(id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json)
                        && preLoginCheckRegion(id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json)
        );
    }

    /**
     * 限制某个渠道登录，通过clientId限制
     * 使用场景：渠道多起来了，客户端登录需要提审，主渠道都过了审了而一些小渠道没有过审，那么就先上线，禁止这些小渠道登录
     */
    private boolean preLoginCheckClientId(IAuthContext authContext, String id, String token, String deviceId, int platform, String gaId, String appVersion,
                                          String os, int serverId, HttpServletRequest request, JSONObject json) {
		if(authContext.getExtData() == null){
			return true;
		}
        JSONObject tokenJson = authContext.getExtData().getJSONObject("token");
        if (tokenJson == null) {
            return true;
        }

        String ClientId = tokenJson.getString("clientId");
        if (StringUtils.isEmpty(ClientId)) {
            return true;
        }

        List<String> clientIds = configCenter.getLsConfig().getWebServer().getForbiddenClientIds();
        if (CollectionUtils.isEmpty(clientIds)) {
            return true;
        }

        if (clientIds.contains(ClientId)) {
//            json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
//            json.put("msg", "clientId check Failed.");
            logger.error("用户的clientId被禁止登录. id: {} gaId: {}  token: {}, platform: {}, clientId: {}, clientIds: {}",
                    id, gaId, token, platform, ClientId, clientIds);
            return false;
        }

        return true;
    }

    /**
     * 游客登录限制IP
     */
    private boolean preLoginCheckVistorLogin(String id, String token, String deviceId, int platform, String gaId, String appVersion, String os, int serverId, HttpServletRequest request, JSONObject json) {
        PlatformType type = PlatformType.findById(platform);
        if (type != PlatformType.VISITOR) {
            return true;
        }

        WebServerConfig webServer = configCenter.getLsConfig().getWebServer();
        List<String> ips = webServer.getVisitorIps();
        if (CollectionUtils.isEmpty(ips)) {
            return true;
        }

        String clientIp = HttpUtils.getIpAddr(request);
        if (ips.contains(clientIp)) {
            return true;
        }

        json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
        json.put("msg", "Platform & IP check Failed.");
        logger.error("Failed user authentication. token: {}, platform: {}, ip: {}", token, platform, clientIp);

        return false;
    }

	private boolean preLoginCheckRegionDomestic(String id, String token, String deviceId, int platform, String gaId, String appVersion, String os, int serverId, HttpServletRequest request, JSONObject json) {
		// 海外才检查
		String env = versionManager.getServerRunEnv();
		if (!"online".equals(env)) {
			return true;
		}
		// 白名单用户不检查
		if (serverService.isInLoginUuidWhiteList(id)) {
			return true;
		}
		// 国内不加黑名单处理
//		// 黑名单用户直接失败
//		if (deviceId != null && !deviceId.isEmpty() && redisClient.sismember(Constants.LOGIN_DEVICE_BLACKLIST, deviceId)) {
//			logger.error("preLoginCheckRegionDomestic login_device_blacklist [{}] Failed user authentication. " +
//					"token: {}, platform: {}, deviceId: {}", deviceId, token, platform, deviceId);
//			preLoginCheckDomesticRegionFail(id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json);
//			return false;
//		}
		
		String ip = HttpUtils.getIpAddr(request);
		// 白名单ip不检查
		if (serverService.isInLoginIpWhiteList(ip)) {
			// 白名单ip 将用户id加入到白名单中
			serverService.addLoginUuidWhiteList(id);
			return true;
		}
		WebServerConfig webServer = configCenter.getLsConfig().getWebServer();
		List<String> forbiddenCities = webServer.getForbiddenCities();
		// 其他检查城市是否在被禁用列表中
		String city = ip2RegionService.getCity(ip);
		if (forbiddenCities.contains(city)) {
			preLoginCheckDomesticRegionFail(id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json);
			logger.error("preLoginCheckRegionDomestic forbiddenCity [{}] Failed user authentication. " +
					"forbiddenCities: {}, city: {}, ip: {}", ip, forbiddenCities, city, ip);
			return false;
		}
		return true;
	}

	private boolean preLoginCheckRegionOversea(String id, String token, String deviceId, int platform, String gaId, String appVersion, String os, int serverId, HttpServletRequest request, JSONObject json) {
        // 海外才检查
        String env = versionManager.getServerRunEnv();
		if (!"oversea-online".equals(env)) {
            return true;
        }

        // 白名单用户不检查
        if (serverService.isInLoginUuidWhiteList(id)) {
            return true;
        }

        // 黑名单用户直接失败
        if (deviceId != null && !deviceId.isEmpty() && redisClient.sismember(Constants.LOGIN_DEVICE_BLACKLIST, deviceId)) {
			logger.error("preLoginCheckRegionOversea login_device_blacklist [{}] Failed user authentication. " +
					"token: {}, platform: {}, deviceId: {}", id, token, platform, deviceId);
            preLoginCheckRegionFail(id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json);
            return false;
        }

        // 检查系统语言
        String sysLang = request.getParameter("sysLang");
        if (sysLang == null) {
            return true;
        }

        WebServerConfig webServer = configCenter.getLsConfig().getWebServer();
        List<String> languages = webServer.getForbiddenLanguages();
        if (languages.contains(sysLang)) {
            if (deviceId != null && !deviceId.isEmpty()) {
                redisClient.sadd(Constants.LOGIN_DEVICE_BLACKLIST, deviceId);
            }
			logger.error("preLoginCheckRegionOversea login_language [{}] Failed user authentication. " +
					"languages: {}, sysLang: {}, deviceId: {}", id, languages, sysLang, deviceId);
            preLoginCheckRegionFail(id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json);
            return false;
        }

        // 检查ip
        String ip = HttpUtils.getIpAddr(request);
        String geoIpCountry = geoIpService.getCountryCode(ip);
        List<String> regions = webServer.getForbiddenRegions();

        if (regions.contains(geoIpCountry)) {
            if (deviceId != null && !deviceId.isEmpty()) {
                redisClient.sadd(Constants.LOGIN_DEVICE_BLACKLIST, deviceId);
            }
            preLoginCheckRegionFail(id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json);
			logger.error("preLoginCheckRegionOversea login_region [{}] Failed user authentication. " +
					"regions: {}, geoIpCountry: {}, deviceId: {}", id, regions, geoIpCountry, deviceId);
            return false;
        }
        return true;
	}


	/**
	 * 海外服务不让内登录
	 *
	 * @return
	 */
	private boolean preLoginCheckRegion(String id, String token, String deviceId, int platform, String gaId, String appVersion, String os, int serverId, HttpServletRequest request, JSONObject json) {
		String env = versionManager.getServerRunEnv();
		switch (env) {
			case "oversea-online":
				return preLoginCheckRegionOversea(id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json);
			case "online":
				return preLoginCheckRegionDomestic(id, token, deviceId, platform, gaId, appVersion, os, serverId, request, json);
			default:
				return true;
		}
    }

	void preLoginCheckDomesticRegionFail(String id, String token, String deviceId, int platform, String gaId, String appVersion, String os, int serverId, HttpServletRequest request, JSONObject json) {
		json.put("serverStatus", ServerStatusEnum.CLOSE.getId());
		json.put("title", "维护通知");
		json.put("content", "服务器正在维护中，请关注微信公众号“三国冰河时代”获取最新资讯。");
		json.put("timestamp", "");
		json.put("roleId", id);
        json.put("userId", id);

		json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
		json.put("msg", "服务器正在维护中。");

		logger.error("中国地区暂时不开放注册. id: {}, deviceId: {}, ip: {}, sysLang: {}, token: {}, platform: {}",
				id, deviceId, HttpUtils.getIpAddr(request), request.getParameter("sysLang"), token, platform);
	}

    void preLoginCheckRegionFail(String id, String token, String deviceId, int platform, String gaId, String appVersion, String os, int serverId, HttpServletRequest request, JSONObject json) {
        json.put("serverStatus", ServerStatusEnum.CLOSE.getId());
        json.put("title", "Server Maintenance");
        json.put("content", "Servers are currently under maintenance");
        json.put("timestamp", "");
        json.put("roleId", id);
        json.put("userId", id);

        json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
        json.put("msg", "Servers are currently under maintenance.");

        logger.error("中国地区暂时不开放注册. id: {}, deviceId: {}, ip: {}, sysLang: {}, token: {}, platform: {}",
                id, deviceId, HttpUtils.getIpAddr(request), request.getParameter("sysLang"), token, platform);
    }

    private Server createKVKServer(Server server) {
		if (server.getServerStatus() == ServerStatusEnum.CLOSE.getId()) {
			// KVK服的话game服的instance为null
			Map<Integer, GameServerConfig> gameServers = configCenter.getLsConfig().getGameServers();
			KvkInstance kvkInstance = gameServers.get(server.getId().intValue()).getKvkInstance();
			if (kvkInstance != null) {
				// 有对应的KVKinstance信息
				int kServerId = kvkInstance.getkServerId();
				Long id = server.getId();
				Server kServer = serverService.getById(kServerId);
				if (kServer == null) {
					logger.error("Server={}的k服={}信息null", id, kServerId);
				} else {
					return kServer;
				}
			} else {
			}
		}
		return server;
	}

	@Override
	protected String doSwitch(String token, int platform, String deviceId) {
		long start = System.currentTimeMillis();
		JSONObject json = new JSONObject();
		IAuthContext authContext = doAuth("", token, platform);
		if (!authContext.isSuccess()) {
			json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
			json.put("msg", "Failed user authentication.");
			logger.error("Failed user authentication. token: {}, platform: {}", token, platform);
			return json.toJSONString();
		}

		// 打点用信息
		String loginBaseInfo = new StringBuilder().append("token [").append(token).append("], platform [").append(platform).append("], deviceId [").append(deviceId).append("]")
				.toString();

		JSONArray ja = new JSONArray();
		JSONObject userAccountJA = new JSONObject();
		String platformId = authContext.getUserId();
		// 切换的平台没有账号信息
		AccountBind accountBind = accountBindService.findByPlatformIdAndPlatform(platformId, platform);
		if (accountBind == null) {
			logger.error("[switchAccount] AccountBind not found! authUserId[{}], platform[{}]", platformId, platform);
			userAccountJA.put("serverId", -1);
			userAccountJA.put("id", -1);
			ja.add(userAccountJA);
			json.put("ret", ApiErrorCode.ACCOUNT_NOT_FOUND);
			json.put("msg", "account bind not found.");
			json.put("accounts", ja.toJSONString());
			json.put("platform", platform);
			return json.toJSONString();
		}
		logger.info("[doSwitch] getAccountBindByPlatform info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		// 获取当前设备的绑定信息
		AccountBind deviceBind = accountBindService.findByPlatformIdAndPlatform(deviceId, PlatformType.VISITOR.getId());
		// 空说明这个设备id未绑过信息，前端给的参数有问题
		if (deviceBind == null) {
			userAccountJA.put("serverId", -1);
			userAccountJA.put("id", -1);
			ja.add(userAccountJA);
			json.put("ret", ApiErrorCode.ACCOUNT_NOT_FOUND);
			json.put("msg", "The platform is not bound.");
			json.put("accounts", ja.toJSONString());
			json.put("platform", platform);
			logger.error("[findUsersOnLogin] find current use device bind 'bindDeviceId'[{}] AccountBind is null", deviceId);
			return json.toJSONString();
		}
		logger.info("[doSwitch] getAccountBindByDevice info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		// 获取账号对象
		Account account = accountService.findAccountById(accountBind.getAccId());
		if (account == null) {
			logger.error("[switchAccount] Account not found! accId[{}]", accountBind.getAccId());
			userAccountJA = new JSONObject();
			userAccountJA.put("serverId", -1);
			userAccountJA.put("id", -1);
			ja.add(userAccountJA);
			json.put("ret", ApiErrorCode.ACCOUNT_NOT_FOUND);
			json.put("msg", "account not found.");
			json.put("accounts", ja.toJSONString());
			json.put("platform", platform);
			return json.toJSONString();
		}
		logger.info("[doSwitch] getAccount info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		// 获取账号对应的主角色
		User user = userService.findByRoleId(account.getMainRoleId());
		logger.info("[doSwitch] getUser info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		// 为空根据accId查所有角色，从列表中选择一个返回(已跟策划 罗迪 确认)
		if (user == null) {
			List<User> userList = userService.findByAccId(account.getId());
			if (CollectionUtils.isEmpty(userList)) {
				userAccountJA.put("serverId", -1);
				userAccountJA.put("id", -1);
				ja.add(userAccountJA);
				json.put("ret", ApiErrorCode.USER_ERROR);
				json.put("msg", "User not found.");
				json.put("accounts", ja.toJSONString());
				json.put("platform", platform);
				return json.toJSONString();
			} else {
				// 遍历非封号user
				for (User u : userList) {
					if (u.getLockStatus() != User.LOCK_STATUS_LOCKED) {
						user = u;
						break;
					}
				}
				if (user == null) {
					userAccountJA.put("serverId", -1);
					userAccountJA.put("id", -1);
					ja.add(userAccountJA);
					json.put("ret", ApiErrorCode.USER_ERROR);
					json.put("msg", "User not found.");
					json.put("accounts", ja.toJSONString());
					json.put("platform", platform);
					return json.toJSONString();
				}
			}
		}
		// 变更旧设备绑定关系，更新当前设备绑定的Account信息
		deviceBind.setAccId(account.getId());
		// deviceBind.setRoleId(user.getRoleId());
		accountBindService.updateAccountBind(deviceBind);

		logger.info("[doSwitch] updateAccountAccIe info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);

		Server server = serverService.getById(user.getCurrentServerId());
		if(server == null){
			logger.error("[doSwitch] user:{} get server:{} is null! please check!", user.getId(), user.getCurrentServerId());
			userAccountJA.put("serverId", user.getCurrentServerId());
			userAccountJA.put("id", user.getId());
			ja.add(userAccountJA);
			json.put("ret", ApiErrorCode.GAME_SERVER_ID_ERROR);
			json.put("msg", "Server not found.");
			json.put("accounts", ja.toJSONString());
			json.put("platform", platform);
			return json.toJSONString();
		}
		userAccountJA.put("serverId", server.getId());
		userAccountJA.put("serverName", server.getName());
		userAccountJA.put("serverStatus", server.getServerStatus());
		userAccountJA.put("serverHotState", server.getServerHotState());
		userAccountJA.put("id", user.getId());
		userAccountJA.put("roleId", user.getRoleId());
		userAccountJA.put("roleName", user.getRoleName());
		userAccountJA.put("roleHead", user.getRoleHead());
		userAccountJA.put("roleMainCityLevel", user.getRoleMainCityLevel());
		userAccountJA.put("roleAllianceName", user.getRoleAllianceName());
        userAccountJA.put("roleAllianceAliasName", user.getRoleAllianceAliasName());
		userAccountJA.put("deviceId", deviceId);
		ja.add(userAccountJA);
		json.put("ret", 0);
		json.put("msg", "find users success");
		json.put("accounts", ja.toJSONString());
		json.put("platform", platform);
		logger.info("[doSwitch] getServer info :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		return json.toJSONString();
	}

	/**
	 * 绑定流程
	 * 客户端拿userId去SDK服务端绑定，绑定成功后SDK服务端返回给客户端
	 * 客户端检查doLogin
	 *
	 * @param id
	 * @param token
	 * @param platform
	 * @return
	 */
	@Override
	protected String doNewBind(String id, String token, int platform) {
		// 先校验
		JSONObject json = new JSONObject();
		// 原平台认证
		IAuthContext authContext = doAuth(id, token, platform);
		if (!authContext.isSuccess()) {
			json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
			json.put("msg", "Failed user authentication.");
			logger.error("Failed original authentication. token: {}, platform: {}", token, platform);
			return json.toJSONString();
		}
		// 查找用户是否存在
		AccountBind oldBind = accountBindService.findByPlatformIdAndPlatform(authContext.getUserId(), platform);
		if (oldBind == null) {
			json.put("ret", ApiErrorCode.ACCOUNT_NOT_FOUND);
			json.put("msg", "account not found.");
			logger.error("original account bind not found. platformId: {}, platform: {}", authContext.getUserId(), platform);
			return json.toJSONString();
		}
		Account account = accountService.findAccountById(oldBind.getAccId());
		if (account == null) {
			json.put("ret", ApiErrorCode.USER_ERROR);
			json.put("msg", "Account not found.");
			logger.error("original Account not found. token: {}, platform: {}", token, platform);
			return json.toJSONString();
		}
		// 检查是否已经设置绑定
		if (account.isNeedSendBindMail()) {
			json.put("ret", ApiErrorCode.PLATFORM_ALREADY_BOUND);
			json.put("msg", "Platform already bound.");
			logger.error("platform already bound. userId: {}, token: {}, platform: {}", authContext.getUserId(), token, platform);
			return json.toJSONString();
		}
		// 设置发送邮件为true，之后所有新创建角色时候也发送
		account.setNeedSendBindMail(true);
		accountService.updateAccount(account);
		// 给账号下所有角色发送绑定奖励邮件（策划：罗迪）
		List<User> userList = userService.findByAccId(account.getId());
		if (CollectionUtils.isNotEmpty(userList)) {
			for (User user : userList) {
				if (user.isSendBindMail()) {
					continue;
				}
				emailEndpointService.sendFirstBindEmail(user);
			}
		}
		// 返回成功
		json.put("ret", 0);
		json.put("msg", "Platform bind success.");
		logger.info("platform bind success. userId: {}, platform: {}", authContext.getUserId(), platform);
		return json.toJSONString();
	}

	@Deprecated
	@Override
	protected String doBind(String oId, String oToken, int oPlatform, String nId, String nToken, int nPlatform) {
		JSONObject json = new JSONObject();
		// 原平台认证
		IAuthContext authContext = doAuth(oId, oToken, oPlatform);
		if (!authContext.isSuccess()) {
			json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
			json.put("msg", "Failed user authentication.");
			logger.error("Failed original authentication. token: {}, platform: {}", oToken, oPlatform);
			return json.toJSONString();
		}
		// 查找用户是否存在
		AccountBind oldBind = accountBindService.findByPlatformIdAndPlatform(authContext.getUserId(), oPlatform);
		if (oldBind == null) {
			json.put("ret", ApiErrorCode.ACCOUNT_NOT_FOUND);
			json.put("msg", "account not found.");
			logger.error("original account bind not found. platformId: {}, platform: {}", authContext.getUserId(), oPlatform);
			return json.toJSONString();
		}
		Account account = accountService.findAccountById(oldBind.getAccId());
		if (account == null) {
			json.put("ret", ApiErrorCode.USER_ERROR);
			json.put("msg", "Account not found.");
			logger.error("original Account not found. token: {}, platform: {}", oToken, oPlatform);
			return json.toJSONString();
		}
		// 新平台验证
		authContext = doAuth(nId, nToken, nPlatform);
		if (!authContext.isSuccess()) {
			json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
			json.put("msg", "Failed user authentication.");
			logger.error("Failed new authentication. token: {}, platform: {}", nToken, nPlatform);
			return json.toJSONString();
		}
//		String oldPlatformId = oldBind.getPlatformId();
//		String newPlatformId = authContext.getUserId();
		List<AccountBind> existBinds = accountBindService.findByAccId(account.getId());
		if (CollectionUtils.isNotEmpty(existBinds) && existBinds.stream().anyMatch(b -> b.getPlatform() == nPlatform)) {
			json.put("ret", ApiErrorCode.BIND_SAME_PLATFORM);
			json.put("msg", "user bound same platform.");
			fillBindPlatformInfo(account.getId(), json);
			logger.error("[doBind] Failed. User bind same platform. token: {}, platform: {}, platformId: {} ", nToken, nPlatform, authContext.getUserId());
			return json.toJSONString();
		} else {
			// 已有绑定里没未找到新平台绑定信息，可以继续
			logger.warn("[doBind] user[{}] not bind platform[{}] account ", account.getMainRoleId(), nPlatform);
		}
		// 如果已经有绑定的账号
		AccountBind accountBind = accountBindService.findByPlatformIdAndPlatform(authContext.getUserId(), nPlatform);
		if (accountBind != null) {
			// 绑定不同账号返回错误
			if (accountBind.getAccId() != account.getId()) {
				json.put("ret", ApiErrorCode.PLATFORM_ALREADY_BOUND);
				json.put("msg", "The platform is already bound.");
				logger.error("Failed do bind .The platform is already bound. token: {}, platform: {}, platformId: {} ", nToken, nPlatform, authContext.getUserId());
				logger.error("Failed do bind .The platform is already bound. account bind info platformId: {}, platform: {}, roleId: {} ", accountBind.getPlatformId(),
						accountBind.getPlatform(), account.getMainRoleId());
			}
			// 前端说绑定自己算成功
			else {
				handleBindSuccess(authContext, account, nPlatform, json, account.getMainRoleId());
			}
			return json.toJSONString();
		}
		accountBind = accountBindService.createAccountBind(authContext.getUserId(), nPlatform, account.getId());
		if (accountBind == null) {
			json.put("ret", ApiErrorCode.ACCOUNT_CREATE_FAILED);
			json.put("msg", "account bind create failed.");
			logger.error("Failed do bind . create account bind error . platformId: {}, platform: {}", authContext.getUserId(), nPlatform);
			return json.toJSONString();
		}

		// 验证通过，替换 username， platform
		handleBindSuccess(authContext, account, nPlatform, json, account.getMainRoleId());
		// 发送邮件
		if (!account.isNeedSendBindMail()) {
			// 设置发送邮件为true，之后所有新创建角色时候也发送
			account.setNeedSendBindMail(true);
			accountService.updateAccount(account);
			// 给账号下所有角色发送绑定奖励邮件（策划：罗迪）
			List<User> userList = userService.findByAccId(account.getId());
			if (CollectionUtils.isNotEmpty(userList)) {
				for (User user : userList) {
					if (user.isSendBindMail()) {
						continue;
					}
					emailEndpointService.sendFirstBindEmail(user);
				}
			}

		}
		return json.toJSONString();
	}


	@Override
	protected String doUnbind(String token, int platform, long roleId, String deviceId) {
		JSONObject json = new JSONObject();
		// 不能设备解绑
		if (platform == PlatformType.VISITOR.getId()) {
			json.put("ret", ApiErrorCode.UNBIND_ERROR);
			json.put("msg", "Failed user unbind.");
			logger.error("Unbind failed can not unbind VISITOR platform. old otoken: {}, platform: {}", token, platform);
			return json.toJSONString();
		}
		// 原平台认证
		IAuthContext authContext = doAuth("", token, platform);
		if (!authContext.isSuccess()) {
			json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
			json.put("msg", "Failed user authentication.");
			logger.error("Unbind failed original authentication. old otoken: {}, platform: {}", token, platform);
			return json.toJSONString();
		}
		// 查找用户是否存在
		AccountBind oldBind = accountBindService.findByPlatformIdAndPlatform(authContext.getUserId(), platform);
		if (oldBind == null) {
			json.put("ret", ApiErrorCode.USER_ERROR);
			json.put("msg", "user not found.");
			logger.error("Unbind original AccountBind not found. platformId: {}, platform: {}", authContext.getUserId(), platform);
			return json.toJSONString();
		}
		User user = userService.findByRoleId(roleId);
		if (user == null) {
			json.put("ret", ApiErrorCode.USER_ERROR);
			json.put("msg", "user not found.");
			logger.error("original user not found. token: {}, platform: {}", token, platform);
			return json.toJSONString();
		}
		// 绑定与玩家存的account不一致
		if (oldBind.getAccId() != user.getAccId()) {
			json.put("ret", ApiErrorCode.UNBIND_USER_NOT_SAME);
			json.put("msg", "unbind wrong account");
			logger.error("Unbind account id: {} not same send role: {} accId: {} platformId: {}, platform: {}", oldBind.getAccId(), roleId, user.getAccId(),
					authContext.getUserId(), platform);
			return json.toJSONString();
		}
		Account account = accountService.findAccountById(oldBind.getAccId());
		if (account == null) {
			json.put("ret", ApiErrorCode.ACCOUNT_NOT_FOUND);
			json.put("msg", "account not found.");
			logger.error("Unbind original Account not found. platformId: {}, platform: {}", authContext.getUserId(), platform);
			return json.toJSONString();
		}
		// 新平台验证
		authContext = doAuth("", deviceId, PlatformType.VISITOR.getId());
		if (!authContext.isSuccess()) {
			json.put("ret", ApiErrorCode.FAILED_AUTHENTICATION);
			json.put("msg", "Failed user authentication.");
			logger.error("Failed new authentication. token: {}, platform: {}", deviceId, PlatformType.VISITOR.getId());
			return json.toJSONString();
		}

		AccountBind currentBind = accountBindService.findByPlatformIdAndPlatform(authContext.getUserId(), PlatformType.VISITOR.getId());
		// 如果当前解绑设备为空，说明登录或切账号有问题
		if (currentBind == null) {
			json.put("ret", ApiErrorCode.UNBIND_ERROR);
			json.put("msg", "Failed current device AccountBind is null.");
			logger.error("Failed get current AccountBind is null. device: {}", deviceId);
			return json.toJSONString();
		} else {
			// 如果当前设备绑定的账号id，与已有账号绑定不一致则更新账号id到当前设备的绑定对象上
			if (currentBind.getAccId() != account.getId()) {
				logger.warn("Unbind account_bind accId[{}] and unbind user[{}] accId [{}] is not same!", currentBind.getAccId(), user.getRoleId(), user.getAccId());
				currentBind.setAccId(account.getId());
				accountBindService.updateAccountBind(currentBind);
			}
		}
		// 移除旧的平台绑定信息
		accountBindService.deleteAccountBind(oldBind.getId());
		// 2020-10-22 获取所有绑定信息,过滤出有效对象,将Account的platform设置成新的平台Id (by 罗迪)
		List<AccountBind> bindList = accountBindService.findByAccId(account.getId());
		if (CollectionUtils.isNotEmpty(bindList)) {
			Optional<AccountBind> abOp = bindList.stream().filter(ab -> ab.getPlatform() != PlatformType.DEV.getId() && ab.getPlatform() != PlatformType.GMFREE.getId()
					&& ab.getPlatform() != PlatformType.UNKNOW.getId() && ab.getPlatform() != PlatformType.VISITOR.getId()).findFirst();
			if (abOp.isPresent()) {
				account.setCurrPlatform(abOp.get().getPlatform());
				accountService.saveAccount(account);
			}
		}
		json.put("ret", 0);
		json.put("token", authContext.getUserToken());
		json.put("platform", authContext.getExtData());
		fillBindPlatformInfo(user.getAccId(), json);
		logger.error("unbind success  ret:{}", json.toJSONString());
		return json.toJSONString();
	}

	@Override
	protected boolean isCheckAuditServer() {
		return true;
	}

    /**
     * 从 roleId 获取 userId
     * @param roleId
     * @return
     */
    @Override
    public String doFindUserIdByRoleId(Long roleId) {
        JSONObject json = new JSONObject();
        json.put("ret", 0);

        User user = userService.findByRoleId(roleId);
        if (user == null) {
            json.put("ret", ApiErrorCode.ROLE_NOT_FOUND);
            json.put("msg", "role not found.");
            return json.toJSONString();
        }

        Account account = accountService.findAccountById(user.getAccId());

        if (account == null) {
            logger.error("roleId[{}] get Account[{}] is null! please check", roleId, user.getAccId());
            json.put("ret", ApiErrorCode.ACCOUNT_NOT_FOUND);
            json.put("msg", "account not found.");
            return json.toJSONString();
        }

        List<AccountBind> accountBinds = accountBindService.findByAccId(account.getId());
        if (accountBinds == null || accountBinds.isEmpty()) {
            json.put("ret", ApiErrorCode.ACCOUNT_PLATFORM_BIND_NOT_FOUND);
            json.put("msg", "account platform find not found.");
            return json.toJSONString();
        }

        if (accountBinds.size() > 1) {
            logger.warn("roleId[{}] accountId: {} get Account[{}] is multiple! please check", roleId, account.getId(), accountBinds.size());
//            json.put("ret", ApiErrorCode.MULTIPLE_PLATFORM_ACCOUNT);
//            json.put("msg", "multiple platform account.");
//            return json.toJSONString();
        }

        AccountBind accountBind = accountBinds.get(0);
        String platformUid = accountBind.getPlatformId();

        json.put("userId", platformUid);

        return json.toJSONString();
    }

    /**
     * 从 userId 获取 role 列表
     * @param platformUid
     * @return
     */
    @Override
    public String doFindRolesByUserId(String platformUid) {
        JSONObject json = new JSONObject();
        json.put("roles", new JSONArray());
        json.put("ret", 0);

        List<AccountBind> accountBinds = accountBindService.findByPlatformId(platformUid);
        if (accountBinds == null || accountBinds.isEmpty()) {
            json.put("ret", ApiErrorCode.ACCOUNT_PLATFORM_BIND_NOT_FOUND);
            json.put("msg", "account platform find not found.");
            return json.toJSONString();
        }

        if (accountBinds.size() > 1) {
            logger.warn("platformUid[{}] get Account[{}] is multiple! please check", platformUid, accountBinds.size());
//            json.put("ret", ApiErrorCode.MULTIPLE_PLATFORM_ACCOUNT);
//            json.put("msg", "multiple platform account.");
//            return json.toJSONString();
        }

        AccountBind accountBind = accountBinds.get(0);
        Account account = accountService.findAccountById(accountBind.getAccId());

        if (account == null) {
            logger.error("platformUid[{}] get Account[{}] is null! please check", platformUid, accountBind.getAccId());
            json.put("ret", ApiErrorCode.ACCOUNT_NOT_FOUND);
            json.put("msg", "account not found.");
            return json.toJSONString();
        }

        long mainRoleId = account.getMainRoleId();
        JSONArray roles = json.getJSONArray("roles");

        List<User> users = userService.findByAccId(account.getId());
        List<User> sortedUsers = new ArrayList<>();  // main user 在最前面

        for (User u : users) {
            if (u.getRoleId() == mainRoleId) {
                sortedUsers.add(0, u);
            } else {
                sortedUsers.add(u);
            }
        }

        for (User u : sortedUsers) {
            JSONObject jsonRole = new JSONObject();
            jsonRole.put("serverId", u.getCurrentServerId());
            jsonRole.put("roleId", u.getRoleId());
            jsonRole.put("name", u.getRoleName());
            roles.add(jsonRole);
        }

        json.put("roles", roles);

        return json.toJSONString();
    }

	private void fillBindPlatformInfo(long accId, JSONObject json) {
		List<String> bindPlatformList = accountBindService.findAllPlatformByAccId(accId);
		if (CollectionUtils.isNotEmpty(bindPlatformList)) {
			json.put("bindPlatform", bindPlatformList);
		}
	}

	/**
	 * 公告牌信息
	 *
	 * @param platform
	 * @param request
	 * @param json
	 * @param server
	 * @param register
	 * @param serverStatus
	 * @return
	 */
	private String fillBillboard(int platform, HttpServletRequest request, JSONObject json, Server server, boolean register, int serverStatus) {
		// ------------加入进入游戏公告牌信息---------------
		String languageName = request.getParameter("lang"); // 客户端传过来的是国家名称。
		String lang = CacheManager.getInstance().getLanguagebyName(languageName);

		if (lang == null) {
			logger.error("Can not find country name:{}, use {}", languageName, "English");
			lang = CacheManager.getInstance().getLanguagebyName("English");
		}

		if (serverStatus == ServerStatusEnum.CLOSE.getId() || !server.isVisible()) {
			Map<String, String> map = billboardService.getContent(register ? 1 : 0, Long.toString(server.getId()), Integer.toString(platform), lang, 1);
			if (map != null) {
				json.put("billboard", 1);
				json.put("key", map.get("key"));
				json.put("title", map.get("title"));
				json.put("content", map.get("content"));
				json.put("billboardTimestamp", Long.parseLong(map.get("expTime")));
			} else {
				json.put("billboard", 0);
			}
		} else {
			Map<String, String> map = billboardService.getContent(register ? 1 : 0, Long.toString(server.getId()), Integer.toString(platform), lang, 0);
			if (map != null) {
				json.put("billboard", 1);
				json.put("key", map.get("key"));
				json.put("title", map.get("title"));
				json.put("content", map.get("content"));
				json.put("billboardTimestamp", Long.parseLong(map.get("expTime")));
			} else {
				json.put("billboard", 0);
			}
		}
		return lang;
	}

	/**
	 * 处理绑定成功
	 * 
	 * @param authContext
	 * @param json
	 */
	private void handleBindSuccess(IAuthContext authContext, Account account, int nPlatform, JSONObject json, long roleId) {
		// 验证通过，替换 username， platform
		account.setCurrPlatform(nPlatform);
		accountService.updateAccount(account);
		json.put("ret", 0);
		json.put("token", authContext.getUserToken());
		json.put("platform", authContext.getExtData());
		fillBindPlatformInfo(account.getId(), json);
		logger.info("bind success  ret:{}", json.toJSONString());
	}

	/**
	 * 同步登录验证token到redis
	 * 
	 * @param user
	 * @return token
	 */
	private String syncLoginTokenToRedis(User user, String ip, boolean register,String loginBaseInfo,long start) {
		String roleIdToken = Utils.createMD5String(String.valueOf(user.getRoleId()));
		String accIdToken = Utils.createMD5String(String.valueOf(user.getAccId()));
		String timeToken = Utils.createMD5String(String.valueOf(System.currentTimeMillis()));
		String loginToken = Utils.createMD5String(roleIdToken + accIdToken + timeToken);
		String key = Constants.LOGIN_VERIFICATION_TOKEN + loginToken;
		String ipCity = ip2RegionService.getCity(ip).replace("_", "").strip();
        if (ipCity.isEmpty()) {
            ipCity = "未知";
        }
		int expiredSec = 5 * 60;
		if (register) {
			expiredSec = 15 * 60;
		}
		logger.info("[doLogin] after ip2region :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		//参数为 角色id 账号id 所在DB 当前服
		redisClient.setex(key, expiredSec, user.getRoleId() + "_" + user.getAccId() + "_" + user.getServerId() + "_" + ip + "_" + user.getCurrentServerId() + "_" + ipCity+ "_" + user.getTargetServerId());
		logger.info("[doLogin] after update redis token :: {}, use time {}", loginBaseInfo, System.currentTimeMillis() - start);
		return loginToken;
	}

	/**
	 * 新手服开启时,计算目标服务器Id
	 */
	private int getNewPlayerTargetServer(int targetServerId, String subChannel) {
		if (targetServerId > 0) {
			// 通过分享等情况已经分配targetServerId, 则检查一下合法性,如果不合法则返回0
			Server targetServer = serverService.getById(targetServerId);
			if (targetServer != null){
				return targetServerId;
			}
			logger.error("目标服务器服务器未找到:" + targetServerId);
		}

		// 先判断是否启用归因导量
		if (configCenter.getLsConfig().getNewPlayer().getServerSettings().getSubChannelDiversionOpen()) {
			// 归因导量开启，并且没有分配过targetServerId需要额外计算最终目标服务器
			targetServerId = getServerBySubChannelDiversion(targetServerId, subChannel);
			if (targetServerId > 0) {
				return targetServerId;
			}
		}

		// 将GuideServerInfos按照;和_分割成数组
		int userLimit = configCenter.getLsConfig().getNewPlayer().getServerSettings().getGuideServerUserLimit();
		String[] serverInfos = configCenter.getLsConfig().getNewPlayer().getServerSettings().getGuideServerInfos().split(",");
		int currentServerLimit;
		long serverRegisterCount;
		for (String serverInfoStr : serverInfos) {
			// 在按照_分割成数组
			String[] serverInfoArr = serverInfoStr.split("_");
			targetServerId = Integer.parseInt(serverInfoArr[0]);
			// 如果有第二位.则用第二位.如果没有则用userLimit
			currentServerLimit = userLimit;
			if (serverInfoArr.length > 1) {
				currentServerLimit = Integer.parseInt(serverInfoArr[1]);
			}

			// 缓存数量,此数值只用于缓存数量达到导量配置时使用.不是准确注册数
			serverRegisterCount = ServerServiceImpl.getServerRegister(targetServerId);
			if (serverRegisterCount == -1) {
				// 如果没有缓存,则从redis中获取
				serverRegisterCount = redisClient.incr(Constants.REDIS_KEY_TARGET_SERVER_USER_COUNT + targetServerId);
				ServerServiceImpl.setServerRegister(targetServerId, serverRegisterCount);
				// 判断如果没有满直接返回服务器Id
				if (serverRegisterCount < currentServerLimit){
					return targetServerId;
				}
			} else if (serverRegisterCount < currentServerLimit){
				// 服务有缓存并且缓存服务器没有满, redis自增
				serverRegisterCount = redisClient.incr(Constants.REDIS_KEY_TARGET_SERVER_USER_COUNT + targetServerId);
				ServerServiceImpl.setServerRegister(targetServerId, serverRegisterCount);
				return targetServerId;
			}
		}

		// 如果没有找到符合条件的服务器,则随机选择任意一个服务器,返回Id
		logger.warn("[getNewPlayerTargetServer] 目标服务器已满员,请联系管理员添加新的区服或提升单服人数限制,当前随机选择服务器");
		// 随机索引
		int randomIndex = new Random().nextInt(serverInfos.length);
		targetServerId = Integer.parseInt(serverInfos[randomIndex].split("_")[0]);

		serverRegisterCount = redisClient.incr(Constants.REDIS_KEY_TARGET_SERVER_USER_COUNT + targetServerId);
		ServerServiceImpl.setServerRegister(targetServerId, serverRegisterCount);
		// 获取目标服务器Id
		return targetServerId;
    }

	/**
	 * 更新 account信息
	 * 
	 * @param account
	 * @param deviceId
	 * @param platform
	 * @param bundleId
	 * @param appVersion
	 * @param country
	 * @param channel
	 */
	private void checkAndUpdateAccount(Account account, String deviceId, int platform, String bundleId, String appVersion, String country, String channel, String ip) {
		// 设置登录时的设备id
		if (StringUtils.isBlank(account.getLastDeviceId()) || !account.getLastDeviceId().equals(deviceId)) {
			account.setLastDeviceId(deviceId);
		}
		// 设置当前平台id
		account.setCurrPlatform(platform);

		if (StringUtils.isBlank(account.getAppKey()) || !account.getAppKey().equals(bundleId)) {
			account.setAppKey(bundleId);
		}
		if (StringUtils.isBlank(account.getAppVersion()) || !account.getAppVersion().equals(appVersion)) {
			account.setAppVersion(appVersion);
		}
		if (StringUtils.isBlank(account.getCountry()) || !account.getAppKey().equals(country)) {
			account.setCountry(country);
		}
		if (StringUtils.isBlank(account.getChannel()) || !account.getChannel().equals(channel)) {
			account.setChannel(channel);
		}
        if (StringUtils.isBlank(account.getIp()) || !account.getIp().equals(ip)) {
            account.setIp(ip);
        }
		virtualThreadService.execute(()->accountService.updateAccount(account));
	}

    private User checkOtherUser(long roleId, Account account, HttpServletRequest request, String platformUid) {
        String otherRoleIdStr = request.getParameter("roleId");
        User user;

        if (JavaUtils.bool(otherRoleIdStr)) {
            // 切换角色
            long otherRoleId = Long.parseLong(otherRoleIdStr);
            if (roleId == otherRoleId) {
                // 用户登录时指定的角色就是自己的主角色，不用切换
                user = userService.findByRoleId(roleId);
                logger.info("用户 {} 登录时指定了roleId {} 参数, 但是指定的角色 {} 就是自己的主角色, 不用切换. user_is_null: {}", platformUid, roleId, roleId, user==null);
            } else {
                logger.info("用户 {} 登录时指定了roleId {} 参数, 尝试用指定的角色登录", platformUid, otherRoleId);

                // 检查角色是否属于当前账号，防止用户通过roleId参数获取到其他账号的角色
                User otherUser = userService.findByRoleId(otherRoleId);
                if (otherUser == null) {
                    user = null;
                    logger.error("用户 {} 登录时指定了roleId {} 参数, 但是指定的角色 {} 不存在, user=null!", platformUid, roleId, otherRoleId);
                } else {
                    if (otherUser.getAccId() != account.getId()) {
                        user = null;
                        logger.error("用户 {} 登录时指定了roleId {} 参数, 但是指定的角色 属于账号{}, 不属于当前账号 {}, user=null!", platformUid, roleId, otherUser.getAccId(), account.getId());
//                    return json.toJSONString();
                    } else {
                        // 切换角色成功
                        account.setMainRoleId(otherRoleId);
                        accountService.updateAccount(account);
                        user = otherUser;
                        logger.info("用户 {} 登录时指定了roleId {} 参数, 切换角色成功", platformUid, otherRoleId);
                    }
                }
            }
        } else {
            user = userService.findByRoleId(roleId);
            logger.info("用户 {} 登录时未指定roleId 参数, 尝试用mainRoleId {} 角色登录, user_is_null?: {}", platformUid, roleId, user==null);
        }

        return user;
    }
}
