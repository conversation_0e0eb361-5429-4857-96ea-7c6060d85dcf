package com.lc.billion.icefire.web.bus.api.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.lc.billion.icefire.core.ApiConstants;
import com.lc.billion.icefire.core.common.JsonUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.web.bus.api.account.service.impl.AccountServiceImpl;
import com.lc.billion.icefire.web.bus.api.service.impl.OrderServiceImpl;
import com.lc.billion.icefire.web.bus.gm.service.IEmailEndpointService;
import com.lc.billion.icefire.web.bus.gm.service.impl.WebRPCService;
import com.lc.billion.icefire.web.bus.user.entity.User;
import com.lc.billion.icefire.web.bus.user.service.impl.UserServiceImpl;
import com.lc.billion.icefire.web.bus.wechat.entity.WechatOrder;
import com.lc.billion.icefire.web.mapper.WechatAccountMapper;
import com.lc.billion.icefire.web.mapper.WechatOrderMapper;
import com.lc.billion.icefire.web.utils.wx.AesException;
import com.lc.billion.icefire.web.utils.wx.WXBizMsgCrypt;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = {ApiConstants.WARZ_BASE, ApiConstants.LEGACY_SANGUO2})
public class WxPayController {
    @Autowired
    private OrderServiceImpl orderService;
    @Autowired
    private WechatAccountMapper wechatAccountMapper;
    @Autowired
    private AccountServiceImpl accountService;
    @Autowired
    private UserServiceImpl userService;
    @Autowired
    private WechatOrderMapper wechatOrderMapper;
    @Autowired
    private IEmailEndpointService emailEndpointService;
    @Autowired
    private WebRPCService rpcService;

    private String AESKey = "KQr5B2pfDkMyMveEx8UUFSNN9xBZLvVarr2XLGro8Dk";
    private String APPID = "wx209b8a6bc1c08b85";
    private String TOKEN = "tuyoogame";
    private WXBizMsgCrypt wxBizMsgCrypt;
    public static final int ID_NUM_SIZE_MAX = 20;

    private Logger LOG = LoggerFactory.getLogger(getClass());

    @PostConstruct
    public void init() throws AesException {
        wxBizMsgCrypt = new WXBizMsgCrypt(TOKEN, AESKey, APPID);
    }

    /**
     * 微信游戏圈发奖接口
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/yxqpay", produces = "text/plain; charset=UTF-8")
    public @ResponseBody String wxyxqpay(String nonce, String timestamp, String msg_signature, HttpServletRequest request) {
        String body = getRequestBody(request);
        String encrypt = null;
        JsonNode data = null;
        String orderId = null;
        Map<String, String> decryptMap = null;
        try {
            LOG.info("body={}", body);
            var bodyData = JsonUtils.readTree(body);
            encrypt = bodyData.get("Encrypt").asText();
            data = bodyData.get("MiniGame");
            orderId = data.get("OrderId").asText();
            String xml = wxBizMsgCrypt.decryptMsg(msg_signature, timestamp, nonce, encrypt);
            decryptMap = extract(xml);
            if (!orderId.equals(decryptMap.get("OrderId"))) {
                LOG.error("yxqpaylog 解密后的数据不包含明文中的orderId  msg_signature " + msg_signature + "解密信息:" + decryptMap);
                return wxBizMsgCrypt.generate(1, "msg orderId verfy failed", 1);
            }
        } catch (Exception e) {
            LOG.error("yxqpaylog 解析游戏圈密文失败msg_signature" + msg_signature, e);
            return wxBizMsgCrypt.generate(1, "msg decrypt failed", 2);
        }
        String ToUserOpenid = data.get("ToUserOpenid").asText();
        String zoneId = data.get("Zone").asText();
        var giftTypeId = data.get("GiftTypeId").asText();
        long sendTime = data.get("SendTime").asLong();
//        var goodsList = (ArrayNode) data.get("GoodsList");
//        var goodsId = goodsNode.get("Id").asText();
        var goodsId = decryptMap.get("Id");
        var num = Integer.parseInt(decryptMap.get("Num"));
        StringBuffer items = new StringBuffer(goodsId + "|" + num);
        for (int i = 1; i < ID_NUM_SIZE_MAX; i++) {
            var nextId = decryptMap.get("Id" + i);
            var nextNum = decryptMap.get("Num" + i);
            if (nextId != null && nextNum != null) {
                items.append(";" + nextId + "|" + nextNum);
            }
        }
        var giftId = data.get("GiftId").asText();
        var wechatOrder = wechatOrderMapper.selectByOrderId(orderId);
        if (wechatOrder != null) {
            LOG.info("yxqpaylog orderId={} repeated ToUserOpenid={} zoneId={} goodsId={} Num={} giftTypeId={} sendTime={} giftId={}",
                    orderId, ToUserOpenid, zoneId, goodsId, num, giftTypeId, sendTime, giftId);
            return wxBizMsgCrypt.generate(0, "success", 0);
        }
        var weUser = wechatAccountMapper.selectByOpenId(ToUserOpenid);
        if (weUser == null) {
            LOG.info("yxqpaylog orderId={} cant find openid ToUserOpenid={} zoneId={} goodsId={} Num={} giftTypeId={} sendTime={} giftId={}",
                    orderId, ToUserOpenid, zoneId, goodsId, num, giftTypeId, sendTime, giftId);
            return wxBizMsgCrypt.generate(1, "cant find wechatuser", 3);
        }
        var account = accountService.findAccountById(weUser.getAccId());
        if (account == null) {
            LOG.info("yxqpaylog orderId={} cant find account ToUserOpenid={} zoneId={} goodsId={} Num={} giftTypeId={} sendTime={} giftId={}",
                    orderId, ToUserOpenid, zoneId, goodsId, num, giftTypeId, sendTime, giftId);
            return wxBizMsgCrypt.generate(1, "cant find account", 4);
        }
        User user = userService.findByRoleId(account.getMainRoleId());
        if (account.getMainRoleId() == 0 || user == null) {
            LOG.info("yxqpaylog orderId={} cant find mainRoleId ToUserOpenid={} zoneId={} goodsId={} Num={} giftTypeId={} sendTime={} giftId={} user is null ={}",
                    orderId, ToUserOpenid, zoneId, goodsId, num, giftTypeId, sendTime, giftId, user == null);
            return wxBizMsgCrypt.generate(1, "cant find account role", 5);
        }
        wechatOrder = new WechatOrder();
        wechatOrder.setOrderId(orderId);
        wechatOrder.setWxOpenId(ToUserOpenid);
        wechatOrder.setGoodsId(goodsId);
        wechatOrder.setGoodsCount(num);
        wechatOrder.setRoleId(user.getRoleId());
        wechatOrder.setServerId(user.getCurrentServerId().intValue());
        wechatOrder.setCreateTime(TimeUtil.getNow());
        wechatOrder.setOrderStatus(1);
        wechatOrder.setZoneId(user.getCurrentServerId());
        wechatOrder.setGiftId(!JavaUtils.bool(giftId) ? "" : giftId);
        wechatOrderMapper.insert(wechatOrder);

        try {
            rpcService.getProxy(user.getCurrentServerId()).yxqPay(user.getRoleId(), goodsId, num, giftTypeId == null ? 1 : Integer.parseInt(giftTypeId));
        } catch (Exception e) {
            LOG.error("yxqpaylog send rpc failed orderId=" + orderId + ",roleId=" + user.getRoleId() + ",items=" + items, e);
        }
        try {
            emailEndpointService.roleEmail(String.valueOf(user.getRoleId()), user.getCurrentServerId(), "游戏圈礼包奖励发放", "尊敬的指挥官，请查收您的游戏圈礼包奖励，感谢您的支持", items.toString(), null, "");
        } catch (Exception e) {
            LOG.error("yxqpaylog send Email failed orderId=" + orderId + ",roleId=" + user.getRoleId() + ",items=" + items, e);
        }
        LOG.info("orderId={} ToUserOpenid={} zoneId={} goodsId={} Num={} giftTypeId={} sendTime={} giftId={} roleId={}",
                orderId, ToUserOpenid, zoneId, goodsId, num, giftTypeId, sendTime, giftId, user.getRoleId());
        return wxBizMsgCrypt.generate(0, "success", 0);
    }


    private String getRequestBody(HttpServletRequest request) {
        StringBuilder body = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return body.toString();
    }

    public WXBizMsgCrypt getWxBizMsgCrypt() {
        return wxBizMsgCrypt;
    }

    public static void main(String args[]) throws AesException {
        String body = "{\"ToUserName\":\"gh_55d3428e6139\",\"FromUserName\":\"oSKbG61fAZH9xAh0fSznHlztIVk8\",\"CreateTime\":\"1717575819\",\"MsgType\":\"event\",\"Event\":\"minigame_deliver_goods\",\"SessionFrom\":\"\",\"MiniGame\":{\"OrderId\":\"ea116728-6b27-4939-a497-69c97eabe615\",\"IsPreview\":\"1\",\"ToUserOpenid\":\"oSKbG64jAOn0-crQBngO1Uq5d5TY\",\"Zone\":\"1001\",\"GiftTypeId\":\"8\",\"GiftId\":\"CBgAAoXb6-hwHJIPEo56pMpkF7JzwyBRAV_0Cdblk1b0kFTHJBZXNI9JPaOj5Edc7X9uSseu7ma3FPFI\",\"SendTime\":\"1717575819\",\"GoodsList\":[{\"Id\":\"19908002\",\"Num\":\"5\"},{\"Id\":\"1\",\"Num\":\"2000\"},{\"Id\":\"10104002\",\"Num\":\"5\"},{\"Id\":\"10202002\",\"Num\":\"2\"}]},\"appid\":\"\",\"trace_id\":\"\",\"version\":\"\",\"errcode\":\"\",\"errmsg\":\"\",\"result\":{\"suggest\":\"\",\"label\":\"\"},\"detail\":null,\"Encrypt\":\"l5UyFPZSYCAgZJj1Ggf3+MuDV5QK3tE6GswnakpljkyraUf/zjPN5xcL8nkIillqvN86t78qQESiXHRKpu9bhh6dnWNN5wYKBjf3Azrx1WzvBRg6qU7w2q5FYZUJiZBxvxEc1lS7XPzW1HsrB+zczjQXnjmv4/FJMLG/IZ6B9Wobrts1zDjeGDn9hqwkA5L0NAP1VDU1Kr+ZET7y+66KpReqDNNhONBNrkLdbmA7VMJBg6d4gHelIfB0p9AtnKtFof6hECJmK4d6TonPXPzfcyjkfUZQ/yCI40ejQjy4G/SBq+zGk0QgFs7DwuoYQdxX0EuaP08vkk0d1e27bB/yqPr9fncsMMhWWc3naUL07ZEFdGf6dweR9LHNxw3Q8UWCKiWuVPnG1yDobsJXQeDaM+ImoXmZ9Be4ITrlrqv4tfYq5eVW1h/Zzd1e9l7MsuKh6YB6I6ei3EWQc14anpXbdGdHiDCtFNtq0b+jf1phkhnV6+JGrGvaSqdj+8GbK92wLzqNl+zPgJun4AtwjeVPu8j+E8zpiNcZp0/2KdgPuulREjGkZinkMTyYHQKkemzd3uy8jt5ulwyYNZqiRHGBZssmxB5Ly7aGuFZEtRfs2ZhiNb1deryOcajKXVogbPXSVZ2cymHC2IELYujSb0zJ8GOhZhuG3QbJXHjixNQShiYJ4QFVcBOwL6SDBs9Su8i3Z2lPGTas10APkqf4O/dLlw1mRoYjJ7J6UfUzazs8tw+gL83XucoWPNwzIklaaIQTlYIHoBnhRt1BCVM9l50Qnk47sJdVjMwNRskTGUDjwurH/254mr9dNBQkAm2difghT87uyxyQ0QdvgLgijFV/19XIoRjVn8KbnitzbCqDo/vuRBgDoPRfi0qs1t7FP5ybFcCu0y4rctCqQ/w1JgOypy9E5S+kPfNqyNkK5EYY1YTqXKO09jPOz98Xybvqfz7iWN0nv+2235en1v87jYxCRTglYZKHt4omGz/4fE9qmnPEBLjhdbb1QnhmzwBawJwCLpRPmoERBMYzt5gIBNA4qeUrYMhLcFATdiRG+eZouqX7OGQ7xBuAkhkoKPE4iWfOt/XfQGhADHMCOkCMHTHGlcDg8S2lqz1bsznF+7kD1TjuL4HzRqDtLehAeRK+//SzM1JIJNUQhH6gvZ5yRwWJISR+TqbxQzfyBe8LNkRd0fcvw+5Ba9pXlGx4yA0CpHANF6uQjBWMj+oLxxf9vk6YVK0lEtWp4ruPe4Nt/2HV2RAyrnBwh8FDxGlCtyNGpkZl\"}";
//        String nonce, String timestamp, String msg_signature,
        System.out.println(body);
        WxPayController controller = new WxPayController();
        controller.init();
        var bodyData = JsonUtils.readTree(body);
        var encrypt = bodyData.get("Encrypt").asText();
        var data = bodyData.get("MiniGame");
        var orderId = data.get("OrderId").asText();
        String nonce = "138972325";
        String timestamp = "1717575819";
        String signature = "97e8d82ee23a2003723d2684dc3082c0467c82f1";
        String text = controller.getWxBizMsgCrypt().decryptMsg(signature, timestamp, nonce, encrypt);
        System.out.println(text);
        var map = controller.extract(text);
        var Id = map.get("Id");
        var Num = map.get("Num");
        StringBuffer sb = new StringBuffer(Id + "|" + Num);
        for (int i = 1; i < ID_NUM_SIZE_MAX; i++) {
            var idn = map.get("Id" + i);
            var numn = map.get("Num" + i);
            if (idn != null && numn != null) {
                sb.append(";" + idn + "|" + numn);
            }
        }
        System.out.println(sb);

//        System.out.println(msg);

    }
    public Map<String, String> extract(String xmltext) throws AesException     {
        Map<String, String> resultMap = new HashMap<>();
        try {
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
            dbf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            dbf.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
            dbf.setXIncludeAware(false);
            dbf.setExpandEntityReferences(false);
            DocumentBuilder db = dbf.newDocumentBuilder();
            StringReader sr = new StringReader(xmltext);
            InputSource is = new InputSource(sr);
            Document document = db.parse(is);

            Element root = document.getDocumentElement();
            List<String> params = List.of("OrderId", "ToUserOpenid", "Id", "Num", "CreateTime", "SendTime");
            for (var param : params) {
                var node0 = root.getElementsByTagName(param).item(0);
                resultMap.put(node0.getNodeName(), node0.getTextContent());
                for (int i = 1 ; i < 10; i++) {
                    var node = root.getElementsByTagName(param).item(i);
                    if (node == null) {
                        continue;
                    }
                    resultMap.put(node.getNodeName() + i, node.getTextContent());
                }
            }
        } catch (Exception e) {
            LOG.error("解析失败" + xmltext, e);
        }
        return resultMap;
    }

}
