package com.lc.billion.icefire.web.bus.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.ApiConstants;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Utils;
import com.lc.billion.icefire.game.biz.redis.RedisClient;
import com.lc.billion.icefire.web.bus.api.account.service.impl.AccountServiceImpl;
import com.lc.billion.icefire.web.bus.api.service.lock.DistributedLock;
import com.lc.billion.icefire.web.bus.api.service.lock.LockKeyUtils;
import com.lc.billion.icefire.web.bus.config.AlipayProductConfig;
import com.lc.billion.icefire.web.bus.gm.service.IEmailEndpointService;
import com.lc.billion.icefire.web.bus.gm.service.impl.WebRPCService;
import com.lc.billion.icefire.web.bus.user.entity.User;
import com.lc.billion.icefire.web.bus.user.service.impl.UserServiceImpl;
import com.lc.billion.icefire.web.bus.wechat.entity.WechatOrder;
import com.lc.billion.icefire.web.mapper.WechatAccountMapper;
import com.lc.billion.icefire.web.mapper.WechatOrderMapper;
import com.simfun.sgf.utils.JavaUtils;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.encoders.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping(value = {ApiConstants.WARZ_BASE, ApiConstants.LEGACY_SANGUO2})
public class AlipayGiftSendController {

    // 公钥
    private static final String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5aF09PjF8bINMOlv82MiUwAx1v8p+xdeVDzZeEP1P3n3B1wLDKcw8MFjCg+sHIftm2+mbps+EuPhwSmWd6qKsR3SgpK0Y+UXYgTryRA3HLqJCZtEn+Rrdk66aKf1VzMqs8/HU4ZP8rTG8PCtsjCfAqaLzKFrPICFD6UXEFBP9r5mViR/fhTVDsDM9H3fWjXVv20nZZler+ykRJQQU3/knRxKmzcqozNP5rD2VXfKp384Wy+Ul+ywVQVED/3y1/DNulHAiS7svfUv0svj7cp71zv7E5kW4eZx8b8TLsJ5YGik1fPWOCJTrKN0+ndjSbr7Ynn1xeJpMKwhpE7Sbd5q0QIDAQAB";
    private static final long REQUEST_EXP_TIME = 300000; // 5分钟，单位毫秒
    private static final String MAIL_SUBJECT = "三国玩乐豆奖励发放";
    private static final String MAIL_CONTENT = "您的三国玩乐豆奖励已发放，请注意查收，祝您游戏愉快！";

    private final AccountServiceImpl accountService;
    private final UserServiceImpl userService;
    private final WechatAccountMapper wechatAccountMapper;
    private final WechatOrderMapper wechatOrderMapper;
    private final IEmailEndpointService emailEndpointService;
    private final ConfigServiceImpl configService;
    private final RedisClient redisClient;
    private final WebRPCService rpcService;
    @Autowired
    public AlipayGiftSendController(AccountServiceImpl accountService, UserServiceImpl userService, WechatAccountMapper wechatAccountMapper, WechatOrderMapper wechatOrderMapper, WebRPCService rpcService,
                                    IEmailEndpointService emailEndpointService, ConfigServiceImpl configService, @Qualifier("redisClient2") RedisClient redisClient) {
        this.accountService = accountService;
        this.userService = userService;
        this.wechatAccountMapper = wechatAccountMapper;
        this.wechatOrderMapper = wechatOrderMapper;
        this.rpcService = rpcService;
        this.emailEndpointService = emailEndpointService;
        this.configService = configService;
        this.redisClient = redisClient;
    }

    @RequestMapping(value = "/alipayGiftSend", produces = "application/x-www-form-urlencoded; charset=UTF-8")
    public String alipayGiftSend(@RequestParam Map<String, String> params) {
        // 定义必需的参数列表
        String[] requiredParams = {"sign", "method", "charset", "version", "utc_timestamp", "sign_type", "gift_id", "gift_type_id", "open_id", "order_id"};
        // 检查必需的参数
        for (String param : requiredParams) {
            if (!params.containsKey(param) || params.get(param).isEmpty()) {
                log.error("Missing required parameter: {}", param);
                return returnFail("MISSING_REQUIRED_PARAMETER", "MISSING_REQUIRED_PARAMETER");
            }
        }
        String gift_type_id = params.get("gift_type_id");
        String gift_id = params.get("gift_id");
        String order_id = params.get("order_id");
        String open_id = params.get("open_id");
        long clientTimestamp = Long.parseLong(params.get("utc_timestamp"));
        //验证请求有效期
        if (!isTimestampValid(clientTimestamp)) {
            log.info("alipayGiftSend:invalid timestamp: {}", clientTimestamp);
            return returnFail("INVALID_TIMESTAMP", "INVALID_TIMESTAMP");
        }
        //验证签名
        boolean isPass = rsaCheckV1(params, PUBLIC_KEY, "utf-8");
        if (!isPass) {
            log.info("alipayGiftSend:check sign fail");
            return returnFail("CHECK_SIGN_FAIL", "CHECK_SIGN_FAIL");
        }
        //获取配置
        AlipayProductConfig.AlipayProductMeta alipayProductMeta = configService.getConfig(AlipayProductConfig.class).getByProductId(gift_id);
        if (alipayProductMeta == null) {
            log.info("alipayGiftSend:alipayProductMeta == null,gift_id:{}", gift_id);
            return returnFail("SYSTEM_META_IS_NULL", "SYSTEM_META_IS_NULL");
        }
        //查询历史订单
        var wechatOrder = wechatOrderMapper.selectByOrderId(order_id);
        if (wechatOrder != null) {
            log.info("alipayGiftSend:wechatOrder is exist.order_id:{}", order_id);
            return returnSuccess("SUCCESS");
        }
        //查询支付宝账号
        var weUser = wechatAccountMapper.selectByOpenId(open_id);
        if (weUser == null) {
            log.info("alipayGiftSend:weUser is null.open_id:{}", open_id);
            return returnSuccess("USER_UNREGISTERED");
        }
        //查询游戏账号
        var account = accountService.findAccountById(weUser.getAccId());
        if (account == null) {
            log.info("alipayGiftSend:account is null.open_id:{}", open_id);
            return returnFail("SYSTEM_ACCOUNT_IS_NULL", "SYSTEM_ACCOUNT_IS_NULL");
        }
        //查询指定角色
        User user = userService.findByRoleId(account.getMainRoleId());
        if (account.getMainRoleId() == 0 || user == null) {
            log.info("alipayGiftSend:account getMainRoleId == 0 or user == null.accountId:{},mainRoleId:{}", account.getId(), account.getMainRoleId());
            return returnFail("SYSTEM_USER_IS_NULL", "SYSTEM_USER_IS_NULL");
        }

        DistributedLock lock = new DistributedLock(redisClient, LockKeyUtils.getAlipayGiftSendKey(Utils.createMD5String(order_id)), 30);
        try {
            if (!lock.lock()) {
                log.warn("alipayGiftSend:Repeated requests:open_id:{}, order_id:{}, gift_id:{}", open_id, order_id, gift_id);
                return returnFail("REPEATED_REQUESTS", "REPEATED_REQUESTS");
            }
            //写入order表
            String[] parts = alipayProductMeta.getRewardValues();
            wechatOrder = new WechatOrder();
            wechatOrder.setOrderId(order_id);
            wechatOrder.setWxOpenId(open_id);
            wechatOrder.setGoodsId(parts[0]);
            wechatOrder.setGoodsCount(Long.parseLong(parts[1]));
            wechatOrder.setRoleId(user.getRoleId());
            wechatOrder.setServerId(user.getCurrentServerId().intValue());
            wechatOrder.setCreateTime(TimeUtil.getNow());
            wechatOrder.setOrderStatus(1);
            wechatOrder.setZoneId(user.getCurrentServerId());
            wechatOrder.setGiftId(!JavaUtils.bool(gift_id) ? "" : gift_id);
            wechatOrderMapper.insert(wechatOrder);
            
            //发送RPC
            try {
                rpcService.getProxy(user.getCurrentServerId()).alipayCircleSign(user.getRoleId(), gift_type_id == null ? 1 : Integer.parseInt(gift_type_id), gift_id);
            } catch (Exception e) {
                log.error("alipayGiftSend:send game rpc fail:open_id:{}, order_id:{}, gift_id:{},role_id:{},reward:{},e:{}", open_id, order_id, gift_id, user.getRoleId(), Arrays.toString(parts), e.getMessage());
            }

            //发送邮件
            isPass = sendEmailByUser(user, alipayProductMeta);
            if (!isPass) {
                log.warn("alipayGiftSend:send mail fail:open_id:{}, order_id:{}, gift_id:{},role_id:{},reward:{}", open_id, order_id, gift_id, user.getRoleId(), Arrays.toString(parts));
                return returnFail("SYSTEM_SEND_MAIL_FAIL", "SYSTEM_SEND_MAIL_FAIL");
            }
            log.info("alipayGiftSend:send mail success:open_id:{}, order_id:{}, gift_id:{},role_id:{},reward:{}", open_id, order_id, gift_id, user.getRoleId(), Arrays.toString(parts));
            return returnSuccess("SUCCESS");
        } catch (Exception e) {
            log.error("alipayGiftSend:Exception:open_id:{}, order_id:{}, gift_id:{},e:{}", open_id, order_id, gift_id, e.toString());
            return returnFail("SYSTEM_EXCEPTION", "SYSTEM_EXCEPTION");
        } finally {
            lock.unlock();
        }
    }


    // 成功响应
    public static String returnSuccess(String resultCode) {
        JSONObject result = new JSONObject();
        JSONObject response = new JSONObject();
        response.put("code", "10000");
        response.put("msg", "Success");
        response.put("result_code", resultCode);
        result.put("response", response);
        return result.toJSONString();
    }

    // 失败响应
    public static String returnFail(String subCode, String subMsg) {
        JSONObject result = new JSONObject();
        JSONObject response = new JSONObject();
        response.put("code", "40004");
        response.put("msg", "Business Failed");
        if (subCode != null && !subCode.isEmpty()) {
            response.put("sub_code", subCode);
        }
        if (subMsg != null && !subMsg.isEmpty()) {
            response.put("sub_msg", subMsg);
        }

        result.put("response", response);
        return result.toJSONString();
    }

    /***
     * 发送邮件给指定的玩家
     * @param user 玩家
     * @return 成功返回true，否则返回false。
     */
    private boolean sendEmailByUser(User user, AlipayProductConfig.AlipayProductMeta alipayProductMeta) {
        if (user == null) {
            return false;
        }
        String rewardItems = alipayProductMeta.getReward();
        String mailTitle = alipayProductMeta.getMailTitle();
        String mailDesc = alipayProductMeta.getMailDesc();
        try {
            var rs = emailEndpointService.roleEmail(String.valueOf(user.getRoleId()), user.getCurrentServerId(), mailTitle, mailDesc, rewardItems, null, "");
            log.info("alipayGiftSend: roleEmail return str:{}", rs);
        } catch (Exception e) {
            log.error("alipayGiftSend: send Email failed roleId={},items={}", user.getRoleId(), rewardItems, e);
            throw new RuntimeException("奖励邮件发送失败");
        }
        return true;
    }


    /**
     * 判断指定时间戳是否超时
     *
     * @param timestamp 指定时间戳
     * @return 未超时责返回true, 超时责返回false
     */
    private static boolean isTimestampValid(long timestamp) {
        long currentTimeMillis = System.currentTimeMillis();
        long clientTimeMillis = timestamp < 1_000_000_000_000L ? timestamp * 1000 : timestamp;
        return Math.abs(currentTimeMillis - clientTimeMillis) <= REQUEST_EXP_TIME;
    }

    private static boolean rsaCheckV1(Map<String, String> params, String publicKey, String charset) {
        String sign = params.remove("sign");
        sign = sign.replace(" ", "+");
        params.remove("sign_type");
        String content = params.entrySet().stream()
                .filter(entry -> !isNullOrEmpty(entry.getKey()) && !isNullOrEmpty(entry.getValue()))
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        return verify(content, sign, publicKey, charset, "RSA2");
    }

    private static boolean verify(String content, String sign, String alipayPublicKey, String charset, String signType) {
        if (!isNullOrEmpty(charset) && !"utf-8".equalsIgnoreCase(charset)) {
            log.info("alipayGiftSend:verify:charset只支持utf-8");
            return false; // "charset只支持utf-8"
        }
        if (!isNullOrEmpty(signType) && !"RSA2".equalsIgnoreCase(signType)) {
            log.info("alipayGiftSend:verify:signType只支持RSA2");
            return false; // "signType只支持RSA2"
        }
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] encodedKey = alipayPublicKey.getBytes();
            encodedKey = org.bouncycastle.util.encoders.Base64.decode(encodedKey);
            PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initVerify(publicKey);
            signature.update(content.getBytes(StandardCharsets.UTF_8));
            return signature.verify(Base64.decode(sign.getBytes()));
        } catch (Exception e) {
            String errorMessage = "alipayGiftSend:验签遭遇异常，请检查公钥格式或签名是否正确。content=" + content + " sign=" + sign + " reason=" + e.getMessage();
            log.info("alipayGiftSend:sign fail.errorMessage:{}", errorMessage);
            return false; // "RSA签名失败"
        }
    }

    private static boolean isNullOrEmpty(String str) {
        return str == null || str.isEmpty();
    }

}