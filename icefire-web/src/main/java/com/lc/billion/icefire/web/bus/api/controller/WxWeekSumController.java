package com.lc.billion.icefire.web.bus.api.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.ApiConstants;
import com.lc.billion.icefire.core.common.JsonUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Utils;
import com.lc.billion.icefire.game.biz.redis.RedisClient;
import com.lc.billion.icefire.web.bus.api.account.entity.Account;
import com.lc.billion.icefire.web.bus.api.account.entity.AccountBind;
import com.lc.billion.icefire.web.bus.api.account.service.IAccountBindService;
import com.lc.billion.icefire.web.bus.api.account.service.impl.AccountServiceImpl;
import com.lc.billion.icefire.web.bus.api.service.lock.DistributedLock;
import com.lc.billion.icefire.web.bus.api.service.lock.LockKeyUtils;
import com.lc.billion.icefire.web.bus.config.WebSettingConfig;
import com.lc.billion.icefire.web.bus.gm.service.IEmailEndpointService;
import com.lc.billion.icefire.web.bus.user.entity.User;
import com.lc.billion.icefire.web.bus.user.service.impl.UserServiceImpl;
import com.lc.billion.icefire.web.bus.wechat.entity.WechatRewardInfo;
import com.lc.billion.icefire.web.mapper.WechatRewardInfoMapper;
import com.lc.billion.icefire.web.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@RestController
@RequestMapping(value = {ApiConstants.WARZ_BASE, ApiConstants.LEGACY_SANGUO2})
public class WxWeekSumController {

    private static final int TYPE_ID = 1;
    private static final long REQUEST_EXP_TIME = 300000; // 5分钟，单位毫秒
    private static final int REDIS_CACHE_TIME = 60;
    private static final String API_SECRET_KEY = "ZF2FFY6TWxxkqdfiVrtY";
    private static final String CACHE_KEY_PREFIX = "WxSearchUserCache.";

    private static final String MAIL_SUBJECT = "三国周报奖励发放";
    private static final String MAIL_CONTENT = "您的三国周报奖励已发放，请注意查收，祝您游戏愉快！";

    private final AccountServiceImpl accountService;
    private final UserServiceImpl userService;
    private final IAccountBindService accountBindService;
    private final WechatRewardInfoMapper wechatRewardInfoMapper;
    private final IEmailEndpointService emailEndpointService;
    private final ConfigServiceImpl configService;
    private final RedisClient redisClient;

    @Autowired
    public WxWeekSumController(AccountServiceImpl accountService, UserServiceImpl userService,
                               IAccountBindService accountBindService, WechatRewardInfoMapper wechatRewardInfoMapper,
                               IEmailEndpointService emailEndpointService, ConfigServiceImpl configService, @Qualifier("redisClient2") RedisClient redisClient) {
        this.accountService = accountService;
        this.userService = userService;
        this.accountBindService = accountBindService;
        this.wechatRewardInfoMapper = wechatRewardInfoMapper;
        this.emailEndpointService = emailEndpointService;
        this.configService = configService;
        this.redisClient = redisClient;
    }

    @PostMapping(value = "/searchUserByAccount", produces = "text/plain; charset=UTF-8")
    public String searchUserByAccount(@RequestParam String sdkUserId, @RequestParam long timestamp, @RequestParam String sign) {

        if (!validateSearchUserRequest(sdkUserId, timestamp, sign)) {
            return JsonUtils.toJson(Response.error(-1, "请求验证失败"));
        }

        String cacheKey = CACHE_KEY_PREFIX + sdkUserId;
        String cacheData = getCacheData(cacheKey);
        if (cacheData != null) {
            return cacheData;
        }
        List<AccountBind> accountBinds = accountBindService.findByPlatformId(sdkUserId);
        JSONArray jsonArray = new JSONArray();
        for (AccountBind accountBind : accountBinds) {
            Account account = accountService.findAccountById(accountBind.getAccId());
            if (account == null) {
                log.error("platformId[{}] find account[{}] is null", sdkUserId, accountBind.getAccId());
                continue;
            }
            List<User> userList = userService.findByAccId(accountBind.getAccId());
            if (!CollectionUtils.isEmpty(userList)) {
                for (User user : userList) {
                    jsonArray.add(createUserJSON(user, account, accountBind));
                }
            }
        }

        if (jsonArray.isEmpty()) {
            return JsonUtils.toJson(Response.error(-1, "没有查找到用户"));
        }
        JSONObject response = new JSONObject();
        response.put("ret", 0);
        response.put("data", jsonArray);
        String responseString = response.toJSONString();
        setCacheData(cacheKey, responseString);
        return responseString;
    }

    @PostMapping(value = "/wxWeekSumReward", produces = "text/plain; charset=UTF-8")
    public String wxWeekSumReward(@RequestParam String sdkUserId, @RequestParam int serverId,
                                  @RequestParam long roleId, @RequestParam long timestamp, @RequestParam String sign) {

        if (!validateWeekSumRewardRequest(sdkUserId, serverId, roleId, timestamp, sign)) {
            return JsonUtils.toJson(Response.error(-1, "请求验证失败"));
        }
        DistributedLock lock = new DistributedLock(redisClient, LockKeyUtils.getWxWeekSumKey(TYPE_ID, Utils.createMD5String(sdkUserId)), 30);
        try {
            if (lock.lock()) {
                long nowTimestamp = System.currentTimeMillis();
                WechatRewardInfo wechatRewardInfo = wechatRewardInfoMapper.selectByTypeIdAndAccountId(TYPE_ID, sdkUserId);
                if (wechatRewardInfo == null) {
                    wechatRewardInfo = createNewWechatRewardInfo(sdkUserId, serverId, roleId, nowTimestamp);
                } else if (isNewWeek(wechatRewardInfo.getUpdateTime())) {
                    wechatRewardInfo.setRewardStatus(true);
                    wechatRewardInfo.setUpdateTime(nowTimestamp);
                } else {
                    return JsonUtils.toJson(Response.error(-2, "本周已领奖"));
                }

                User user = userService.findByRoleId(roleId);

                if (!sendEmailByUser(user)) {
                    return JsonUtils.toJson(Response.error(-3, "奖励发送失败"));
                }
                wechatRewardInfoMapper.updateInsert(wechatRewardInfo);
                return JsonUtils.toJson(Response.success());
            }
            log.warn("丢弃重复请求; wxWeekSumReward! sdkUserId:{}, serverId:{} ,roleId:{}", sdkUserId, serverId, roleId);
            return JsonUtils.toJson(Response.error(-4, "丢弃重复请求"));
        } catch (Exception e) {
            log.error("wxWeekSumReward error! sdkUserId:{}, serverId:{} ,roleId:{} ,e:{}", sdkUserId, serverId, roleId, e.toString());
            return JsonUtils.toJson(Response.error(-5, "请稍后再试"));
        } finally {
            lock.unlock();
        }

    }


    private String getCacheData(String cacheKey) {
        try {
            return redisClient.get(cacheKey);
        } catch (Exception e) {
            log.error("Redis get operation failed for key: {}", cacheKey, e);
            return null;
        }
    }

    private void setCacheData(String cacheKey, String data) {
        try {
            redisClient.setex(cacheKey, REDIS_CACHE_TIME, data);
        } catch (Exception e) {
            log.error("Redis set operation failed for key: {}", cacheKey, e);
        }
    }

    /***
     * 发送邮件给指定的玩家
     * @param user 玩家
     * @return 成功返回true，否则返回false。
     */
    private boolean sendEmailByUser(User user) {
        if (user == null) {
            return false;
        }
        String rewardItems = configService.getConfig(WebSettingConfig.class).getWechatWeekSumRewardItems();
        try {
            var rs = emailEndpointService.roleEmail(String.valueOf(user.getRoleId()), user.getCurrentServerId(), MAIL_SUBJECT, MAIL_CONTENT, rewardItems, null, "");
            log.debug("roleEmail return str:{}", rs);
        } catch (Exception e) {
            log.error("wxWeekSumReward send Email failed roleId={},items={}", user.getRoleId(), rewardItems, e);
            throw new RuntimeException("奖励邮件发送失败");
        }
        return true;
    }

    /**
     * 判断指定时间戳是否超时
     *
     * @param timestamp 指定时间戳
     * @return 未超时责返回true, 超时责返回false
     */
    private boolean isTimestampValid(long timestamp) {
        return Math.abs(System.currentTimeMillis() - timestamp) <= REQUEST_EXP_TIME;
    }

    /**
     * 判断指定时间戳是否为当前周
     *
     * @param lastRewardTime 指定时间戳
     * @return 是则返回True, 否责返回False
     */
    private static boolean isNewWeek(long lastRewardTime) {
        LocalDate lastRewardDate = Instant.ofEpochMilli(lastRewardTime).atZone(ZoneId.of("Asia/Shanghai")).toLocalDate();
        LocalDate now = Instant.ofEpochMilli(TimeUtil.getNow()).atZone(ZoneId.of("Asia/Shanghai")).toLocalDate();
        // 获取上次领取时间所在周的周一
        LocalDate lastMonday = lastRewardDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 获取当前时间所在周的周一
        LocalDate thisMonday = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        return !lastMonday.equals(thisMonday);
    }
    /**
     * 验证请求参数的签名。
     * 验证过程：
     * 1. 检查必要参数（sign和timestamp）是否存在
     * 2. 暂时移除sign参数
     * 3. 添加共享私钥到参数列表
     * 4. 参数排序和拼接
     * - 将所有参数（包括key）按键名升序排列（字典序）
     * - 将排序后的参数以 "键=值" 的形式拼接，用 "&" 分隔
     * 5. 对拼接字符串进行MD5哈希，得到期望的签名
     * 6. 比较期望的签名和原始签名
     * <p>
     * 示例：
     * 原始参数：{userId=10001, product="apple", quantity=5}
     * 添加key后排序的拼接结果：
     * key=sharedSecretKey&product=apple&quantity=5&timestamp=1729599817547&userId=10001
     * md5:9201e35ab521d170772fb42198f569c4
     * 注意：即使原始参数顺序不同，排序后的结果应始终一致，key是双方约定的共享密钥
     *
     * @param params 包含所有请求参数的TreeMap。必须包含"sign"和"timestamp"。
     *               使用TreeMap确保参数按键的自然顺序排序。
     * @return 如果签名验证成功返回true，否则返回false。
     */
    private boolean verifySign(TreeMap<String, String> params) {
        if (params == null || params.isEmpty()) {
            return false;
        }
        String sign = params.getOrDefault("sign", "");
        String timestamp = params.getOrDefault("timestamp", "");
        if (sign.isEmpty() || timestamp.isEmpty()) {
            return false;
        }
        params.remove("sign");
        params.put("key", API_SECRET_KEY);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (!sb.isEmpty()) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        String expectedSign = DigestUtils.md5Hex(sb.toString()).toLowerCase();
        log.debug("verify str:{},sys sign:{},user sign:{},cur time:{}", sb, expectedSign, sign, System.currentTimeMillis());
        params.remove("key");
        params.put("sign", sign);
        return expectedSign.equals(sign.toLowerCase());
    }


    /**
     * 校验搜索玩家参数
     *
     * @param sdkUserId 名称
     * @param timestamp 时间戳
     * @param sign      签名
     * @return 是否通过
     */
    private boolean validateSearchUserRequest(String sdkUserId, long timestamp, String sign) {
        TreeMap<String, String> params = new TreeMap<>();
        params.put("sdkUserId", sdkUserId);
        params.put("timestamp", String.valueOf(timestamp));
        params.put("sign", sign);
        return verifySign(params) && isTimestampValid(timestamp);
    }

    /**
     * 校验玩家领奖参数
     *
     * @param sdkUserId 账户名
     * @param serverId  服务器Id
     * @param roleId    角色Id
     * @param timestamp 时间戳
     * @param sign      签名
     * @return 是否通过
     */
    private boolean validateWeekSumRewardRequest(String sdkUserId, int serverId, long roleId, long timestamp, String sign) {
        TreeMap<String, String> params = new TreeMap<>();
        params.put("sdkUserId", sdkUserId);
        params.put("serverId", String.valueOf(serverId));
        params.put("roleId", String.valueOf(roleId));
        params.put("timestamp", String.valueOf(timestamp));
        params.put("sign", sign);

        return verifySign(params) && isTimestampValid(timestamp);
    }


    private WechatRewardInfo createNewWechatRewardInfo(String sdkUserId, int serverId, long roleId, long timestamp) {
        WechatRewardInfo wechatRewardInfo = new WechatRewardInfo();
        wechatRewardInfo.setTypeId(TYPE_ID);
        wechatRewardInfo.setAccountId(sdkUserId);
        wechatRewardInfo.setServerId(serverId);
        wechatRewardInfo.setRoleId(roleId);
        wechatRewardInfo.setCreateTime(timestamp);
        wechatRewardInfo.setUpdateTime(timestamp);
        wechatRewardInfo.setRewardStatus(true);
        return wechatRewardInfo;
    }

    private JSONObject createUserJSON(User user, Account account, AccountBind accountBind) {
        JSONObject result = new JSONObject();
        result.put("id", user.getId());
        result.put("platform", accountBind.getPlatform());
        result.put("serverId", user.getCurrentServerId());
        result.put("createTime", user.getCreateTime());
        result.put("deviceid", account.getLastDeviceId());
        result.put("sdkUserId", accountBind.getPlatformId());
        result.put("whitelist", user.getWhitelist());
        result.put("roleId", user.getRoleId());
        result.put("sdkUserId", account.getAdvertisingId());
        result.put("ip", account.getIp());
        result.put("userName", user.getRoleName());
        result.put("icon", user.getRoleHead());
        return result;
    }

}