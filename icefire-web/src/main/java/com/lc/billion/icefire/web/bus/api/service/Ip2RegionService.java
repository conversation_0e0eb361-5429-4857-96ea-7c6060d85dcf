package com.lc.billion.icefire.web.bus.api.service;

import com.lc.billion.icefire.core.config.MetaUtils;
import org.lionsoul.ip2region.xdb.Searcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * @ClassName Ip2RegionService
 * @Description
 * <AUTHOR>
 * @Date 2024/5/22 16:21
 * @Version 1.0
 */
@Service
public class Ip2RegionService {

    private static final Logger logger = LoggerFactory.getLogger(Ip2RegionService.class);

    @Value("${ip2regiondata}")
    private String ip2regiondata;

//    private Searcher searcher = null;
    /**
     * 由于使用的是线程池，所以需要使用ThreadLocal来存储Searcher。否则会出现线程安全问题，导致登录失败等问题
     */
    private static final ThreadLocal<Searcher> threadLocalSearcher = new ThreadLocal<>();
    private static final ThreadLocal<Boolean> initSuccess = new ThreadLocal<>();

    @PostConstruct
    public void init() {
        // 移到每个线程中初始化，避免线程安全问题
//        try {
////            searcher = Searcher.newWithFileOnly(ip2regiondata);
//        } catch (Exception e) {
//            logger.error("", e);
//            throw new RuntimeException(e);
//        }
    }

    public String getCity(String ip) {
        String city = "";
        if (ip2regiondata == null) {
            return city;
        }
        try {
            Searcher searcher = threadLocalSearcher.get();
            if (searcher == null) {
                Boolean success = initSuccess.get();
                if (success != null && !success) {
                    return city;
                }
                try {
                    searcher = Searcher.newWithFileOnly(ip2regiondata);
                    threadLocalSearcher.set(searcher);
                    initSuccess.set(true);
                } catch (Exception e) {
                    initSuccess.set(false);
                    logger.error("", e);
                    throw new RuntimeException(e);
                }
            }

            String region = searcher.search(ip);
            if (region != null) {
                String[] split = MetaUtils.parse(region, '|');
                if (split != null && split.length == 5) {
                    city = split[3];
                }
            }
        } catch (Exception e) {
            logger.error("", e);
            return city;
        }
        return city;
    }

    public static void main(String[] args) {
        Ip2RegionService ip2RegionService = new Ip2RegionService();
        ip2RegionService.ip2regiondata = "data/ip2region.xdb";
        ip2RegionService.init();
        String[] ips = {
                "**************",
                "*************",
                "************",
                "**************",
                "**************",
                "*************",
                "**************",
        };
        for (String ip : ips) {
            String region = ip2RegionService.getCity(ip);
            System.out.println("ip: " + ip + ", region: " + region);
        }
    }

}
