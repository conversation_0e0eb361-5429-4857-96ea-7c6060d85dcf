package com.lc.billion.icefire.web.bus.api.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.cache.CacheManager;
import com.lc.billion.icefire.core.ApiConstants;
import com.lc.billion.icefire.core.common.JsonUtils;
import com.lc.billion.icefire.core.support.Utils;
import com.lc.billion.icefire.game.biz.redis.RedisClient;
import com.lc.billion.icefire.web.bus.api.account.entity.Account;
import com.lc.billion.icefire.web.bus.api.account.entity.AccountBind;
import com.lc.billion.icefire.web.bus.api.account.service.IAccountBindService;
import com.lc.billion.icefire.web.bus.api.account.service.IAccountService;
import com.lc.billion.icefire.web.bus.api.entity.Recharge;
import com.lc.billion.icefire.web.bus.api.entity.RechargeState;
import com.lc.billion.icefire.web.bus.api.service.AuthRepository;
import com.lc.billion.icefire.web.bus.api.service.IAuthContext;
import com.lc.billion.icefire.web.bus.api.service.IAuthService;
import com.lc.billion.icefire.web.bus.api.service.impl.RechargeServiceImpl;
import com.lc.billion.icefire.web.bus.gm.model.BulletinBoard;
import com.lc.billion.icefire.web.bus.gm.model.CommunityNotice;
import com.lc.billion.icefire.web.bus.gm.model.CommunityShare;
import com.lc.billion.icefire.web.bus.gm.service.IBulletinBoardService;
import com.lc.billion.icefire.web.bus.gm.service.ICommunityNoticeService;
import com.lc.billion.icefire.web.bus.gm.service.ICommunityShareService;
import com.lc.billion.icefire.web.bus.server.service.IServerService;
import com.lc.billion.icefire.web.bus.user.entity.User;
import com.lc.billion.icefire.web.bus.user.service.IUserService;
import com.lc.billion.icefire.web.utils.HttpUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 * @sine 2016年1月8日 下午4:26:10
 * 
 */
@Controller
@RequestMapping(value = {ApiConstants.WARZ_BASE, ApiConstants.LEGACY_SANGUO2})
public class ApiController {

	private Logger LOG = LoggerFactory.getLogger(getClass());

	public static final String CLIENT_INFO = "client_info_final";

	public static final String OS_ANDROID = "android";
	public static final String OS_IOS = "ios";

	public static final int EU_OPEN = 1;
	/**
	 * 获取公告信息列表，
	 * 新需求是要客户端能支持多张图片
	 * 为了兼容老版本，增加1个字段判断
	 * 这个值，代表新客户端支持2张图片处理，
	 * 老的客户端还是下发一张
	 */
	public static final String NEW_PICTURE_2_VERSION="1";
	public static final String NEW_PICTURE_2_SPLIT="|";

	@Autowired
	private IServerService serverService;

	@Autowired
	private IUserService userService;

	@Autowired
	private AuthRepository authRepository;

	@Autowired
	@Qualifier("redisClient0")
	private RedisClient redisClient;

	@Autowired
	private RechargeServiceImpl rechargeService;

	@Autowired
	private IBulletinBoardService bulletinBoardService;

	@Autowired
	@Qualifier("redisClient2")
	private RedisClient redisClient2;

	@Autowired
	private IAccountBindService accountBindService;

	@Autowired
	private IAccountService accountService;
	@Autowired
	private ICommunityNoticeService communityNoticeService;
	@Autowired
	private ICommunityShareService communityShareService;



	/**
	 * 订单状态查询接口
	 * 
	 * @param orderId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/orderInfo")
	public @ResponseBody Map<String, String> orderInfo(String orderId, HttpServletRequest request) {
		Map<String, String> result = new HashMap<>();
		if (Utils.isEmpty(orderId)) {
			result.put("ret", "2");
			return result;
		}
		Recharge recharge = rechargeService.get(orderId);
		if (recharge == null) {// 不存在
			result.put("ret", "2");
		} else if (recharge.getState() == RechargeState.READY) {
			result.put("ret", "1");
		} else {// 成功状态
			result.put("ret", "0");

		}
		return result;
	}

	@RequestMapping(value = "/updategaid", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String updategaid(String id, String token, int platform, String gaid, int serverId) {
		LOG.info("id=" + id + " token=" + token + "platform=" + platform + "gaid=" + gaid);
		JSONObject json = new JSONObject();

		// 平台认证
		IAuthService authService = authRepository.getService(platform);
		IAuthContext authContext = authService.auth(id, token);

		if (!authContext.isSuccess()) {
			json.put("ret", ErrorCode.FAILED_AUTHENTICATION);
			json.put("msg", "Failed user authentication.");

			LOG.error("Failed user authentication. token: {}, platform: {}", token, platform);
			return json.toJSONString();
		}

		// 查找用户是否存在
//		User user = userService.findOne(platform, serverId);
//		if (user == null) {// 没有用户
//			json.put("ret", ErrorCode.USER_ERROR);
//			json.put("msg", "user is null.");
//
//			LOG.error("user is null. token: {}, platform: {}", token, platform);
//			return json.toJSONString();
//		}
		// biLogService.updateUserGaid(user.getServerId(), user.getRoleId(), gaid);

		//2020-10-22 sean 原本写的就有问题, 现在账号系统重新设计,一起改了. 不过貌似实际并没有执行过该方法
		AccountBind accountBind = accountBindService.findByPlatformIdAndPlatform(authContext.getUserId(), platform);
		if(accountBind == null){
			json.put("ret", ErrorCode.ACCOUNT_NOT_FOUND);
			json.put("msg", "account bind is null.");
			LOG.error("AccountBind is null. id: {} token: {}, platform: {}, platformId: {}", id, token, platform, authContext.getUserId());
			return json.toJSONString();
		}
		Account account = accountService.findAccountById(accountBind.getAccId());
		if(account == null){
			json.put("ret", ErrorCode.ACCOUNT_NOT_FOUND);
			json.put("msg", "account is null.");
			LOG.error("Account is null. id: {} token: {}, platform: {}, platformId: {}", id, token, platform, authContext.getUserId());
			return json.toJSONString();
		}
		account.setAdvertisingId(gaid);
		accountService.updateAccount(account);

		json.put("ret", "0");
		return json.toJSONString();
	}

	@RequestMapping(value = "/euopen", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String euopen(HttpServletRequest request) {
		JSONObject json = new JSONObject();
		Map<String, String[]> parameters = new HashMap<>(request.getParameterMap());
		if (LOG.isInfoEnabled()) {
			LOG.info(request.getRequestURL().toString() + "->" + request.getMethod() + "->" + HttpUtils.formatInovationParameters(parameters));
		}

		String userIdStr = request.getParameter("userId");
		String openStr = request.getParameter("open");

		try {
			long userId = Long.parseLong(userIdStr);
			int open = Integer.parseInt(openStr);
			User user = userService.findOne(userId);
			if (user != null) {
				user.setEuopen(open);
				userService.updateUser(user);
				json.put("ret", "0");
			} else {
				LOG.info("euopen user is null! userId:{} ", userIdStr);
				json.put("ret", "-1");
			}
		} catch (Exception e) {
			LOG.error("euopen error! userId:{} ", userIdStr, e);
			json.put("ret", "-1");
		}
		return json.toJSONString();
	}

	@RequestMapping(value = "/chatProtocal", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String chatProtocal(HttpServletRequest request) {
		JSONObject json = new JSONObject();
		Map<String, String[]> parameters = new HashMap<>(request.getParameterMap());
		if (LOG.isInfoEnabled()) {
			LOG.info(request.getRequestURL().toString() + "->" + request.getMethod() + "->" + HttpUtils.formatInovationParameters(parameters));
		}

		String userIdStr = request.getParameter("userId");
		String chatProtocalStr = request.getParameter("chatProtocal");

		try {
			long userId = Long.parseLong(userIdStr);
			int chatProtocal = Integer.parseInt(chatProtocalStr);
			User user = userService.findOne(userId);
			if (user != null) {
				user.setChatProtocal(chatProtocal);
				userService.updateUser(user);
				json.put("ret", "0");
			} else {
				LOG.info("chatProtocal user is null! userId:{} ", userIdStr);
				json.put("ret", "-1");
			}
		} catch (Exception e) {
			LOG.error("chatProtocal error! userId:{} ", userIdStr, e);
			json.put("ret", "-1");
		}
		return json.toJSONString();
	}

	@RequestMapping("/clientversion")
	public @ResponseBody String version() {
		// XXX 通过PUBSUB优化为不是每次都取
		String result = redisClient.get(ApiController.CLIENT_INFO);
		return result;
	}

	/**
	 * 获取公告最后更新时间
	 */
	@RequestMapping(value = "/getBulletinUpdateTime", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String getBulletinUpdateTime(long roleId) {
		User user = userService.findByRoleId(roleId);
		JSONObject json = new JSONObject();
		if (user == null) {
			json.put("ret", ErrorCode.UNBIND_ERROR);
			json.put("msg", "Failed user get bulletin update time.");
			LOG.error("getBulletinUpdateTime failed! get user is null.role is: {}", roleId);
			return json.toJSONString();
		}
		String updateTime = redisClient2.get(IBulletinBoardService.BULLETIN_UPDATE_TIME);
		json.put("ret", 0);
		json.put("msg", "get bulletin update success");
		json.put("lastUpdateTime", updateTime);
		return json.toJSONString();
	}

	/**
	 * 获取公告信息列表
	 */
	@RequestMapping(value = "/getBulletinInfo", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String getBulletinInfo(long roleId, String language,String newVersion) {
		User user = userService.findByRoleId(roleId);
		JSONObject json = new JSONObject();
		if (user == null) {
			json.put("ret", ErrorCode.UNBIND_ERROR);
			json.put("msg", "Failed user get bulletin info.");
			LOG.error("getBulletinUpdateTime failed! get user is null.role is: {}", roleId);
			return json.toJSONString();
		}
		Account account = accountService.findAccountById(user.getAccId());
		if(account == null){
			json.put("ret", ErrorCode.UNBIND_ERROR);
			json.put("msg", "Failed user get bulletin info.");
			LOG.error("getBulletinUpdateTime failed! get account is null.role is: {}", roleId);
			return json.toJSONString();
		}
		List<BulletinBoard> bulletinList = bulletinBoardService.getBulletinList(user.getServerId(), account.getCurrPlatform());
		json.put("ret", 0);
		json.put("msg", "get bulletin list success");
		String lang = CacheManager.getInstance().getLanguagebyName(language);
		if(CollectionUtils.isEmpty(bulletinList)){
			json.put("bulletins",new JSONArray().toJSONString());
		}
		else{
			JSONArray bulletinsJson = new JSONArray();
			for(BulletinBoard bulletin : bulletinList){
				JSONObject bulletinJson = bulletin.toJson();
				/**
				 * 老客户端，可能以后在增加一种类型，newVersion 这个值就用得到
				 */
				try {
					if(newVersion==null || !NEW_PICTURE_2_VERSION.equals(newVersion)){
						if(StringUtils.isNotEmpty(bulletin.getPictureId()) && bulletin.getPictureId().indexOf(NEW_PICTURE_2_SPLIT)>-1){
							bulletinJson.put("pictureId",bulletin.getPictureId().split(NEW_PICTURE_2_SPLIT)[0]);
						}
					}
				}catch (Exception e){
					LOG.error(e.getMessage(),e);
				}

				Map<String, String> contentMap =bulletinBoardService.getContent(bulletin.getContentKey(), lang);
				bulletinJson.putAll(contentMap);
				bulletinsJson.add(bulletinJson);
			}
			json.put("bulletins", bulletinsJson.toJSONString());
		}
		return json.toJSONString();

	}

	/**
	 * 获取社区公告信息
	 * @param roleId
	 * @param language
	 * @return
	 */
	@RequestMapping(value = "/getCommunityInfo", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String getCommunityInfo(long roleId, String language,String newVersion) {
		User user = userService.findByRoleId(roleId);
		JSONObject json = new JSONObject();
		if (user == null) {
			json.put("ret", ErrorCode.UNBIND_ERROR);
			json.put("msg", "Failed user get bulletin info.");
			LOG.error("getBulletinUpdateTime failed! get user is null.role is: {}", roleId);
			return json.toJSONString();
		}
		Account account = accountService.findAccountById(user.getAccId());
		if(account == null){
			json.put("ret", ErrorCode.UNBIND_ERROR);
			json.put("msg", "Failed user get bulletin info.");
			LOG.error("getBulletinUpdateTime failed! get account is null.role is: {}", roleId);
			return json.toJSONString();
		}
		List<CommunityNotice> communityList = communityNoticeService.getCommunityList(user.getServerId(), account.getCurrPlatform());
		json.put("ret", 0);
		json.put("msg", "get bulletin list success");
		if(CollectionUtils.isEmpty(communityList)){
			json.put("bulletins",new JSONArray().toJSONString());
		}
		else{
			JSONArray bulletinsJson = new JSONArray();
			for(CommunityNotice bulletin : communityList){
				JSONObject bulletinJson = bulletin.toJson();
				Map<String, String> contentMap =communityNoticeService.getContent(bulletin.getContentKey(), language);
				bulletinJson.putAll(contentMap);
				bulletinsJson.add(bulletinJson);
			}
			json.put("bulletins", bulletinsJson.toJSONString());
		}
		List<CommunityShare> communityShareList = communityShareService.getCommunityShareList(user.getCurrentServerId(), account.getCurrPlatform());
		if(CollectionUtils.isEmpty(communityShareList)){
			json.put("shares",new JSONArray().toJSONString());
		}else{
			JSONArray bulletinsJson = new JSONArray();
			communityShareList.forEach(e->{
				JSONObject jsonObject = e.toJson();
				/**
				 * 新的请求方式增加2个字段处理 兼容老的客户端
				 */
				if(newVersion!=null && NEW_PICTURE_2_VERSION.equals(newVersion)){
					Map<String, String> contentMap =communityShareService.getContent(e.getEnterTitle(), language);
					jsonObject.putAll(contentMap);
				}
				bulletinsJson.add(jsonObject);
			});
			json.put("shares",bulletinsJson.toJSONString());
		}
		return json.toJSONString();

	}

	/**
	 * 获取公告投票信息
	 */
	@RequestMapping(value = "/getBulletinVote", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String getBulletinVote(long roleId, long bulletinId) {
		User user = userService.findByRoleId(roleId);
		JSONObject json = new JSONObject();
		if (user == null) {
			json.put("ret", ErrorCode.UNBIND_ERROR);
			json.put("msg", "Failed user get bulletin vote.");
			LOG.error("getBulletinVote failed! get user is null.role is: {}", roleId);
			return json.toJSONString();
		}
		BulletinBoard bulletinBoard = bulletinBoardService.selectById(bulletinId);
		if(bulletinBoard == null){
			json.put("ret", ErrorCode.BULLETIN_NOT_FOND);
			json.put("msg", "Failed get bulletin");
			LOG.error("getBulletinVote failed! get bulletin[{}] is null!", bulletinId);
			return json.toJSONString();
		}
		json.put("ret", 0);
		json.put("msg", "get bulletin vote success");
		json.put("id", bulletinId);
		Map<String, String> voteValue = redisClient2.hgetMap(IBulletinBoardService.BULLETIN_VOTE_INFO + bulletinId);
		if(voteValue == null){
			json.put("votes", "");
		}
		else{
			json.put("votes", JsonUtils.parseMap(voteValue).toJSONString());
		}
		return json.toJSONString();

	}

	/**
	 * 获取公告信息内容
	 */
	@RequestMapping(value = "/addBulletinVote", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String addBulletinVote(long roleId, long bulletinId, String voteKey) {
		User user = userService.findByRoleId(roleId);
		JSONObject json = new JSONObject();
		if (user == null) {
			json.put("ret", ErrorCode.UNBIND_ERROR);
			json.put("msg", "Failed user bulletin vote.");
			LOG.error("addBulletinVote failed! get user is null.role is: {}", roleId);
			return json.toJSONString();
		}
		Account account = accountService.findAccountById(user.getAccId());
		if(account == null){
			json.put("ret", ErrorCode.UNBIND_ERROR);
			json.put("msg", "Failed user get bulletin info.");
			LOG.error("addBulletinVote failed! get account is null.role is: {}", roleId);
			return json.toJSONString();
		}

		BulletinBoard bulletin = bulletinBoardService.selectById(bulletinId);
		if(bulletinBoardService.checkBulletinValid(bulletin, String.valueOf(user.getServerId()), String.valueOf(account.getCurrPlatform()))){
			redisClient2.hincrby(IBulletinBoardService.BULLETIN_VOTE_INFO+ bulletinId, voteKey, 1);
		}
		else{
			LOG.error("user[{}], add bulletin[{}] vote number[{}] failed!", roleId, bulletinId, voteKey);
		}
		json.put("ret", 0);
		json.put("msg", "bulletin vote success");
		return json.toJSONString();
	}


	private static class ErrorCode{
		public static final int FAILED_AUTHENTICATION = -1;
		public static final int USER_ERROR = -2;
		public static final int PLATFORM_ALREADY_BOUND = -3;
		public static final int ACCOUNT_CREATE_FAILED = -4;
		public static final int ACCOUNT_NOT_FOUND= -5;
		public static final int GAME_SERVER_ID_ERROR= -6;
		public static final int BIND_SAME_PLATFORM= -7;
		public static final int UNBIND_ERROR= -8;
		public static final int UNBIND_USER_NOT_SAME= -9;
		public static final int USER_IS_BAN= -10;
		public static final int BULLETIN_NOT_FOND = -11;
	}
}
