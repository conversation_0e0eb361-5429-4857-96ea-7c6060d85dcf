<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.3.xsd



       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.3.xsd">
    
    <context:annotation-config />
    <context:component-scan base-package="com.lc.billion.icefire.web" />
    <context:component-scan base-package="com.lc.billion.icefire.core.config" />

	<bean id="propertyConfigurer" class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>/WEB-INF/config/server.properties</value>
                <value>/WEB-INF/config/platform.properties</value>
                <value>/WEB-INF/config/zookeeper.properties</value>
                <value>/WEB-INF/config/appsflyer.properties</value>
				<value>/WEB-INF/version.properties</value>
            </list>
        </property>
		<property name="fileEncoding">
			<value>utf-8</value>
		</property>

	</bean>
    <bean id="serverConfigManager" class="com.lc.billion.icefire.web.ServerConfigManager" init-method="init">
        <property name="locations">
            <list>
                <value>/WEB-INF/config/servertype.json</value>
            </list>
        </property>
    </bean>
    <bean id="isNormalGameServerTester" class="org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder"></bean>
    <bean id="passwordEncoder" class="org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder"></bean>
    
    <bean id="configureService"
        class="com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl" init-method="start">
        <property name="configPackages">
        	<list>
        		<value>com.lc.billion.icefire.web.bus.config</value>
        	</list>
        </property>
    </bean>

	<bean id="webConfigListener" class="com.lc.billion.icefire.web.WebConfigListener" ></bean>
    
	<bean id="configCenter"
		class="com.longtech.ls.zookeeper.ConfigCenter" destroy-method="close">
		<constructor-arg name="envKey_GameServerId" value="" />
		<constructor-arg name="envKey_ZkPath" value="LS_ENV_KEY_ZK_PATH" />
		<constructor-arg name="envKey_ZkUrl" value="LS_ENV_KEY_ZK_URL" />
		<constructor-arg name="zkConnectString" value="${zk.connectString}" />
		<constructor-arg name="base" value="${zk.configPath}" />
		<constructor-arg name="serverTypeDeterminer" value="#{serverConfigManager.serverTypeDeterminer}" />
	</bean>

	<bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
	    <property name="targetObject" ref="configCenter" />
	    <property name="targetMethod" value="addGameServerConfigListener" />
	    <property name="arguments" ref="webConfigListener" />
	</bean>

    <!-- cache 配置,存在冗余属性，选择jedis时，codis的属性无效；同理，选择codis时，jedis的属性无效 -->
	<!-- 老功能使用，暂时保留，不与深究 -->
	<bean id="redisClient0" class="com.lc.billion.icefire.game.biz.redis.JedisClient">
		<property name="host" value="#{configCenter.lsConfig.webServer.redis.host}" />
		<property name="port" value="#{configCenter.lsConfig.webServer.redis.port}" />
		<property name="password" value="#{configCenter.lsConfig.webServer.redis.password}" />
		<property name="timeout" value="30000" />
		<property name="maxTotal" value="800" />
		<property name="maxIdle" value="150" />
		<property name="mxWaitMillis" value="5000" />
		<property name="dbindex" value="0" />
	</bean>
	
	<!-- cache 配置,存在冗余属性，选择jedis时，codis的属性无效；同理，选择codis时，jedis的属性无效 -->
	<!-- 活动中的多服排名，或者活动中的少量的多服务数据共享 -->
	<bean id="redisClient1" class="com.lc.billion.icefire.game.biz.redis.JedisClient">
		<property name="host" value="#{configCenter.lsConfig.webServer.redis.host}" />
		<property name="port" value="#{configCenter.lsConfig.webServer.redis.port}" />
		<property name="password" value="#{configCenter.lsConfig.webServer.redis.password}" />
		<property name="timeout" value="30000" />
		<property name="maxTotal" value="500" />
		<property name="maxIdle" value="30" />
		<property name="mxWaitMillis" value="5000" />
		<property name="dbindex" value="1" />
	</bean>

    <!-- cache 配置,存在冗余属性，选择jedis时，codis的属性无效；同理，选择codis时，jedis的属性无效 -->
	<!-- 边缘功能中需要跨服共享的数据，如：公告点赞数（多服共享） -->
	<bean id="redisClient2" class="com.lc.billion.icefire.game.biz.redis.JedisClient">
		<property name="host" value="#{configCenter.lsConfig.webServer.redis.host}" />
		<property name="port" value="#{configCenter.lsConfig.webServer.redis.port}" />
		<property name="password" value="#{configCenter.lsConfig.webServer.redis.password}" />
		<property name="timeout" value="30000" />
		<property name="maxTotal" value="500" />
		<property name="maxIdle" value="30" />
		<property name="mxWaitMillis" value="5000" />
		<property name="dbindex" value="2" />
	</bean>

    <!-- 原zk的instance数据：每个服的注册速度、在线人数等 -->
    <bean id="redisClient5" class="com.lc.billion.icefire.game.biz.redis.JedisClient">
        <property name="host" value="#{configCenter.lsConfig.webServer.redis.host}" />
        <property name="port" value="#{configCenter.lsConfig.webServer.redis.port}" />
        <property name="password" value="#{configCenter.lsConfig.webServer.redis.password}" />
        <property name="timeout" value="30000" />
        <property name="maxTotal" value="500" />
        <property name="maxIdle" value="30" />
        <property name="mxWaitMillis" value="5000" />
        <property name="dbindex" value="5" />
    </bean>

	<bean id="webRPCServer" class="com.lc.billion.icefire.web.bus.rpc.WebRPCServer" init-method="init" destroy-method="destroy">
		<constructor-arg name="ip" value="#{configCenter.lsConfig.webServer.rpcBindIp}" />
		<constructor-arg name="port" value="#{configCenter.lsConfig.webServer.rpcBindPort}" />
		<constructor-arg name="threads" value="2"/>
	</bean>
	
	<!-- apache shiro配置初始化，依赖flyway，须优先启动 -->
	<bean id="flywayService" class="com.lc.billion.icefire.web.bus.flyway.FlywayService">
		<constructor-arg name="autoInc" value="${autoInc}"/>
		<constructor-arg name="dataSource" ref="dataSource" />
	</bean>

	
    <import resource="classpath:config/spring/mybatis/mybatis-config.xml" />
    <import resource="../shiro/spring-shiro.xml" />
	<bean id="versionManager" class="com.lc.billion.icefire.VersionManager" init-method="init">
		<property name="deployVersion" value="${deployVersion}"/>
	</bean>
    
</beans>