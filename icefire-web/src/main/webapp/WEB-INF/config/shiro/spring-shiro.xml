<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="
       http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
       http://www.springframework.org/schema/aop
           http://www.springframework.org/schema/aop/spring-aop-4.3.xsd">
    <aop:aspectj-autoproxy/>
    <!-- 缓存管理器，使用Ehcache实现 -->
    <bean id="cacheManager" class="org.apache.shiro.cache.ehcache.EhCacheManager">
<!--         <property name="cacheManagerConfigFile" value="classpath:config/spring/ehcache.xml"/> -->
    </bean>
 
   	<!-- 凭证匹配器，验证密码是否正确，用 下面的类去验证  -->
    <bean id="credentialsMatcher" class="org.apache.shiro.authc.credential.HashedCredentialsMatcher">
        <property name="hashAlgorithmName" value="MD5"/>
        <property name="hashIterations" value="2"/>
        <property name="storedCredentialsHexEncoded" value="true"/>
    </bean>
 
    <!-- Realm实现 -->
    <bean id="userRealm" class="com.lc.billion.icefire.web.bus.shiro.AuthRealm">
        <property name="credentialsMatcher" ref="credentialsMatcher"/>
        <!-- 启用缓存 -->
        <property name="cachingEnabled" value="true"/>
        <!-- 启用身份验证缓存，即缓存AuthenticationInfo信息 -->
        <property name="authenticationCachingEnabled" value="true"/>
        <!-- 缓存AuthenticationInfo信息的缓存名称 -->
        <property name="authenticationCacheName" value="authenticationCache"/>
        <!-- 启用授权缓存，即缓存AuthorizationInfo信息 -->
        <property name="authorizationCachingEnabled" value="true"/>
        <!-- 缓存AuthorizationInfo信息的缓存名称 -->
        <property name="authorizationCacheName" value="authorizationCache"/>
    </bean>
 
    <!-- 安全管理器 -->
    <bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
        <property name="realm" ref="userRealm"/>
        <property name="cacheManager" ref="cacheManager"/>
    </bean>
 
 	<!-- 自定义过滤器 -->
 	<bean id="ajaxFormAuthenticationFilter" class="com.lc.billion.icefire.web.bus.shiro.filter.AjaxFormAuthenticationFilter" />
 	<bean id="ajaxPermissionsAuthorizationFilter" class="com.lc.billion.icefire.web.bus.shiro.filter.AjaxPermissionsAuthorizationFilter" />
 	<bean id="ajaxRolesAuthorizationFilter" class="com.lc.billion.icefire.web.bus.shiro.filter.AjaxRolesAuthorizationFilter" />
 
    <!-- Shiro的Web过滤器 -->
    <bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
    	<!-- 安全管理器 -->
        <property name="securityManager" ref="securityManager"/>
        <!-- 登录表单界面 -->
        <property name="loginUrl" value="/login"/>
        <!-- 登录成功跳转界面 -->
        <property name="successUrl" value="/console" />
        <!-- 登录成功,没有权限界面 -->
        <property name="unauthorizedUrl" value="/unauthorized"/>
        
        <!-- 使用数据库动态配置 -->
        <property name="filterChainDefinitionMap" ref="filterChainDefinitionMap"></property>
        
        <!-- 设置权限过滤器 -->
        <property name="filters">
        	<map>
        		<entry key="authc" value-ref="ajaxFormAuthenticationFilter"></entry>
        		<entry key="roles" value-ref="ajaxRolesAuthorizationFilter"></entry>
        		<entry key="perms" value-ref="ajaxPermissionsAuthorizationFilter"></entry>
        	</map>
        </property>

        <!-- <property name="filterChainDefinitions">
        	<value>
        		静态资源
        		/css/** = anon
        		/js/** = anon
        		/images/** = anon
        		/fonts/** = anon
        		
        		登录、登出
            	/login = anon
            	/loginUser = anon
            	/logout = logout
            	/unauthorized = anon
            	
            	特殊页面
            	/ = anon
            	/api/** = anon
            	/about = anon
            	
            	业务相关url
        		/console = perms[console_index]
				/console/roleinfo = perms[console_roleinfo]
				/console/role/searchinfo/uid = perms[console_role_searchinfo_uid]
				/console/role/searchinfo/name = perms[console_role_searchinfo_name]
				/console/role/ban/banacc = perms[console_role_ban_banacc]
				/console/role/ban/unbanacc = perms[console_role_ban_unbanacc]
				/console/banned/chat = perms[console_role_banned_chat]
				/console/unban/chat = perms[console_role_unban_chat]
				/console/banned/head = perms[console_role_banned_head]
				/console/unban/head = perms[console_role_unban_head]
				/console/reset/head = perms[console_role_reset_head]
				/console/role/applyGm = perms[console_role_applygm]
				/console/mission/finish = perms[console_mission_finish]
				/console/role/seachUsers/deviceid = perms[console_role_seachusers_deviceid]
				/console/role/changeDevId/deviceid = perms[console_role_changedevid_deviceid]
				/console/push = perms[console_push]
				/console/push/add = perms[console_push_add]
				/console/banHeadRecord = perms[console_banheadrecord]
				/console/search/headRecord = perms[console_search_headrecord]
				/console/systememail = perms[console_systememail]
				/console/email/system = perms[console_email_system]
				/console/roleemail = perms[console_roleemail]
				/console/email/role = perms[console_email_role]
				/console/maillist = perms[console_maillist]
				/console/updateroledata = perms[console_updateroledata]
				/console/updateroledata/search/uid = perms[console_updateroledata_search_uid]
				/updateroledata/search/name = perms[console_updateroledata_search_name]
				/console/billboard = perms[console_billboard]
				/console/billboard/update = perms[console_billboard_update]
				/console/billboard/remove = perms[console_billboard_remove]
				/console/billboard_ = perms[console_billboard_]
				/console/billboard/update_ = perms[console_billboard_update_]
				/console/notice = perms[console_notice]
				/console/notice/marquee = perms[console_notice_marquee]
				/console/notice/cancelmarquee = perms[console_notice_cancelmarquee]
				/console/command = perms[console_command]
				/console/role/search/uid = perms[console_role_search_uid]
				/console/role/search/name = perms[console_role_search_name]
				/console/command/execute = perms[console_command_execute]
				/console/changename = perms[console_changename]
				/console/user/searchuserbythird = perms[console_user_searchuserbythird]
				/console/user/changename/searchuser = perms[console_user_changename_searchuser]
				/console/user/searchnewuser = perms[console_user_searchnewuser]
				/console/language = perms[console_language]
				/console/language/checkkey = perms[console_language_checkkey]
				/console/language/addrecord = perms[console_language_addrecord]
				/console/language/updaterecord = perms[console_language_updaterecord]
				/console/language/remove = perms[console_language_remove]
				/console/language/category = perms[console_language_category]
				/console/language/refreshCache = perms[console_language_refreshcache]
				/console/order = perms[console_order]
				/console/order/search = perms[console_order_search]
				/console/order/fixOrder = perms[console_order_fixorder]
				/console/guideEmail = perms[console_guideemail]
				/console/guideEmail/all = perms[console_guideemail_all]
				/console/guideEmail/add = perms[console_guideemail_add]
				/console/guideEmail/update = perms[console_guideemail_update]
				/console/guideEmail/del = perms[console_guideemail_del]
				/console/guideEmail/upload = perms[console_guideemail_upload]
				/console/welfare = perms[console_welfare]
				/console/welfare/search = perms[console_welfare_search]
				/console/welfare/addWelfare = perms[console_welfare_addwelfare]
				/console/welfare/edit = perms[console_welfare_edit]
				/console/welfare/remove = perms[console_welfare_remove]
				/console/protection = perms[console_protection]
				/console/protection/add = perms[console_protection_add]
				/console/protectionList = perms[console_protectionlist]
				/console/searchGM = perms[console_searchgm]
				/console/searchGM/search = perms[console_searchgm_search]
				/console/popface = perms[console_popface]
				/console/popface/file = perms[console_popface_file]
				/console/popface/del = perms[console_popface_del]
				/console/resourceedit = perms[console_resourceedit]
				/console/role/searchResource/name = perms[console_role_searchresource_name]
				/console/role/searchResource/uid = perms[console_role_searchresource_uid]
				/console/add/resource = perms[console_add_resource]
				/console/update/resource = perms[console_update_resource]
				/console/itemedit = perms[console_itemedit]
				/console/role/searchItem/name = perms[console_role_searchitem_name]
				/console/role/searchItem/uid = perms[console_role_searchitem_uid]
				/console/add/item = perms[console_add_item]
				/console/update/item = perms[console_update_item]
				/console/buildingedit = perms[console_buildingedit]
				/console/role/searchBuilding/name = perms[console_role_searchbuilding_name]
				/console/role/searchBuilding/uid = perms[console_role_searchbuilding_uid]
				/console/update/building = perms[console_update_building]
				/console/armyedit = perms[console_armyedit]
				/console/role/searchArmy/name = perms[console_role_searcharmy_name]
				/console/role/searchArmy/uid = perms[console_role_searcharmy_uid]
				/console/add/army = perms[console_add_army]
				/console/update/army = perms[console_update_army]
				/console/metric = perms[console_metric]
				/console/metric/load = perms[console_metric_load]
				/console/server = perms[console_server]
				/console/server/checkid = perms[console_server_checkid]
				/console/server/update = perms[console_server_update]
				/console/server/remove = perms[console_server_remove]
				/console/server/startall = perms[console_server_startall]
				/console/server/stopall = perms[console_server_stopall]
				/console/server/updateWhiteL = perms[console_server_updatewhitel]
				/console/server/startBatch = perms[console_server_startbatch]
				/console/server/stopBatch = perms[console_server_stopbatch]
				/console/system/command = perms[console_system_command]
				/console/system/command/execute = perms[console_system_command_execute]
				/console/dynamicode = perms[console_dynamicode]
				/console/dynamicode/send = perms[console_dynamicode_send]
				/console/health = perms[console_health]
				/console/health/watch = perms[console_health_watch]
				/console/bifollowselect = perms[console_bifollowselect]
				/console/useroptions = perms[console_useroptions]
				/console/select/userOptions = perms[console_select_useroptions]
        		
        		密码修改页面
        		/console/admin/changepwd = anon
        		/console/admin/checkPass = anon
        		/console/admin/changepass = anon
        		
        		其他
                /** = authc,roles[aaa]
            </value>
        </property> -->
    </bean>
 
     <!-- 配置一个 bean, 该 bean 实际上是一个 Map. 通过实例工厂方法的方式 -->
    <bean id="filterChainDefinitionMap" factory-bean="filterChainDefinitionMapBuilder" factory-method="buildFilterChainDefinitionMap" />
	<bean id="filterChainDefinitionMapBuilder" class="com.lc.billion.icefire.web.bus.shiro.FilterChainDefinitionMapBuilder" />
 
    <!-- Shiro生命周期处理器-->
    <bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor"/>
    
    <!-- 开启shiro方法授权注解 -->
<!--     <bean class="org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator" depends-on="lifecycleBeanPostProcessor">
    	<property name="proxyTargetClass" value="true" />
    </bean>
    <bean class="org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor">
    	<property name="securityManager" value="true" />
    </bean>     -->
 
</beans>