<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns = "https://jakarta.ee/xml/ns/jakartaee"
         xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee https://jakarta.ee/xml/ns/jakartaee/web-app_6_0.xsd"
         id="DOOMSDAY-WEB" version="6.0">
  <display-name>LS-WEB</display-name>
<!--  <context-param>-->
<!--    <param-name>logbackConfigLocation</param-name>-->
<!--    <param-value>/WEB-INF/config/logback.xml</param-value>-->
<!--  </context-param>-->
  <context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>/WEB-INF/config/spring/applicationContext.xml</param-value>
  </context-param>
  <context-param>
    <param-name>logbackDisableServletContainerInitializer</param-name>
    <param-value>true</param-value>
  </context-param>
  <listener>
    <listener-class>com.lc.billion.icefire.web.spring.LogbackConfigListenerWithLogToApp</listener-class>
  </listener>
  <listener>
    <listener-class>com.lc.billion.icefire.web.spring.ContextLoaderListenerWithLogToApp</listener-class>
  </listener>
<!--   <listener>
    <listener-class>com.lc.billion.icefire.ServerSerletListener</listener-class>
  </listener>
 -->  
  	<!-- 配置shiro过滤器，且该过滤器必须配置在dispatcherServlet之前 ，DelegatingFilterProxy类会自动到Spring容器中
  	查找名字为shiroFilter的bean并把filter请求交给它处理 -->
	<filter>
	    <filter-name>shiroFilter</filter-name>
	    <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
	    <async-supported>true</async-supported>
	    <init-param>
	        <param-name>targetFilterLifecycle</param-name>
	        <param-value>true</param-value>
	    </init-param>
	</filter>
	<filter-mapping>
	    <filter-name>shiroFilter</filter-name>
	    <url-pattern>/*</url-pattern>
	</filter-mapping>

    <!-- Druid监控页面 -->
    <filter>
      <filter-name>DruidStatViewFilter</filter-name>
      <filter-class>com.alibaba.druid.support.jakarta.StatViewFilter</filter-class>
    </filter>
    <filter-mapping>
      <filter-name>DruidStatViewFilter</filter-name>
      <url-pattern>/druid/*</url-pattern>
    </filter-mapping>

   <!-- Druid Web监控页面 -->
    <filter>
      <filter-name>DruidWebStatFilter</filter-name>
      <filter-class>com.alibaba.druid.support.jakarta.WebStatFilter</filter-class>
      <init-param>
        <param-name>exclusions</param-name>
        <param-value>*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*</param-value>
      </init-param>
    </filter>
    <filter-mapping>
      <filter-name>DruidWebStatFilter</filter-name>
      <url-pattern>/*</url-pattern>
    </filter-mapping>



<!--   <filter>
    <filter-name>springSecurityFilterChain</filter-name>
    <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
  </filter>
  <filter-mapping>
    <filter-name>springSecurityFilterChain</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping> -->
  <filter>
    <filter-name>characterEncodingFilter</filter-name>
    <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>UTF-8</param-value>
    </init-param>
    <init-param>
      <param-name>forceEncoding</param-name>
      <param-value>true</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>characterEncodingFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  <servlet>
    <servlet-name>dispatcher</servlet-name>
    <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
    <init-param>
      <param-name>contextConfigLocation</param-name>
      <param-value>/WEB-INF/spring-servlet.xml</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
    <multipart-config>
      <max-file-size>524288000</max-file-size>
      <max-request-size>5242880000</max-request-size>
    </multipart-config>
  </servlet>
  <servlet-mapping>
    <servlet-name>dispatcher</servlet-name>
    <url-pattern>/</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>default</servlet-name>
    <url-pattern>*.js</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>default</servlet-name>
    <url-pattern>*.css</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>default</servlet-name>
    <url-pattern>*.xml</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>default</servlet-name>
    <url-pattern>*.jpg</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>default</servlet-name>
    <url-pattern>*.png</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>default</servlet-name>
    <url-pattern>*.gif</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>default</servlet-name>
    <url-pattern>*.map</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>default</servlet-name>
    <url-pattern>/fonts/*</url-pattern>
  </servlet-mapping>
  <session-config>
    <session-timeout>300</session-timeout>
  </session-config>
  <welcome-file-list>
    <welcome-file>index.jsp</welcome-file>
  </welcome-file-list>
</web-app>