<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.3.xsd     
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.3.xsd     
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.3.xsd     
       http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.3.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.3.xsd">

	<mvc:annotation-driven />
	<mvc:default-servlet-handler />

	<context:annotation-config />
	<context:component-scan base-package="com.lc.billion.icefire.web" use-default-filters="false">
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<bean id="stringConverter" class="org.springframework.http.converter.StringHttpMessageConverter">
		<property name="supportedMediaTypes">
			<list>
				<value>text/plain;charset=UTF-8</value>
			</list>
		</property>
	</bean>
	<bean id="jsonConverter" class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter"></bean>
	<bean class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter">
		<property name="messageConverters">
			<list>
				<ref bean="stringConverter" />
				<ref bean="jsonConverter" />
			</list>
		</property>
	</bean>
	<bean id="propertyConfigurer" class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>/WEB-INF/config/platform.properties</value>
			</list>
		</property>
		<property name="fileEncoding">
			<value>utf-8</value>
		</property>
	</bean>
	<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver" >
	   <property name="prefix" value="/jsp/" />
	   <property name="suffix" value=".jsp" />
	   <property name="redirectHttp10Compatible" value="false" />
	</bean>
	
	<bean id="multipartResolver" class="org.springframework.web.multipart.support.StandardServletMultipartResolver">
        <!-- 500M -->
        <property name="resolveLazily" value="true" />
    </bean>

	<!-- ViewController -->
	<mvc:view-controller path="/" view-name="index" />
	<mvc:view-controller path="/ok" view-name="ok" />
	<mvc:view-controller path="/console" view-name="console/index" />
	<mvc:view-controller path="/p/a/sendmail" view-name="p/a/sendmail" />
	<mvc:view-controller path="/p/a/getreward" view-name="p/a/getreward" />
	<mvc:view-controller path="/api" view-name="api" />
	<mvc:view-controller path="/about" view-name="about" />
	<mvc:view-controller path="/login" view-name="login" />
	<mvc:view-controller path="/unauthorized" view-name="console/unauthorized" />

	<mvc:interceptors>
		<!-- 局部拦截器 -->
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.lc.billion.icefire.web.LogInterceptor" />
		</mvc:interceptor>
	</mvc:interceptors>
</beans>