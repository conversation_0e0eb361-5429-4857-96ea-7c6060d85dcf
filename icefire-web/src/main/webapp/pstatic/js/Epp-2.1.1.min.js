
// phone_menu(".menu", '.top .wid');

wow()

// scroll_addclass(".top","active",90);

fontSize(1226)

function fontSize(design) {
    var w = $(window).width();
    if (w > design){
        var newWidth = design
    }else{
        var newWidth = w
    }
    $('html').css("fontSize", newWidth / (design / 100) + 'px')
}

$(window).resize(function () {
	fontSize(1226)
})

function readHTML(read, obj) {
    var _class = read.substring(1)
    $(obj.add).append('<div class="' + _class + '">' + $(read).html() + '</div>');

    $(obj.click_show).click(function (e) {
        var _wid = $(window).width()
        console.log(_wid);

        if (obj.max >= _wid) {
            e.preventDefault();
            $(this).toggleClass('active');
            $(this).next().slideToggle();
        }

    })
}

// 阻止冒泡
function prevent(hide_obj) {
    $(hide_obj).parent().click(function (event) {
        $(hide_obj).parent().hide();
    })
    $(hide_obj).click(function (event) {
        event.stopPropagation();
    });
}

function BubPro(main1, layout, event) {
	$(main1)[event](function (e) {

		$(this).fadeOut();
		e.stopPropagation();
	})
	$(main1).find(layout).click(function (e) {
		e.stopPropagation();
	})
}

// 锚记过渡动画
function Anchorage(obj,moveoff){
	$(obj).css('cursor','pointer')
	$(obj).click(function(e){
        e.preventDefault();
		$('html,body').animate({ scrollTop: ($($(this).attr('to')).offset().top) - moveoff},1000);
	});
}

// 返回顶部(隐藏显示)
function scrollHide(target,juli){
	var topv=$(juli).offset().top;
	$(window).scroll(function(){
		var v=$(window).scrollTop();
		if(v>=topv){
			$(target).fadeIn()
		}else{
			$(target).fadeOut()
		}
	})
}

// 计算高度
function calc(Oheight,Id_Obj){
	var win_height=$(window).height();
	var valt=Scroll(Id_Obj);
	$(".auto-height").css("cssText","height:"+(win_height-Oheight-valt)+"px !important;overflow-y:auto;");
	window.onresize=function(){
		valt=Scroll(Id_Obj);
		$(".auto-height").css("cssText","height:"+(win_height-Oheight-valt)+"px !important;overflow-y:auto;");
	};
}


// 获取滚动条的信息
function Scroll(ID){
	var obj=document.getElementById(ID);

	console.log('可视区(包滚动条)：'+obj.offsetHeight);
	console.log('可视区(不包滚动打)：'+obj.clientHeight);

	console.log('文档总高度：'+obj.scrollHeight);

	var ScrollHei=obj.offsetHeight-obj.clientHeight;

	console.log('滚动条的高度: '+ ScrollHei);

	if(obj.scrollHeight>obj.clientHeight){
		console.log("------------------------垂直滚动条");
	};

	if(obj.offsetHeight-obj.clientHeight>0){
		console.log("------------------------水平滚动条");
	};
	return ScrollHei
};

// 类名切换
function active(obj){
	$(obj).click(function(event) {
		$(this).toggleClass('active');
	});
	
}

// 设置手机端图片的高度
function setHei(obj,w,h){
	var bi=h/w;
	if($(window).width()<=750){
		var wid=parseInt($(".wid").width());
		$(obj).height(bi*wid)
	}
}

// 设置手机端图片的宽度
function setDev(obj){
	if($(window).width()<=750){
		var wid=parseInt($(".wid").width());
		$(obj).width(wid);
	}
}

//方本框中统计字数
function maxleng(obj_input,show_text){
	var max=parseInt($(show_text).text())
	$(obj_input).attr({maxlength:max});
	$(obj_input).keyup(function(){
		var len=$(this).val().length
		if(max-len>-1)
			$(show_text).text(max-len)
		log(len)
	})
}

// WoW动画封装库
function wow(ismob){
	ismob=ismob?ismob:false
	if (!(/msie [6|7|8|9]/i.test(navigator.userAgent))){
		var wow=new WOW({
			boxClass: 'wow',
			animateClass: 'animated',
			offset: 0,
			mobile: ismob,
			live: true,
		});
		wow.init();
		// data-wow-duration="5" //  动画持续时间
		// data-wow-duration="0.15s" //  动画延迟时间
		// data-wow-offset="50" //  元素的位置露出后距离底部多少像素执行
		// data-wow-iteration="4" //  动画执行次数
	}
}

//菜单计算全屏背景
function menu_screen(obj){
	auto();
	function auto(){
		var width=$(window).width();
		$(obj).each(function(index, el) {
			var leftV=$(this).offset().left;
			$(this).find('.boxs').css({"left":-leftV+'px',"width":width+'px'})
		});
	}

	$(window).resize(function(){
		auto();
		log(1)
	})
}

// 阻止冒泡
function stopPro(show,hide){
	$(show).click(function(event) {
		event.stopPropagation();
	});
	$('body').click(function(event) {
		event.stopPropagation();
		$(hide).hide();
	});
}

// 控制台输出
function log(text){
	console.log(text);
}

// 截取字符串
function substr(text){
	var arr=[]
	for(var i=0;i<text.length;i++){
		var te=text.substring(i,i+1)
		arr[i]=te
	}
	return arr;
}

// 按钮效果
$("button.halo").click(function(event) { //光晕效果
	var x=event.pageX-$(this).offset().left;
	var y=event.pageY-$(this).offset().top;
	$(this).append('<div class="effect" style="left:'+x+'px;top:'+y+'px;-webkit-animation:buthalo 1s;"></div>');
	_this=$(this);
	setTimeout(function(){
		console.log(_this);
		_this.find('.effect').remove();
	},2000)

});

// 有序替换图片
function tarpic(click_obj,target_obj){
    $(click_obj).click(function(){
    	var dee=$(this).index();
    	var path=$(this).find("img").attr("src");
    	$(target_obj).eq(dee).find("img").attr("src",path);
    })
}

// 画圆
function paint_cirl(obj_default_class,obj_change_class,start_color,end_color,main_class){
	var ite=0;
	var te=setInterval(function(){
			ite++;
			if(ite>=112 ||  ite>=111/$(main_class+" .sp1").text()*$(main_class+" .sp0").text()){clearInterval(te);}
		draw23(obj_change_class,ite,end_color)
		},10)
	draw23(obj_default_class,111,start_color);
	function draw23(id,ss,color) {
	 var canvas = document.getElementById(id);
	if (canvas == null) {
	 return false;
	 }
	var ctx = canvas.getContext("2d");
	ctx.lineWidth=20;
	ctx.strokeStyle=color
	ctx.beginPath();
	ctx.arc($("#"+id).width()/2,$("#"+id).width()/2,($("#"+id).width()-40)/4,0,ss/20);
	ctx.stroke();
	}
}

// 打字效果
function typing(textp){
	var num=0;
	$(textp).each(function(index, element) {
		var text=$(this).text();
		$(this).attr("data",text).text("");
		otime=$(textp).eq(index).attr("data").length*58; // 58 是速度值
		num=num+otime;//累加值
    	setTimeout(function(){
			abc($(textp).eq(index));
			},num-otime+1050);
    });
	function abc(obj){
		var strText=$(obj).attr("data");
		var i=0;
		var text="";
		var time=setInterval(function(){
				text=text+strText.substr(i,1)
				i++
				$(obj).html(text)
				if(i>=strText.length){
						clearInterval(time);
					}
			},58)
	}
}

// 下拉菜单效果
function hover_down_menu(oli_obj,showDiv){
	 $(oli_obj).hover(function(){
			var ind=null;
			var j=10;
			var olen=$(this).find(showDiv).show().find("a").length;
			var ind=$(this).index();
			for(var i=0;i<olen;i++){
			setTimeout(function(){
				$(oli_obj).eq(ind).find(showDiv+" a").eq(j-10).css({"transform":"translate(0,0px) rotate(0deg)","opacity":"1"});
				j=j+1;
				},50*i+50)
			}
		},function(){
				Flip(this);
			})
	$(oli_obj).each(function(index, element) {
       Flip(this);
    });
	function Flip(e){
			var ind=null;
			var j=10;
			var olen=$(e).find(showDiv).find("a").length;
			var ind=$(e).index();
			for(var i=0;i<olen;i++){
			setTimeout(function(){
				$(oli_obj).eq(ind).find(showDiv+" a").eq(j-10).css({"transform":"translate(0,-"+j+"px)  rotate("+-(80+j+5+j*5)+"deg)","opacity":"1"});
				j=j+1;
				},50*i+50)
				if(i==olen-1){
					setTimeout(function(){
						$(e).find(showDiv).hide();
						},50*i+140)
				}
			}
		}
	}

// 左右等高
function levelwith(obj_fr,obj_fl){
		var var_hei=$(obj_fr).height()+parseInt($(obj_fr).css("padding-bottom"));
		$(obj_fl).height(var_hei);
	}

// 图片列表向左滚动
function roll_picture(oLi,speed){
	$(oLi).parent().parent().css({"position":"relative"})
	var oLi_len=$(oLi).length; //个数
	var oLi_wid=$(oLi).width(); //宽度
	var oLi_marl=parseInt($(oLi).eq(1).css("marginLeft"));//第2个左边距
	$(oLi).parent().css({"position":"absolute","left":"0","width":oLi_len*(oLi_wid+oLi_marl)+"px"});
	var timer=setInterval(OnLeft,speed);
	var te=0;//移到的距离
	var num=0;//个数
	function OnLeft(){
		if(te==oLi_wid+oLi_marl+1){
			var oHtml=$(oLi).eq(num).html();
			$(oLi).parent().append('<li>'+oHtml+'</li>');
			$(oLi).eq(num).remove();
			
			te=-1;
			$(oLi).parent().css({"left":te+"px"});
			}
		te++;
		$(oLi).parent().css({"left":-te+"px"});
	}
	$(oLi).parent().hover(function () {
		clearInterval(timer);
	}, function () { 
		timer = setInterval(OnLeft, speed);
	})
}

function input_bd(obj_input,classN){//焦点加类
	$(obj_input).focus(function(){
		$(this).addClass(classN);
	})
	$(obj_input).blur(function(){
			$(this).removeClass(classN);
		})
	}

function zoom(){//整体缩小
	$(window).resize(function(){
			abtee();
		})
	abtee()
	function abtee(){
	var wid=$(window).width();
	if(wid<1366){
			$("html").css("zoom","0.8");
		}else{
				$("html").removeAttr("style");
			}
	}
}

// 单选
function radio_pic(obj){
	$(obj).click(function(){
		$(this).parent().find("i").removeClass("acti");
		$(this).find("i").addClass("acti");
	})
}

// 子菜单自适应大小
function  auto_subMenu(obj,find_obj){
	$(obj).each(function(index, element) {
		var sum=0;
		var otvel=0;
		var wit=parseInt($(this).find("a").css("paddingLeft"))*2;
		$(this).find("a").each(function(index, element) {
			var suw=$(this).width()
			sum=sum+suw
			otvel=index+1
		});
		$(this).width(sum+otvel*wit);
	});
}

// 拖动进度条
function dropmove(down,move,direction,obj_hei){
	$(move).mouseup(function(e){
			 if(direction==1){
				var X=e.pageX-$(this).offset().left;
				$(this).find(".box").width(X);
				}else{
						var Y=e.pageY-$(this).offset().top;
						$(this).find(".box").height(obj_hei-Y);
					}
		})
	$(down).mousedown(function(){

		$(move).mousemove(function(e){
				var t=$(this).offset().top;
				var lf=$(this).offset().left;
				 if(direction==1){
						 //console.log("横向");
						 if(e.pageX-lf<=$(move).width() && e.pageX-lf>0){
							$(this).find(".box").css("width",e.pageX-lf+"px");
						 }
				 }else{
					  //console.log("纵向");
						if(e.pageY-t<=$(move).height() && e.pageY-t>0){
						$(this).find(".box").css("height",obj_hei-(e.pageY-t)+"px");
					}
				$(move).unbind("hover");
				}
			})
	})
	$(move).mouseup(function(){
			$(move).unbind("mousemove");
		})
		$(down).mouseup(function(){
			$(move).unbind("mousemove");
		})
	$(move).hover(function(){

		},function(){
				$(move).unbind("mousemove");
			})

}

// 小图换成大图
function enlarge(click_obj,cg_obj){
	$(click_obj).click(function(){
			$(this).addClass('acti').siblings().removeClass('acti');
			var src=$(this).find("img").attr("src");
			$(cg_obj).attr("src",src);
		});
	}

// 目标活动倒计时
function TargetTime(element,divtype){
	ShowCountDown();
	var tid=0;
	tid=setInterval(ShowCountDown,1000);
 	function ShowCountDown()
    {
        var cc = $(element);
        var elem = $(element).find(".state");
        var arr = cc.attr("end-timer").split(":");
        var now = new Date();

        var endDate = new Date(arr[0], arr[1]-1, arr[2], arr[3],arr[4],arr[5]);
	    var leftTime=endDate.getTime()-now.getTime();

		if (leftTime>0){
			var leftsecond = parseInt(leftTime/1000);
			var day1=Math.floor(leftsecond/(60*60*24)).toString();
			var hour=Math.floor((leftsecond-day1*24*60*60)/3600).toString();
			var minute=Math.floor((leftsecond-day1*24*60*60-hour*3600)/60).toString();
			var second=Math.floor(leftsecond-day1*24*60*60-hour*3600-minute*60).toString();
			var hour=Math.floor((leftsecond-day1*24*60*60)/3600+24*day1).toString();
			if(day1<10) day1="0"+day1;
			if(hour<10) hour="0"+hour;
			if(minute<10) minute="0"+minute;
			if(second<10) second="0"+second;
			//var countDown = "倒计时 : "+day1+"天"+hour+"小时"+minute+"分"+second+"秒"
			//console.log(countDown);

			$(divtype+".t span.sp1").text(day1.substring(0, 1));
			$(divtype+".t span.sp2").text(day1.substring(2, 1));

			$(divtype+".s span.sp1").text(hour.substring(0, 1));
			$(divtype+".s span.sp2").text(hour.substring(2, 1));

			$(divtype+".f span.sp1").text(minute.substring(0, 1));
			$(divtype+".f span.sp2").text(minute.substring(2, 1));

			$(divtype+".m span.sp1").text(second.substring(0, 1));
			$(divtype+".m span.sp2").text(second.substring(2, 1));
		}
		else{
				clearInterval(tid);
				$(divtype+" span").text('0');
			}

	}

}

// 折叠展开
function slide_nav(obj,class_name,iname){
	$(obj).click(function(){
		if($(this).attr('opent')==="1"){
			$(this).parents('li').find(class_name).slideUp();
			$(this).find('i').removeClass(iname);
			$(this).attr('opent',"0");
			$(obj).removeClass('acti');

		}else{
			$(obj).removeClass('acti');
			$(this).addClass('acti');
			$(obj).find("i").removeClass(iname);
			$(obj).parent().find(class_name).slideUp();
			$(this).parent().find(class_name).slideDown();
			$(this).find('i').addClass(iname);
			$(obj).attr('opent',"0");
			$(this).attr('opent',"1");
			}
	});
}

// 点击展开
function shoutext(Oclick,Ofind){
	$(Oclick).click(function(){
		if($(this).attr("opent")=="1"){
				$(this).parent().find(Ofind).slideUp();
				$(Oclick).removeAttr("opent");
			}else{
			$(Oclick).removeAttr("opent");
			$(Oclick).parent().find(Ofind).slideUp();
			$(this).attr("opent","1").parent().find(Ofind).slideDown();
			}
		});
}

// 数量加减
function numadd(obj, click_left, click_right) {
	var max = $(click_right).attr('max');
	$(click_left).click(function(){
		i=parseInt($(obj).val());
		if(i>1){
				i--;
				$(obj).val(i);
			}
		if(i==1){
				$(this).addClass("noadd");
			}

	})
	$(click_right).click(function () {
		i = parseInt($(obj).val());
		if (max) {
			if (max > i) {
				i++;
			}
		} else {
			i++
		}
		$(click_left).removeClass("noadd");
		$(obj).val(i);
	})
}

// 图片上传后显示
function upimg(obj,append){
	$(obj).change(function(){
			$(append).append('<li><div class="pict"><i><img src="'+getFileUrl("upp")+'" alt=""></i></div></li>');
		})
	}

// 弹窗-显示和隐藏
function alertbox(click_show,click_hide,obj){
	$(click_hide).click(function(){
			$(obj).fadeOut();
		})
	$(click_show).click(function(){
			$(obj).fadeIn();
		})
}

// 复制命令操作
function copy(click_obj){
$(click_obj).click(function(){
		$(this).parents("li").find("textarea").select();
		document.execCommand("Copy");
	})
}

// 新闻垂直滚动
function newRoll(obj,cirl){
		$(obj).css({"position":"absolute"});
		var olent=$(obj).length;
		var j=0;
		var timter;
		for(var i=0;i<olent;i++)
		$(obj).eq(i).css({"top":i*$(obj).height()+"px"});
		timter=setInterval(stol,3000);
		$(obj).hover(function(){
				clearInterval(timter);
			},function(){
				timter=setInterval(stol,3000);
				})
		$(cirl).hover(function(){
				clearInterval(timter);
			},function(){
				timter=setInterval(stol,3000);
				})
		$(cirl).click(function(){
			j=$(this).index()-1;
			stol();
		})
		function stol(){
			j++;
			if(j>olent-1) j=0;
			$(cirl).eq(j).addClass("acti").siblings().removeClass('acti');
			$(obj).each(function(index, element) {
				var tp=parseInt($(this).css("top"));
                $(this).animate({"top":tp-$(obj).height()+"px"},500,function(){
						$(obj).eq(j-1).css("top",(olent-1)*$(obj).height()+"px")
					});
            });

		}
}

// 返回顶部
function myScroll() {
	var x=document.body.scrollTop||document.documentElement.scrollTop;
	var timer=setInterval(function(){
    x = x - 0.5 * (x/6);
	if(x<100)
	{
	x=0;
	window.scrollTo(x,x);
	clearInterval(timer);
	}
	window.scrollTo(x,x);
	},"1");
}

// 获取文件名
function getFileName(o){
    var pos=o.lastIndexOf("\\");
    return o.substring(pos+1);
}

// 获取File上传图片
function getFileUrl(sourceId) {
	var url;
	if (navigator.userAgent.indexOf("MSIE")>=1) { // IE
	url = document.getElementById(sourceId).value;
	} else if(navigator.userAgent.indexOf("Firefox")>0) { // Firefox
	url = window.URL.createObjectURL(document.getElementById(sourceId).files.item(0));
	} else if(navigator.userAgent.indexOf("Chrome")>0) { // Chrome
	url = window.URL.createObjectURL(document.getElementById(sourceId).files.item(0));
	}
	return url;
}

// 复选框选择
function addInput(obj){
	$(obj).click(function(){
		if($(this).find("input[type='checkbox']").is(':checked')==true){
					$(this).find("input").addClass("check");
				}else{
						$(this).find("input").removeClass("check");
					}
	});
}

// 复选框下的显示
function checkbox(obj,findbel){
	$(obj).click(function(){
		if($(this).find("input[type='checkbox']").is(':checked')==true){
			$(this).find(findbel).show();
		}else{
				$(this).find(findbel).hide();
			}
	})
}

// 两色图标点击切换
function cli_cgpic(obj){
	$(obj).click(function(){
			if($(this).attr("ter")=="1"){
				var path_img=$(this).find('img').attr('src');
				var new_path=path_img.replace("-1.",".");
				$(this).find('img').attr('src',new_path);
				$(this).attr("ter","0");
			}else{
			var path_img=$(this).find('img').attr('src');
			var new_path=path_img.replace(".","-1.");
			$(this).find('img').attr('src',new_path);
			$(this).attr("ter","1");
			}
		})
}

// 手机验证码倒计时
function Countdown(obj,input_obj,classname,num){
	$(obj).attr("value","1");
	var jte=num;
	var timer=null;
	function miuete(){
			jte--;
			if(jte<=0) {
			$(obj).removeClass(classname).text('重新获取');
				jte=num;
				clearInterval(timer);
				$(obj).attr("value","1");
			}
			$(obj+'.'+classname).text(jte+'s');
		}
	$(obj).click(function(e){
        e.preventDefault();
		if($(input_obj).val()!=''){
			if($(this).attr("value")=="1"){
				$(this).attr("value","0");
					$(this).addClass(classname);
					miuete();
					timer=setInterval(miuete,1000);
			}
        } else {
            alert('请输入手机号码!')
        }

	})
}


// 点击图标状态切换
function cli_cgimg(obj){
$(obj).click(function(){
		$(obj).each(function(index, element) {
            var path_img=$(this).find('img').attr('src');
			var new_path=path_img.replace("-1.",".");
			$(this).find('img').attr('src',new_path);
        });
		var path_img=$(this).find('img').attr('src');
		var new_path=path_img.replace(".","-1.");
		$(this).find('img').attr('src',new_path);
	})
}

// 切换图片  1.对象  2.索引值  3.图片名称
function click_cgimg(obj,index,imgname){
	$(obj).eq(index).click(function(){
	if($(this).attr('adimg')=="1"){
			$(this).attr('adimg','0');
			$(this).find('img').attr("src","img/"+imgname+".png");
		}else{
				$(this).find('img').attr("src","img/"+imgname+"-1.png");
				$(this).attr('adimg','1');
			}
	})
}

// 给对象添加类  1.点击的对象  2.类名  3.是否清除所有-非零[可选]
function addClass(obj,className,allt){
		if(allt>0){
		$(obj).click(function(){
				$(obj).removeClass(className);
				$(this).addClass(className);
			})
			}else{
					$(obj).click(function(){
					$(this).addClass(className).siblings().removeClass(className);
			})
				}
	}

// 三级菜单展开  1.点击的对象  2.展开的对象  3.添加类名旋转图标
function slideshow(click_show,obj_open,class_name){
$(click_show).click(function(){
		if($(this).attr('true')=="1"){
			$(this).parent().find(obj_open).slideUp();
			$(this).removeClass(class_name);
			$(this).attr('true',"0");
		}else{
				$(this).parent().find(obj_open).slideDown();
				$(this).addClass(class_name);
				$(this).attr('true',"1");
			}
	})
}

// 悬浮  1.点击的对象  2.移动的对象  3.距离右边的数值 4.类名 5.负值
function suspension(click_obj,move_obj,distance,className,negative){
	$(click_obj).click(function(){

		if($(this).attr('opene')=='1'){
				$(move_obj).animate({'right':-$(move_obj).width()-negative+"px"},600,'swing');
				$(this).attr('opene','0');
				$(this).removeClass(className);
			}else{
					$(this).addClass(className);
					$(move_obj).animate({'right':distance+"px"},600,'swing');
					$(this).attr('opene','1');
				}
	})
}

// 全屏左右滚动轮播 1.数量  2.左点击  2.右点击  3.小圆点点击
function full_screen(number,click_left,click_right,click_cirl){
	var olen1=$(number).length;
	var timer=0;
	var bowidth1=$('body').width();
	$(number).parent().css("position","absolute");
	$(number).parent().width(bowidth1*olen1+"px");
	var thinde1=0;

	$(number).css("width",bowidth1+"px");

	for(var j=0;j<olen1;j++){
			$(number).eq(j).css("left",j*bowidth1+"px");
		}
	$(click_right).click(function(){
			thinde1++;
			if(thinde1<olen1){
			$(number).parent().animate({"left":-thinde1*bowidth1+"px"},600,"swing");}
		})
	$(click_left).click(function(){
			if(thinde1>0){
				thinde1--;
			$(number).parent().animate({"left":-thinde1*bowidth1+"px"},600,"swing");
			}
		})

	$(click_cirl).click(function(){
			thinde1=$(this).index();
			$(this).addClass('acti').siblings().removeClass('acti');
			$(number).parent().animate({"left":-thinde1*bowidth1+"px"},600,"swing");
		})
	timer=setInterval(autocroll,5000);
	function autocroll(){
			thinde1++;
			if(thinde1>olen1-1) thinde1=0;

			$(click_cirl).eq(thinde1).addClass('acti').siblings().removeClass('acti');
			$(number).eq(thinde1).addClass('acti').siblings().removeClass('acti');
			$(number).parent().animate({"left":-thinde1*bowidth1+"px"},600,"swing");
		}
	$(click_cirl).hover(function(){
			clearInterval(timer);
		},function(){
				timer=setInterval(autocroll,5000);
			})

}

// 居中 1.对象  2. 0-[左边] 1-[右边]  3.宽度  4.偏移量
function autobox(obj,oble,value,pase){
	lrauto();
	$(window).resize(function(){
			 lrauto();
		})
	function lrauto(){
			var bwidt=$("body").width();
			if(oble==0){
					$(obj).css({left:(bwidt-value)/2+pase+"px"});
			}else{
				if(oble==1){
					$(obj).css({right:(bwidt-value)/2+pase+"px"})
					}
					else{
							alert('第二个传值出错！0是左 1是右');
						}
				}
		}
}

// 裁图后 垂直水平居中  1.图片对象
function Cutimg(obj){
	autoimg()
	$(window).resize(function(){
			 autoimg()
		})
	function autoimg(){
			var widt=$(obj).parent().width();
			var heit=$(obj).parent().height();
			var img_wid=$(obj).width();
			var img_hei=$(obj).height();
			$(obj).css({"left":(widt-img_wid)/2+"px","top":(heit-img_hei)/2+"px","position":"absolute"});
			$(obj).parent().css({"position":"relative","overflow":"hidden"});
		}
}

//弹出菜单  1.移上去的对象  2.隐藏的对象
function eject(hover_obj,hide_obj){
	$(hide_obj).hide();
	$(hover_obj).hover(function(){
		$(hide_obj).hide();
		$(hide_obj).eq($(this).index()).show();
	})
$(hide_obj).parent().hover(function(){

	},function(){
			$(hide_obj).fadeOut();
		})
}

//HOVER变颜色的图标 1.移上去的对象
function replaimg(obj){
	$(obj).hover(function(){
			var path_img=$(this).find('img').attr('src');
			var new_path=path_img.replace(".","-1.");
			$(this).find('img').attr('src',new_path);
		},function(){
			var path_img=$(this).find('img').attr('src');
			var new_path=path_img.replace("-1.",".");
			$(this).find('img').attr('src',new_path);
			})
	}

//当元素滚动到一定时就固定定位  1.定位的对象  2.加入一个类  3.默认值不可更改
function scrollTop(obj_fixed,ClassName,value,ohei_value){
	value=$(obj_fixed).offset().top;
	$(window).scroll(function(){
			var valt=$(window).scrollTop();
			var top=$(obj_fixed).offset().top;
			if(value==1){
					value=top;
				}
			if(valt>value+ohei_value){
				$(obj_fixed).addClass(ClassName);
				}else{
				$(obj_fixed).removeClass(ClassName);
				}
		})
	}

//选项卡切换  1.点击的对象  2.切换的的对象  3.事件
function tabs(Oobj,Otabch,event){
	$(Otabch).hide();
	var dex=$(Oobj).parent().find('.acti').index();
	$(Otabch).eq(dex).show();
		$(Oobj)[event](function(e){
                e.preventDefault();
				$(this).addClass('acti').siblings().removeClass('acti');
				$(Otabch).hide();
				$(Otabch).eq($(this).index()).show();
			});
}
// 遍历滚动
function RollEach(obj,sub,num){
	num=num?num:1;
	$(obj).each(function(index, el) {
		var _this=$(this);
		if(sub){
			Roll(_this.find(sub).find('ul li'),_this.find(sub).find(".prev"),_this.find(sub).find(".next"),_this.find(sub).find(".cirl span"),num);
		}else{
			Roll(_this.find('ul li'),_this.find(".prev"),_this.find(".next"),_this.find(".cirl span"),num);
		}
	});
}	
// 横向滚动
function Roll(number,prevBtn,nextBtn,Cirl_span,Scroll_num,type){
	Scroll_num=Scroll_num?Scroll_num:1
	var num = $(number).length;//元素的数
	if(num>1){
        var screen=num/Scroll_num
        var main_wid=parseInt($(number).parent().parent().outerWidth())
        // var marLeft=main_wid+parseInt($(number).css("margin-left"));
        var marLeft=$(number).outerWidth()+parseInt($(number).css("margin-left"));
        $(number).parent().css({"width":num*marLeft+"px","position":"absolute","top":"0"}).parent().css({"position":"relative","overflow":"hidden"});/*计算宽度initial*/
        var one=0;
        this.debug=function(){
            console.log('滚动个数=>'+num+'\n对象宽度=>'+$(number).outerWidth() + '\n左边距=>' + parseInt($(number).css("margin-left")))
        }

        for(var i=0;i<screen;i++){
            if(type!='num'){
                $(Cirl_span).append('<span></span>')
            }else{
                $(Cirl_span).append('<span>'+(i+1)+'</span>')
            }
        }
        var j=0;//统计
        var setotimer=null;//时间变量
        setotimer=setInterval(next,3000)  //自动下一个

        var prev_state=true;
        var next_state=false;

        $(Cirl_span).find('*').eq(j).addClass('acti').siblings().removeClass('acti');
        
        $(Cirl_span).find('*').click(function(event) {
            var dex=$(this).index();

            if(dex>j){
                var res=dex-j
                $(number).parent().animate({"left":-marLeft*(Scroll_num*res)+"px"},600,function(){
                    $(this).css("left","0px");
                    for(var i=0;i<Scroll_num*res;i++){
                        var $one_li=$(number).first()
                        var $two_li=$(number).last()
                        $one_li.insertAfter($two_li);    //移动节点
                    }
                })
            }else if(dex<j){
                    var res=dex-j
                    log(res)
                    for(var i=0;i<Scroll_num*-res;i++){
                        var $one_li=$(number).first()
                        var $two_li=$(number).last()
                        $two_li.insertBefore($one_li);
                    }
                    $(number).parent().css("left",-marLeft*Scroll_num*-res+"px").animate({"left":"0"},600)
            }

            j=dex
            $(Cirl_span).find('*').eq(j).addClass('acti').siblings().removeClass('acti');
        });
	
	
        function next(){
            prev_state=true;
            if(next_state){
                log(next_state)
                var $one_li=$(number).first()
                var $two_li=$(number).last()
                $one_li.insertAfter($two_li)
                $(number).parent().css("left","0");
                next_state=false;
            }
            j++;
            if(j>screen-1) j=0;
            $(Cirl_span).find('*').eq(j).addClass('acti').siblings().removeClass('acti');
            $(number).parent().animate({"left":-marLeft*(Scroll_num)+"px"},600,function(){
                $(this).css("left","0");
                for(var i=0;i<Scroll_num;i++){
                    var $one_li=$(number).parent().find('li').first()
                    var $two_li=$(number).parent().find('li').last();
                    $one_li.insertAfter($two_li);    //移动节点
                }
            })
        }

        function prev(){
            next_state=true;
            if(prev_state){
                for(var i=0;i<Scroll_num;i++){
                    var $one_li=$(number).parent().find('li').first()
                    var $two_li=$(number).parent().find('li').last();
                    $two_li.insertBefore($one_li);    //移动节点
                }
                $(number).parent().css("left",-marLeft*(Scroll_num)+"px");
                prev_state=false;
            }
            j--;
            if(j<0) j=screen-1;
            $(Cirl_span).find('*').eq(j).addClass('acti').siblings().removeClass('acti');
            $(number).parent().animate({"left":"0"},600,function(){
                $(this).css("left",-marLeft*(Scroll_num)+"px");
                for(var i=0;i<Scroll_num;i++){
                    var $one_li=$(number).parent().find('li').first()
                    var $two_li=$(number).parent().find('li').last();
                    $two_li.insertBefore($one_li);    //移动节点
                }
            })
        }

        $(number).hover(function() {
            clearInterval(setotimer)
        }, function() {
            setotimer=setInterval(next,3000)  //自动下一个
        });
        $(Cirl_span).find('span').hover(function() {
            clearInterval(setotimer)
        }, function() {
            setotimer=setInterval(next,3000)  //自动下一个
        });

        $(prevBtn).hover(function() {
            clearInterval(setotimer)
        }, function() {
            setotimer=setInterval(next,3000)  //自动下一个
        });
        $(nextBtn).hover(function() {
            clearInterval(setotimer)
        }, function() {
            setotimer=setInterval(next,3000)  //自动下一个
        });


        //上一个
        $(prevBtn).click(function(){
            prev()
        })

        //下一个
        $(nextBtn).click(function(){
            next()
        })
        
    }

}

// 下拉列表	1.点击获取文本内容  2.点击对像显示下拉  3.下拉这个Div对像 4.事件 5.是否是文字标签
function down_drop(a,b,c,d,e){
    
	$(a).click(function(){
        $(a).removeClass('acti');
        $(this).addClass('actactivei');
        var text=$(this).text();
        if(e==1) {
            $(this).parent().parent().find('var').text(text);
        }else{
            $(this).parent().parent().find('input').val(text);
        }
        $(this).parent().hide();
    })

    $(b)[d](function(){
        $(this).parent().addClass('active').find(c).show();
    })

    $(a).parent().parent().hover(function(){

    },function(){
        $(this).removeClass('active').find(c).hide();
    })
}

// 淡入淡出轮播  1.切换的对像	2.小圆点控制的对像  3.时间值[必选]  4.左点击  5.右点击 6.文字部分切换
function fade(banner_ul_li,cirl,timer,click_left,click_right,cg_text){
	var i=0;
	var Otimer=null;
	var olen=$(banner_ul_li).length;
	$(banner_ul_li).css("opacity","0");
	$(banner_ul_li).eq(i).addClass('acti').siblings().removeClass('acti');
	$(banner_ul_li).eq(i).css("zIndex","1").animate({"opacity":"1"},600,'swing');
	$(cirl).click(function(){
			$(this).addClass('acti').siblings().removeClass('acti');
			i=$(this).index();
			$(cg_text).eq(i).addClass("acti").siblings().removeClass("acti");
			$(banner_ul_li).eq(i).addClass('acti').siblings().removeClass('acti');
			$(banner_ul_li).eq(i).css("zIndex","1").animate({"opacity":"1"},600,'swing').siblings().css("zIndex","0").animate({"opacity":"0"},600,'swing');
		})
	var bannNext=function(){
		i++;
		if(i>olen-1) i=0;
		$(cg_text).eq(i).addClass("acti").siblings().removeClass("acti");
		$(banner_ul_li).eq(i).addClass('acti').siblings().removeClass('acti');
		$(cirl).eq(i).addClass('acti').siblings().removeClass('acti');
		$(banner_ul_li).eq(i).css("zIndex","1").animate({"opacity":"1"},600,'swing').siblings().css("zIndex","0").animate({"opacity":"0"},600,'swing');
	}
	var bannPrev=function(){
		i--;
		if(i<0) i=olen-1;
		$(cirl).eq(i).addClass('acti').siblings().removeClass('acti');
		$(banner_ul_li).eq(i).addClass('acti').siblings().removeClass('acti');
		$(cg_text).eq(i).addClass("acti").siblings().removeClass("acti");
		$(banner_ul_li).eq(i).css("zIndex","1").animate({"opacity":"1"},600,'swing').siblings().css("zIndex","0").animate({"opacity":"0"},600,'swing');
	}
	this.but_next=function(){
		bannNext();
	}
	this.but_prev=function(){
		bannPrev();
	}

	Otimer=setInterval(function(){
		bannNext();
	},timer);

	$(cirl).hover(function(){
			clearInterval(Otimer);
		},function(){
			Otimer=setInterval(function(){
				bannNext();
			},timer);
	})
	$(click_left).hover(function(){
			clearInterval(Otimer);
		},function(){
			Otimer=setInterval(function(){
				bannNext();
			},timer);
	})
	$(click_right).hover(function(){
			clearInterval(Otimer);
		},function(){
			Otimer=setInterval(function(){
				bannNext();
			},timer);
	})
	$(click_left).click(function(){
			bannPrev();
		})
	$(click_right).click(function(){
			bannNext();
		})
}

// 单击展开
function slideDown(obj,show_obj){
	$(obj).click(function(event) {
		if($(this).attr("eee")=="1"){
			$(this).removeAttr('eee')
			$(this).removeClass('sp1').find('i').removeClass('acti');
			$(this).parent().find(show_obj).slideUp();
		}else{
			$(this).attr("eee","1")
			$(this).addClass('sp1').find('i').addClass('acti');
			$(this).parent().find(show_obj).slideDown();

		}

	});
}

//滚动条到一定的时候加上类名
function scroll_addclass(obj_fixed, class_name, value) {

	if (typeof (value) =='number') {
		console.log('99')
	}else {
		value = $(obj_fixed).offset().top;
	}
	$(window).scroll(function(event) {
		var vlte=$(window).scrollTop();
		if(vlte>=value){
			$(obj_fixed).addClass(class_name);
		}
		else{
			$(obj_fixed).removeClass(class_name);
		}
	});
}

// 调用事件
function call_resize(obj){
	eval(obj);
    $(window).resize(function(event) {
	    eval(obj);
    });
}

// 手机自动宽
function min_auto(obj,wid) {
		var scr_wid=$(window).width();
		if(scr_wid<=wid){
			$(obj).width(scr_wid);
		}else{
			$(obj).removeAttr('style');
		}
	}

// 表格js
function table(left_obj,right_obj,ife){
	var Objwid=$(right_obj).width();
	$(left_obj).removeAttr('style');
	$(right_obj).removeAttr('style');

	$(left_obj).each(function(index, el) {
		var leftHei=$(this).height();
		var rightHeight=$(right_obj).eq(index).height();
		if(leftHei>rightHeight){
			$(right_obj).eq(index).height(leftHei);
			$(right_obj).eq(index).find('span').css("line-height",rightHeight+'px');
		}else{
			$(this).height(rightHeight);
			$(this).find('span').css("line-height",rightHeight+'px');
			

			log(ife)
			if(ife==1){
				$(this).css("line-height",rightHeight+'px');
			}
		}
	});
	$(right_obj).find('p').width(Objwid);
}

// 三级菜单下拉
function three_menu(obj_li,one,two,three){
	$(obj_li).hover(function() {
		$(this).stop().find(one).slideDown(200);
	}, function() {
		$(this).stop().find(one).slideUp(200);
	});
	console.log(obj_li+" "+one+" "+two);
	$(obj_li+" "+one+" "+two).hover(function() {
		$(this).stop().find(three).slideDown(200);
	}, function() {
		$(this).stop().find(three).slideUp(200);
	});
}

// 页面跳转
function IsPC(url,is) {
    var userAgentInfo = navigator.userAgent;
    var Agents = ["Android", "iPhone","SymbianOS", "Windows Phone","iPad", "iPod"];
    var flag = false;
    for (var v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
            flag = true;
            break;
        }
    }
	if (flag){
			console.info("Current phone mode");
			if(is){
				window.location.href=url;
			}
		}else{
			console.info("Current computer model");
			}
}

//上传显示图片 1. input控件对象 2.显示的img对象 3.显示的名称对象
function showFile(put_obj,show_img,show_name){
	$(put_obj).change(function(event) {
		var e=getFileUrl($(this).attr("id"));
		$(show_img).attr("src",e);
		$(show_name).val(getFileName($(this).val()));
	});
}

// 3D 旋转
function bann3d(li_list,click_left,click_right){
	var timer=null;
	var li_size=$(li_list).size();
	timer=setInterval(next,3000);
	console.info();
	$(li_list).parent().parent().hover(function() {
		clearInterval(timer);
	}, function() {
		timer=setInterval(next,3000);
	});
	$(li_list).each(function(index, el) {
		$(this).attr("index",index);
	});
	$(li_list).hover(function() {
		var inde=$(this).attr("index");
		if(inde<li_size/2){
			//console.log('左');
			$(this).parent().parent().addClass('onLeft');
		}else{
			//console.log('右');
			$(this).parent().parent().removeClass('onLeft');
		}
	});
	$(click_right).click(function(event) {
		next();
	});
	function next(){
		var mycars=new Array(li_size);
		var mycars1=new Array(li_size);
		$(li_list).each(function(index, el) {
			var class1=$(this).attr("class");
			var index1=$(this).attr("index");
			mycars[index]=class1;
			mycars1[index]=index1;
			if(index!=0){
				$(li_list).eq(index-1).attr("class",class1);
				$(li_list).eq(index-1).attr("index",index1);
			}
		});
		$(li_list+":last").attr('class',mycars[0]);
		$(li_list+":last").attr('index',mycars1[0]);
	}
	function prev(){
		var mycars=new Array(li_size);
		var mycars1=new Array(li_size);
		$(li_list).each(function(index, el) {
			var class1=$(this).attr("class");
			var index1=$(this).attr("index");
			mycars[index]=class1;
			mycars1[index]=index1;
			if(index==0){
				$(li_list+":first").attr('class',$(li_list+":last").attr("class"));
				$(li_list+":first").attr('index',$(li_list+":last").attr("index"));
			}else{
				$(li_list).eq(index).attr("class",mycars[index-1]);
				$(li_list).eq(index).attr("index",mycars1[index-1]);
			}
		});
	}
	$(click_left).click(function(event) {
		prev();
	});
	$(li_list).click(function(event) {
		var inde=$(this).attr("index");
		if(inde>3){
			for(var i=0;i<li_size-inde;i++){
				next();
			}
		}else{
			for(var i=0;i<li_size-inde;i++){
				next();
			}
		}
	});
}

// 手机滑屏事件
function operation(main_div,event_name){
	var start_pointX=0;
	var end_pointX=0;
	var start_pointY=0;
	var end_pointY=0;
	var pagejishu=0;
	this.calculation=function(){
		this.start_point=start_pointX;
		this.end_point=end_pointX;
		this.num_Index=pagejishu;
	}
	$(main_div).on('touchstart', function(event) {
		event.stopPropagation();
		var _touch=event.originalEvent.targetTouches[0];
		start_pointX=_touch.pageX;
		start_pointY=_touch.pageY;
		// console.log("L起点："+start_pointX);
		// console.log("T起点："+start_pointY);
	});
	$(main_div).on('touchmove', function(event) {
		event.preventDefault();
	});
	$(main_div).on('touchend', function(event) {
		event.stopPropagation();
		var _touch=event.originalEvent.changedTouches[0];
		end_pointX=_touch.pageX;
		end_pointY=_touch.pageY;
		// console.log("T终点："+end_pointX);
		// console.log("L终点："+end_pointY);
		if(event_name.direction){
			isLeft();
		}else{
			isTop();
		}
		_Event.calculation();
	});
	function isLeft(){
		//console.log("左右移动的数值是："+(end_pointX - start_pointX));
		if(end_pointX-start_pointX>100){
			console.info('向右');
			eval(event_name.prevBut+'()');
			pagejishu++;
		}else if(end_pointX-start_pointX<-100){
			console.info('向左');
			eval(event_name.nextBut+'()');
			pagejishu--;
		}
	}//isLeft()
	function isTop(){
		//console.log("上下移动的数值是："+(end_pointY - start_pointY));
		if(end_pointY-start_pointY>100){
			console.info('向下');
			pagejishu++;
		}else if(end_pointY-start_pointY<-100){
			console.info('向上');
			pagejishu--;
		}
	} //isTop()
}

// 点赞
function zan(click_obj,className,text1,text2){
	$(click_obj).click(function(event) {
		if($(this).find('i').attr("class")){
			$(this).find('i').removeAttr("class");
			$(this).find('var').text(text1);
		}else{
			$(this).find('i').addClass(className);
			$(this).find('var').text(text2);
		}
	});
}
// 点击滚动到对象位置
function click_scroll(click,scrll){
	$(click).click(function(event) {
	var hei=$(scrll).offset().top;
	var i=0;
	var j=0;
	var tee=setInterval(function(){
		i+=j;
		if(i%2==0){
			j++;
		}
		if(i>=hei) clearInterval(tee);
		window.scrollTo(0,i);
	},10)
	});
}

// 生成手机模式菜单
function phone_menu(read_div,add_pos,pos_line,isqian){
    isqian = isqian ? 0 : 1
    $(add_pos).after('<div class="min-menu">'+$(read_div).html()+'</div>');

    if (isqian){
        $('<div class="min-nav"><i></i></div>').insertBefore($(pos_line))
    }else{
        $(pos_line).append('<div class="min-nav"><i></i></div>');
    }

	$(".min-menu ul li>a").append('<i></i>');
	$(".min-menu ul li").each(function(index, el) {
		if($(this).find('.down').length > 0){
			$(this).find('a i').addClass('ico-arrow');
		}
	});
    $(".min-menu ul li > a").on("click", function (e) {
        if ($(this).find('.ico-arrow').length > 0) {
            e.preventDefault();
            $(this).parents('li').find('.down').slideToggle();
            $(this).find('.ico-arrow').toggleClass('rogate');
        }
    })
	$(".min-nav").click(function(){
		$(this).toggleClass('active');
		$(".min-menu").slideToggle();
	})
}

// 检测设备
function device(){
	var u = navigator.userAgent;
	var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
	var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
	if($(window).width()>=1024){
		window.location.href="../.."; //PC
		return "pc";
	}
	if(isAndroid){
		window.location.href="../.."; //安卓
		return "android";
	}
	if(isiOS){
		window.location.href="../.."; //苹果
		return "phone";
	}
	if(navigator.userAgent.indexOf('UCBrowser') > -1) {
		alert("uc浏览器");
	}
}

// 移动窗口
function moveTop(down_obj,obj_Width,out_obj){
	var down=false;
	var x=0,y=0;
	var objleft=$(out_obj).offset().left;
	var objtop=$(out_obj).offset().top;

	var maxwidth=$(out_obj).outerWidth()-$(obj_Width).outerWidth();
	var maxheight=$(out_obj).outerHeight()-$(obj_Width).outerHeight();
	$(obj_Width).css({"left":maxwidth/2+"px","top":maxheight/2+"px"})
	$(down_obj).on("mousedown",function(event) {
		$(this).css("cursor","move");
		down=true;
		event.preventDefault();
		x=event.clientX-$(this).parent().offset().left;
		y=event.clientY-$(this).parent().offset().top;
	});

	$("html").on("mousemove",function(event) {
		event.preventDefault();
		if(down){
			l=event.clientX-x-objleft;
			t=event.clientY-y-objtop;
			if(l<0){
				l=0;
		   }
		   if(t<0){
				t=0;
		   }
		   if(l>maxwidth){
				l=maxwidth;
		   }
		   if(t>maxheight){
				t=maxheight;
		   }
		   $(obj_Width).css({"left":l+"px","top":t+"px"});
		}
	});

	$("html").on("mouseup",function(event) {
		down=false;
		$(down_obj).css("cursor","default");
	});
}

//判断是否是微信浏览器的函数
function isWeiXin(){
	var ua = window.navigator.userAgent.toLowerCase();
	if(ua.match(/MicroMessenger/i) == 'micromessenger'){
		return true;
	}else{
		return false;
	}
}

// 计数器
function NumberPlus(obj,num){
	$(obj).each(function(index, el) {
		var get=parseInt($(this).attr("data-num"));
		abs($(this),get)
	});
	function abs(obj1,nume){
		var i=0;
		var time=setInterval(jia,15);
		function jia(){
			i++
			obj1.text(i);
			if(i>=nume) clearInterval(time);
		}
	}
}