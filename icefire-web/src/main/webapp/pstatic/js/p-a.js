var cn = [
    "角色ID",
    "",
    "礼品码",
    "输入内容",
    "查看操作说明",
    "领奖",
    "发送验证"
]
var cnSend = {
    "0":"验证邮件发送成功，请前往游戏内查看邮件。",
    "-8":"该角色不存在，请前往游戏内查看你的角色ID。",
}
var cnGet = {
    "-2":"该礼品码已失效",
    "-3":"该礼品码已被使用",
    "-4":"你已经兑换过同类礼品码",
    "-1":"该礼品码不存在",
    "0":"兑换成功,请前往游戏通过邮件领取此奖励",
    "-9":"验证已失效，请重新发送验证"
}

var tw = [
    "角色ID",
    "",
    "禮品碼",
    "輸入內容",
    "查看操作說明",
    "領獎",
    "發送驗證",
]
var twSend = {
    "0" :"驗證郵件發送成功，請前往遊戲內查看郵件。",
    "-8":"該角色不存在，請前往遊戲內查看你角色ID。",
}

var twGet = {
    "-2": "該領品碼已失效",
    "-3":"該禮品碼已被使用",
    "-4":"你已經兌換過同類禮品碼",
    "-1":"該禮品碼不存在",
    "0":"兌換成功。請前往遊戲通過郵件領取此獎勵",
    "-9":"驗證已失效，請重新發送驗證"
}

var en = [
    "Character ID",
    "",
    "Gift code",
    "Tap to enter",
    "View instructions",
    "Redeem",
    "Send verification",
]
var enSend = {
    "0" :"The verification mail was sent successfully, please login to the game and check your in-game mail.",
    "-8":"The character doesn't exist, please login to the game and check your character ID.",
}

var enGet = {
    "-2":"Gift code has expired",
    "-3":"Someone else has already used this code",
    "-4":"You have already used this gift code",
    "-1":"Gift code does not exist",
    "0":"Gift code redeemed successfully! Please login to the game to claim your gift in your in-game mail.",
    "-9":"Verification has expired, please send verification again."
}

var ru = [
    "ID персонажа",
    "",
    "Подарочный код",
    "Нажмите для ввода",
    "Как использовать?",
    "Получить",
    "Подтвердить аккаунт"
]

var ruSend = {
    "0" :"Письмо с подтверждением успешно отправлено, пожалуйста, войдите в игру и проверьте внутриигровую почту.",
    "-8":"Персонаж не найден, пожалуйста, войдите в игру и проверьте ID персонажа.",
}

var ruGet = {
    "-2":"Срок действия подарочного кода истек",
    "-3":"Кто-то уже использовал этот подарочный код",
    "-4":"Вы уже использовали этот подарочный код",
    "-1":"Такого подарочного кода не существует",
    "0":"Подарочный код успешно использован! Пожалуйста, войдите в игру и получите ваш подарок по внутриигровой почте.",
    "-9":"Срок подтверждения истек, пожалуйста, отправьте еще раз."
}

var de = [
    "Charakter ID",
    "",
    "Gutscheincode",
    "Tippen sie zum Öffnen",
    "Sehe Anleitung",
    "Sammeln",
    "Sende Bestätigung",
]

var deSend = {
    "0" :"Die Bestätigungsmail wurde erfolgreich versand, bitte betrete das Spiel und überprüfe die eMail.",
    "-8":"Der Charakter existiert nicht, bitte betrete das Spiel und überprüfe die Charakter ID.",
}

var deGet = {
    "-2":"Der Gutscheincode ist abgelaufen",
    "-3":"Jemand anderer hat diesen Code bereits verwendet",
    "-4":"Du hast diesen Gutscheincode bereits verwendet",
    "-1":"Dieser Gutscheincode ist ungültig",
    "0":"Die Übermittlung war erfolgreich Bitte betrete das Spiel um deine Belohnung per Mail zu empfangen.",
    "-9":"Bestätigung ist abgelaufen, bitte sende erneut eine Bestätigung."
}

var jp = [
    "キャラクターID",
    "",
    "ギフトコード",
    "入力してください",
    "操作説明を確認",
    "受け取る",
    "確認メールの送信",
]

var jpSend = {
    "0" :"確認メールを送信しました、ゲーム内でご確認ください。",
    "-8":"このキャラクターは存在しません、ゲーム内で再度キャラクターIDをご確認ください。",
}

var jpGet = {
    "-2":"このギフトコードの有効期限が切れています",
    "-3":"このギフトコードはすでに使用されています",
    "-4":"すでに同様のギフトコードを利用しました",
    "-1":"このギフトコードは存在しません",
    "0":"交換成功しました。報酬はゲーム内のメールにて送信されます",
    "-9":"有効期限が切れています。再度操作を行ってください"
}

var kr = [
    "캐릭터 ID",
    "",
    "선물코드",
    "입력 내용",
    "조작 설명 보기",
    "보너스 수령",
    "인증 발송",
]

var krSend = {
    "0" :"인증 메일 발송 성공, 게임 내 메일을 확인해 보세요.",
    "-8":"해당 캐릭터가 존재하지 않습니다, 게임 내에서 당신의 캐릭터ID를 확인하세요.",
}

var krGet = {
    "-2":"해당 선물 코드는 사용할 수 없습니다",
    "-3":"해당 선물 코드는 이미 사용된 코드입니다",
    "-4":"생존자님께서는 이미 동일한 종류의 선물 코드를 사용하셨습니다",
    "-1":"해당 선물 코드는 존재하지 않습니다",
    "0":"교환 성공, 게임 내 우편을 통해 선물을 수령하세요",
    "-9":"인증번호 무효, 인증번호를 재발송해주세요",
}

var pt = [
    "ID do Personagem",
    "",
    "Código de Recompensa",
    "Toque para entrar",
    "Ver instruções",
    "Resgatar",
    "Enviar verificação",
]

var ptSend = {
    "0" :"O e-mail de verificação foi enviado com sucesso, faça login no jogo e verifique o e-mail.",
    "-8":"O personagem não existe, faça login no jogo e verifique o ID do seu personagem.",
}

var ptGet = {
    "-2":"O código expirou",
    "-3":"Alguém já usou este código",
    "-4":"Você já usou este código",
    "-1":"Este código não existe",
    "0":"Código utilizado com sucesso! Faça login no jogo para resgatar seu presente no e-mail.",
    "-9":"A verificação expirou, por favor envie novamente.",
}


var es = [
    "ID de Personaje",
    "",
    "Código de Regalo",
    "Toca para entrar",
    "Ver Instrucciones",
    "Reclamar",
    "Enviar Verificación",
]

var esSend = {
    "0" :"El correo de verificación ha sido enviado. Por favor entra al juego y revisa tu correo.",
    "-8":"El personaje no existe, por favor entra de nuevo al juego y revisa que el ID de personaje es correcto.",
}

var esGet = {
    "-2":"El Código ha expirado",
    "-3":"Alguien mas ya ha usado este código",
    "-4":"Tu ya has usado este código",
    "-1":"Este código no existe",
    "0":"Código reclamado exitosamente! Por favor entra al juego para reclamar tus recompensas en tu correo",
    "-9":"La verificación ha expirado, por favor envíala de nuevo",
}

var tha = [
    "IDตัวละคร",
    "",
    "รหัสของขวัญ",
    "ใส่เนื้อหา",
    "อธิบายดำเนินการ",
    "รับรางวัล",
    "ส่งรหัสยืนยัน",
]

var thaSend = {
    "0" :"ส่งอีเมลยืนยันสำเร็จ กรุณาไปตรวจสอบที่อีเมล",
    "-8":"ไม่พบตัวละครนี้ กรุณาไปตรวจสอบIDตัวละครในเกม",
}

var thaGet = {
    "-2":"รหัสของขวัญนี้ล้มเหลว",
    "-3":"รหัสของขวัญนี้ถูกใช้แล้ว",
    "-4":"คุณเคยแลกรหัสรางวัลนี้แล้ว",
    "-1":"ไม่พบรหัสรางวัลนี้",
    "0":"แลกสำเร็จ กรุณาไปรับรางวัลที่อีเมลเกม",
    "-9":"ยืนยันรหัสล้มเหลว กรุณาส่งรหัสอีกครั้ง",
}


var langList = [cn,tw,en,ru,de,jp,kr,pt,es,tha]
var langSendList = [cnSend,twSend,enSend,ruSend,deSend,jpSend,krSend,ptSend,esSend,thaSend]
var langGetList = [cnGet,twGet,enGet,ruGet,deGet,jpGet,krGet,ptGet,esGet,thaGet]

var langIndex = 2;

$('.main .wid .conts .language .box').click(function() {
    $('.slide').fadeIn();
})
$('.main .wid .conts .language .slide p').click(function() {
    $('.slide').fadeOut();
    langIndex = $(this).index();
    var lang = langList[langIndex];
    var m = $(this).text();
    $('.main .wid .conts .language .box .txt').text(m);
    $('.main .wid .conts .id .tits1').text(lang[0]);
    $('.main .wid .conts .id .tits2').text(lang[2]);
    $('.main .wid .conts .id .input input').attr('placeholder', lang[3]).focus()
    $('.main .wid .conts .id .instructions a').text(lang[4])
    $('.main .wid .conts .id .button button').text(lang[5])
    $('.main .wid .conts .id .tits3').text(lang[0]);
    $('.main .wid .conts .id .button.sendBtn button').text(lang[6])
})

//$('.main .wid .conts .id .button.sendBtn button').click(function() {

$('#sendBtn').click(function() {
    var roleDisplayId = $('.main .wid .conts .id .input input').val();
    var lang = langList[langIndex];

    if (roleDisplayId && /^[A-Fa-f0-9]{2}$/.test(roleDisplayId.substr(roleDisplayId.length - 2))) {
        $.ajax({
            url: '/p/a/postSendmail',
            type: 'POST',
            data: {
                roleDisplayId: roleDisplayId
            },
            beforeSend: function(xhr) {
                $('#sendBtn').attr({disabled: "disabled"});
            },
            complete: function(result) {
                setTimeout(function () {
                    $('#sendBtn').removeAttr("disabled");
                },600);
            }
        }).done(function (result) {
            var json = jQuery.parseJSON(result);
            var code = json.ret;
            var langSend = langSendList[langIndex];
            showMsg(langSend[code],2400);
        });
    } else {
        showMsg(lang[3],1200);
    }
})


//$('.main .wid .conts .id .button.getBtn button').click(function() {
$('#getBtn').click(function() {
    var giftCode = $('#giftCode').val();
    var serverId = $('#serverId').val();
    var roleId = $('#roleId').val();
    var lang = langList[langIndex];

    if (giftCode) {
        $.ajax({
            url: '/p/a/exchangeCdKey',
            type: 'POST',
            data: {
                serverId:serverId,
                roleId:roleId,
                cdkey: giftCode
            },
            beforeSend: function(xhr) {
                $('#getBtn').attr({disabled: "disabled"});
            },
            complete: function(result) {
                setTimeout(function () {
                    $('#getBtn').removeAttr("disabled");
                },600);
            }
        }).done(function (result) {
            var json = jQuery.parseJSON(result);
            var code = json.ret;
            var langGet = langGetList[langIndex];
            showMsg(langGet[code],2400);
        });
    } else {
        showMsg(lang[3],1200);
    }
})


function getDocReady() {
    var ret = $('#ret').val();
    var langGet = langGetList[langIndex];
    if (ret !== "1") {
        showMsg(langGet[ret],0);
        $('#getBtn').attr("onclick",'').unbind('click');
        $('#giftCode').attr('readonly',true);
    }
}

function showMsg(msg,times) {
    $('.alerts .text').text(msg);
    $('.alerts').removeClass("alerts hide").addClass("alerts show");
    if (times > 0) {
        setTimeout(function () {
            $('.alerts').removeClass("alerts show").addClass("alerts hide");
        },times);
    }
}