@font-face {
  font-family: 'RM';
  src: url("../font/Roboto-Medium.ttf");
}
@font-face {
  font-family: 'RR';
  src: url("../font/Roboto-Regular.ttf");
}


/*  页面重置样式  */
body,p,h1,h2,h3,h4,h5,li,span,i,ul,img,a,strong,input,button,textarea,select,dd,dl,dt,ol{
	margin:0;
	padding:0;
	word-wrap:break-word;
}
h1,h2,h3,h4,h5,h6{
	font-weight: normal;
}
body{
	font-family:"Arial","microsoft yahei";
	font-size:14px;
	line-height: 1;
	color:#333;
}
*{
	-webkit-tap-highlight-color:rgba(0,0,0,0);
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
li{
	list-style-type:none;
	float:left;
}
table{
	border-collapse:collapse;
	border-spacing:0;
}
a,area{
	text-decoration:none;
	outline:none;
	color: #000;
}
img{
	border:none;
	vertical-align:middle;
}
i,var,em{
	font-style:normal;
}
button{
	font-family:"Arial","microsoft yahei";
	font-size: 14px;
	outline:none;
	cursor: pointer;
	border: none;
	line-height: 1;
}
input[type="submit"]{
	border: none;
}
iframe{
	border: none;
}
input,select,textarea{
	outline:none;
	font-family:"Arial","microsoft yahei";font-size: 14px;
}
textarea{
	resize:none;
	overflow-y:auto;
}
.at{
	margin:0 auto;
}
.fl{
	float: left;
}
.fr{
	float: right;
}
.clear{
	clear:both;
}
.dt{
	display: table;
	width: 100%;
}
.fl-w{
	float: left;
	width: 100%;
}
.hide{
	display: none;
}
.ver-container{
	display: table;
	width: 100%;
	text-align: center;
}
.ver-container > * {
	vertical-align: middle;
	display: table-cell;
}

.swiper-button-disabled{
	cursor: no-drop !important;
	opacity: .5;
}

[class*=icon]{
	font-family:"iconfont" !important;
	font-size:16px;
	font-style:normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

[class*=ico]{
	vertical-align: middle;
	display: inline-block;
}
.wid {
  width: 100%;
  max-width: 1080px;
  margin: 0 auto;
}
.main {
  width: 100%;
}
.main .wid {
  min-height: 1000px;
  background-size: 100% auto;
  padding-top: 325px;
  text-align: center;
}
.main .wid .conts {
  width: 92%;
  min-height: 300px;
  background: rgba(17,67,110,0.47);
  max-width: 760px;
  margin: 0 auto;
  padding: 0 56px;
}
.main .wid .conts .language {
  width: 100%;
  height: 98px;
  position: relative;
}
.main .wid .conts .language .box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 98px;
}
.main .wid .conts .language .box .ico {
  height: 98px;
  width: 56px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.main .wid .conts .language .box .txt {
  line-height: 98px;
  color: #63d5ff;
  font-size: 38px;
}
.main .wid .conts .language .slide {
  width: 200px;
  min-height: 100px;
  background: rgba(0,0,0,0.9);
  border-radius: 20px;
  position: absolute;
  right: 0;
  top: 78px;
  z-index: 999;
  overflow: hidden;
  display: none;
  padding: 10px 0;
}
.main .wid .conts .language .slide p {
  width: 100%;
  text-align: center;
  line-height: 60px;
  font-size: 24px;
  color: #fff;
  margin: 0;
  cursor: pointer;
  font-family: 'RR';
}
.main .wid .conts .language .slide p:hover {
  background-color: #000;
}
.main .wid .conts .logos {
  width: 100%;
  text-align: center;
}
.main .wid .conts .logos img {
  max-width: 100%;
}
.main .wid .conts .id {
  width: 100%;
  padding-top: 50px;
}
.main .wid .conts .id .item {
  width: 100%;
  margin-bottom: 30px;
}
.main .wid .conts .id .tits {
  width: 100%;
  height: 70px;
  font-size: 38px;
  font-family: 'RR';
  color: #fff;
  line-height: 70px;
  text-align: left;
}
.main .wid .conts .id .input {
  width: 100%;
}
.main .wid .conts .id .input input {
  width: 100%;
  height: 96px;
  background-color: #11436e;
  border-radius: 10px;
  border: none;
  padding: 0 25px;
  font-size: 40px;
  font-family: 'RR';
  color: #fff;
}
.main .wid .conts .id .input input::-webkit-input-placeholder {
  color: rgba(127,196,254,0.2);
}
.main .wid .conts .id .instructions {
  width: 100%;
  text-align: right;
  line-height: 76px;
}
.main .wid .conts .id .instructions a {
  font-size: 36px;
  color: #63d5ff;
  font-family: 'RR';
  text-decoration: underline;
}
.main .wid .conts .id .button {
  width: 100%;
  padding: 50px 0;
  padding-top: 20px;
}
.main .wid .conts .id .button button {
  width: 100%;
  height: 158px;
  background: url("../img/ee5a10_654x158.png") center no-repeat;
  background-size: cover;
  font-size: 68px;
  font-family: 'RM';
  padding-bottom: 10px;
  color: #d5de9a;
  text-shadow: 2px 5px 0px #3f480e;
}
.alerts {
  width: 760px;
  height: 120px;
  border-radius: 10px;
  text-align: center;
  line-height: 120px;
  color: #fff;
  font-size: 28px;
  background: rgba(0,0,0,0.8);
  position: fixed;
  left: 50%;
  margin-left: -380px;
  top: 50%;
  margin-top: -30px;
  line-height: 40px;
  display: none;
  padding: 0 15px;
}
.alerts .text {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: left;
}
.alerts.show {
  display: block;
}

@media screen and (max-width: 768px) {
  .alerts {
    width: 96%;
    left: 2%;
    margin-left: 0;
    padding: 20px 30px;
    height: auto;
    line-height: 30px;
  }
  .alerts .text {
    font-size: 18px;
  }
  .main .wid {
    padding-top: 140px;
    min-height: 100vh;
  }
  .main .wid .conts {
    padding: 0 10px;
  }
  .main .wid .conts .language {
    height: 60px;
  }
  .main .wid .conts .language .box {
    height: 60px;
  }
  .main .wid .conts .language .box .txt {
    font-size: 20px;
  }
  .main .wid .conts .language .box .ico {
    width: 32px;
  }
  .main .wid .conts .language .box .ico img {
    width: 20px;
  }
  .main .wid .conts .language .slide {
    top: 50px;
    width: 140px;
    border-radius: 10px;
  }
  .main .wid .conts .language .slide p {
    line-height: 40px;
    font-size: 16px;
  }
  .main .wid .conts .logos img {
    max-width: 70%;
  }
  .main .wid .conts .id {
    padding-top: 10px;
  }
  .main .wid .conts .id .item {
    margin-bottom: 0;
  }
  .main .wid .conts .id .tits {
    height: 50px;
    line-height: 50px;
    font-size: 20px;
  }
  .main .wid .conts .id .input input {
    width: 100%;
    height: 60px;
    border-radius: 6px;
    font-size: 20px;
    padding: 0 10px;
  }
  .main .wid .conts .id .instructions {
    line-height: 44px;
    font-size: 18px;
  }
  .main .wid .conts .id .instructions a {
    font-size: 18px;
  }
  .main .wid .conts .id .button {
    padding: 10px 0;
  }
  .main .wid .conts .id .button button {
    height: 80px;
    font-size: 30px;
    background-size: 100% 100%;
  }
}
