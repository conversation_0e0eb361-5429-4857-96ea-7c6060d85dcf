<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE xml>
<configuration scan="true" scanPeriod="5 seconds">
    <property name="LOG_DIR" value="./logs" />

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%d{yyyy-MM-dd_HH:mm:ss.SSS}][%level][%thread][%logger][%X{msgTraceId}] - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="ALL_ROLLFILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/all.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/all.%d{yyyyMMdd_HH}.log</fileNamePattern>
            <maxHistory>2160</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[%d{yyyy-MM-dd_HH:mm:ss.SSS}][%level][%thread][%logger][%X{msgTraceId}] - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="GAME_MSG_LOG_ROLLFILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/game_msg.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/game_msg.%d{yyyyMMdd_HH}.log</fileNamePattern>
            <maxHistory>2160</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>
    <!--日志文件输出至error.log,使用自定义layout-->
    <appender name="ERROR_ROLLFILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/error.%d{yyyyMMdd_HH}.log</fileNamePattern>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxHistory>2160</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.lc.billion.icefire.web.log.error.layout.LogLayout">
                <Pattern>%msg%n</Pattern>
            </layout>
        </encoder>
    </appender>
    <appender name="CORE_LOG_ROLLFILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/core.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/core.%d{yyyyMMdd_HH}.log</fileNamePattern>
            <maxHistory>2160</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ASYNC_ROLLFILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ALL_ROLLFILE"/>
        <queueSize>10240</queueSize>
        <maxFlushTime>3000</maxFlushTime>
    </appender>
    <appender name="ASYNC_GAME_MSG_LOG_ROLLFILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="GAME_MSG_LOG_ROLLFILE" />
        <queueSize>10240</queueSize>
        <maxFlushTime>3000</maxFlushTime>
    </appender>
    <appender name="ASYNC_ERROR_ROLLFILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_ROLLFILE" />
        <queueSize>10240</queueSize>
        <maxFlushTime>3000</maxFlushTime>
    </appender>
    <appender name="ASYNC_CORE_LOG_ROLLFILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="CORE_LOG_ROLLFILE" />
        <queueSize>10240</queueSize>
        <maxFlushTime>3000</maxFlushTime>
    </appender>

    <logger name="org.apache" level="INFO" additivity="false" />
    <logger name="org.springframework" level="INFO" additivity="false" />
    <logger name="org.mybatis" level="INFO" additivity="false" />
    <logger name="org.mongodb" level="INFO" additivity="false"/>
    <logger name="com.alibaba" level="INFO" additivity="false"/>

    <logger name="com.lc.billion.icefire.web.mapper" level="INFO" additivity="false" />
    <logger name="GameMsgLog" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_GAME_MSG_LOG_ROLLFILE"/>
    </logger>
    <logger name="ErrorLog" level="INFO">
        <appender-ref ref="ASYNC_ERROR_ROLLFILE" />
    </logger>
    <logger name="CoreLog" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_CORE_LOG_ROLLFILE"/>
    </logger>

    <root level="DEBUG">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="ASYNC_ROLLFILE"/>
    </root>
</configuration>