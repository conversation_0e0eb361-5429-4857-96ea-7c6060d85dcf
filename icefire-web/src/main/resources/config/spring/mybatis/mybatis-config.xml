<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
       	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.3.xsd
     	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.3.xsd
     	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.3.xsd">

    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
        <property name="driverClassName" value="com.mysql.cj.jdbc.Driver" />
        <property name="url" value="#{configCenter.lsConfig.webServer.mysql.url}" />
        <property name="username" value="#{configCenter.lsConfig.webServer.mysql.username}" />
        <property name="password" value="#{configCenter.lsConfig.webServer.mysql.password}" />
        <property name="initialSize" value="#{configCenter.lsConfig.webServer.mysql.initialSize}" />
        <property name="maxActive" value="#{configCenter.lsConfig.webServer.mysql.maxActive}" />
        <property name="maxCreateTaskCount" value="20" />
        <property name="minIdle" value="4" />
        <property name="maxWait" value="5000" />
        <property name="queryTimeout" value="120"/>
        <property name="testWhileIdle" value="true" />
        <property name="minEvictableIdleTimeMillis" value="1800000" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="validationQuery" value="SELECT 1" />
        <property name="removeAbandonedTimeout" value="1800000" />
        <property name="logAbandoned" value="true" />
        <property name="poolPreparedStatements" value="true" />
        <property name="maxOpenPreparedStatements" value="50" />
        <property name="filters" value="stat" />
    </bean>
    
    <!-- <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer"> -->
    <!--     <property name="basePackage" value="com.lc.billion.icefire.web.dao"/> -->
    <!--     <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/> -->
    <!-- </bean> -->

	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean" depends-on="flywayService">
		<property name="dataSource" ref="dataSource" />
		<property name="configLocation" value="classpath:config/spring/mybatis/mybatis-mapper.xml" />
		<property name="typeAliasesPackage" value="com.autonavi.domain" />
	</bean>

	<!-- <bean id="sqlSession" class="org.mybatis.spring.SqlSessionTemplate"> -->
	<!-- 	<constructor-arg index="0" ref="sqlSessionFactory" /> -->
	<!-- </bean> -->
	
	<bean id="mybatisMapperScanner" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.lc.billion.icefire.web.mapper" />
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory" />
    </bean>
    
</beans>