<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

    <settings>
        <setting name="callSettersOnNulls" value="true"/>
    </settings>
	<mappers>
        <mapper resource="config/spring/mybatis/mapper/Admin.xml"/>
        <mapper resource="config/spring/mybatis/mapper/SysAcl.xml"/>
        <mapper resource="config/spring/mybatis/mapper/User.xml"/>
        <mapper resource="config/spring/mybatis/mapper/UserHistory.xml"/>
        <!-- <mapper resource="config/spring/mybatis/mapper/Server.xml"/> -->
        <mapper resource="config/spring/mybatis/mapper/Recharge.xml"/>
        <mapper resource="config/spring/mybatis/mapper/Language.xml"/>
        <mapper resource="config/spring/mybatis/mapper/Billboard.xml"/>
        <mapper resource="config/spring/mybatis/mapper/Email.xml"/>
        <mapper resource="config/spring/mybatis/mapper/CommonEmail.xml"/>
        <mapper resource="config/spring/mybatis/mapper/UserOptions.xml"/>
        <mapper resource="config/spring/mybatis/mapper/ProtectionHistory.xml"/>
        <mapper resource="config/spring/mybatis/mapper/BanHeadRecord.xml"/>
        <mapper resource="config/spring/mybatis/mapper/SysRole.xml"/>
        <mapper resource="config/spring/mybatis/mapper/SysAdminRole.xml"/>
        <mapper resource="config/spring/mybatis/mapper/SysAclRole.xml"/>
        <mapper resource="config/spring/mybatis/mapper/AccountBind.xml"/>
        <mapper resource="config/spring/mybatis/mapper/ImportReward.xml"/>
        <mapper resource="config/spring/mybatis/mapper/Subscription.xml"/>
        <mapper resource="config/spring/mybatis/mapper/BulletinBoard.xml"/>
        <mapper resource="config/spring/mybatis/mapper/CDKey.xml"/>
        <mapper resource="config/spring/mybatis/mapper/Avatar.xml"/>
        <mapper resource="config/spring/mybatis/mapper/AddItemsRecord.xml"/>
        <mapper resource="config/spring/mybatis/mapper/AddResourcesRecord.xml"/>
        <mapper resource="config/spring/mybatis/mapper/AddSoldiersRecord.xml"/>
        <mapper resource="config/spring/mybatis/mapper/ModifyHerosRecord.xml"/>
        <mapper resource="config/spring/mybatis/mapper/Account.xml"/>
        <mapper resource="config/spring/mybatis/mapper/QuestionnaireRecord.xml"/>
        <mapper resource="config/spring/mybatis/mapper/WelfareAccount.xml"/>
        <mapper resource="config/spring/mybatis/mapper/WelfareDelAccount.xml"/>
        <mapper resource="config/spring/mybatis/mapper/RefundOrder.xml"/>
        <mapper resource="config/spring/mybatis/mapper/RoleMigrateRecord.xml"/>
        <mapper resource="config/spring/mybatis/mapper/CommunityNotice.xml"/>
        <mapper resource="config/spring/mybatis/mapper/CommunityShare.xml"/>
        <mapper resource="config/spring/mybatis/mapper/BanAccountRecord.xml"/>
        <mapper resource="config/spring/mybatis/mapper/BanChatRecord.xml"/>
        <mapper resource="config/spring/mybatis/mapper/RoleDataAuditMapper.xml"/>
        <mapper resource="config/spring/mybatis/mapper/WechatAccount.xml"/>
        <mapper resource="config/spring/mybatis/mapper/WechatOrder.xml"/>
        <mapper resource="config/spring/mybatis/mapper/GroovyOperationRecord.xml"/>
        <mapper resource="config/spring/mybatis/mapper/MailTemplate.xml"/>
        <mapper resource="config/spring/mybatis/mapper/WechatRewardInfo.xml"/>
        <mapper resource="config/spring/mybatis/mapper/ServerAutoStartJob.xml"/>
        <mapper resource="config/spring/mybatis/mapper/AllianceMemberOpHistory.xml"/>
        <mapper resource="config/spring/mybatis/mapper/RestrictedUser.xml"/>
        <mapper resource="config/spring/mybatis/mapper/RestrictedInfo.xml"/>
        <mapper resource="config/spring/mybatis/mapper/KvkAutoStartJob.xml"/>
	</mappers>
	
</configuration>