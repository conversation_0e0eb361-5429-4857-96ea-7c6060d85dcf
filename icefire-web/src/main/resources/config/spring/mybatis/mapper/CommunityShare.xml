<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.CommunityShareMapper">
	<resultMap id="CommunityShare" type="com.lc.billion.icefire.web.bus.gm.model.CommunityShare">
		<result property="id" column="id"/>
		<result property="serverId" column="serverId"/>
		<result property="platform" column="platform"/>
		<result property="startTime" column="startTime"/>
		<result property="expTime" column="expTime"/>
		<result property="createTime" column="createTime"/>
		<result property="enterId" column="enterId"/>
		<result property="enterTitle" column="enterTitle"/>
		<result property="shareId" column="shareId"/>
		<result property="desc" column="desc"/>
	</resultMap>

	<select id="selectAll" resultMap="CommunityShare">
		select
		`id`
		,`serverId`
		,`platform`
		,`startTime`
		,`expTime`
		,`createTime`
		,`enterId`
		,`enterTitle`
		,`shareId`
		,`desc`
		from community_share
		order by id desc;
	</select>

	<select id="selectById" resultMap="CommunityShare" parameterType="long">
		select
		`id`
		,`serverId`
		,`platform`
		,`startTime`
		,`expTime`
		,`createTime`
		,`enterId`
		,`enterTitle`
		,`shareId`
		,`desc`
		from community_share
		where `id` = #{id}
	</select>

	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.CommunityShare">
		insert into community_share(
		`serverId`
		,`platform`
		,`startTime`
		,`expTime`
		,`createTime`
		,`enterId`
		,`enterTitle`
		,`shareId`
		,`desc`
		) values(
		#{serverId}
		,#{platform}
		,#{startTime}
		,#{expTime}
		,#{createTime}
		,#{enterId}
		,#{enterTitle}
		,#{shareId}
		,#{desc}
		)
		<selectKey keyProperty="id" resultType="long" order="AFTER">
			SELECT LAST_INSERT_ID();
		</selectKey>
	</insert>

	<update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.CommunityShare">
		update community_share set
		`serverId` = #{serverId}
		,`platform` = #{platform}
		,`startTime` = #{startTime}
		,`expTime` = #{expTime}
		,`enterId` = #{enterId}
		,`enterTitle` = #{enterTitle}
		,`shareId` = #{shareId}
		,`desc` = #{desc}
		where `id` = #{id}
	</update>

	<delete id="delete" parameterType="long">
		delete from community_share where `id` = #{id}
	</delete>

</mapper>
	