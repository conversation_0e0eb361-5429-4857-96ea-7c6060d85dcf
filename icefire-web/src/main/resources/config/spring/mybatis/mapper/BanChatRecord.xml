<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.BanChatRecordMapper">
	<resultMap id="BanChatRecord" type="com.lc.billion.icefire.web.bus.gm.model.BanChatRecord">
		<result property="id" column="id"/>
		<result property="bannedTime" column="bannedTime"/>
		<result property="bannedDesc" column="bannedDesc"/>
		<result property="createTime" column="createTime"/>
		<result property="type" column="type"/>
		<result property="serverId" column="serverId"/>
		<result property="userId" column="userId"/>
		<result property="operatorUserId" column="operatorUserId"/>
		<result property="operatorUserName" column="operatorUserName"/>
	</resultMap>

	<select id="selectAll" resultMap="BanChatRecord">
		select
		`id`
		,`bannedTime`
		,`bannedDesc`
		,`createTime`
		,`type`
		,`serverId`
		,`userId`
		,`operatorUserId`
		,`operatorUserName`
		from ban_chat_record
		order by id desc;
	</select>

	<select id="selectById" resultMap="BanChatRecord" parameterType="long">
		select
		`id`
		,`bannedTime`
		,`bannedDesc`
		,`createTime`
		,`type`
		,`serverId`
		,`userId`
		,`operatorUserId`
		,`operatorUserName`
		from ban_chat_record
		where `id` = #{id}
	</select>

	<select id="selectAllByParam" resultMap="BanChatRecord" parameterType="java.util.Map">
		select `id`
		,`bannedTime`
		,`bannedDesc`
		,`createTime`
		,`type`
		,`serverId`
		,`userId`
		,`operatorUserId`
		,`operatorUserName`
		from ban_chat_record where 1=1
		<if test="roleId != null">
			AND `userId` = #{roleId}
		</if>
		<if test="serverId != null">
			AND `serverId` = #{serverId}
		</if>
		<if test="startTime != null">
			AND `createTime` &gt;= #{startTime}
		</if>
		<if test="endTime != null">
			AND `createTime` &lt;= #{endTime}
		</if>
		order by `createTime` desc limit #{pageStart}, #{limitSize}
	</select>

	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.BanChatRecord">
		insert into ban_chat_record(
		`bannedTime`
		,`bannedDesc`
		,`createTime`
		,`type`
		,`serverId`
		,`userId`
		,`operatorUserId`
		,`operatorUserName`
		) values(
		#{bannedTime}
		,#{bannedDesc}
		,#{createTime}
		,#{type}
		,#{serverId}
		,#{userId}
		,#{operatorUserId}
		,#{operatorUserName}
		)
		<selectKey keyProperty="id" resultType="long" order="AFTER">
			SELECT LAST_INSERT_ID();
		</selectKey>
	</insert>

	<update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.BanChatRecord">
		update ban_chat_record set
		`bannedTime` = #{bannedTime}
		,`bannedDesc` = #{bannedDesc}
		,`createTime` = #{createTime}
		,`type` = #{type}
		,`serverId` = #{serverId}
		,`userId` = #{userId}
		,`operatorUserId` = #{operatorUserId}
		,`operatorUserName` = #{operatorUserName}
		where `id` = #{id}
	</update>

	<delete id="delete" parameterType="long">
		delete from ban_chat_record where `id` = #{id}
	</delete>

</mapper>
	