<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.BanHeadRecordMapper">
	<resultMap id="banHeadRecord" type="com.lc.billion.icefire.web.bus.gm.model.BanHeadRecord">
		<result property="id" column="id" />
		<result property="roleId" column="role_id" />
		<result property="serverId" column="server_id" />
		<result property="type" column="type" />
		<result property="reason" column="reason" />
		<result property="duration" column="duration" />
		<result property="expireTime" column="expire_time" />
		<result property="createTime" column="create_time" />
	</resultMap>

	<select id="selectAll" resultMap="banHeadRecord">
		select * from `ban_head_record` order by `id` desc
	</select>

	<select id="selectById" resultMap="banHeadRecord" parameterType="Long">
		select * from `ban_head_record` where `id` = #{id}
	</select>

	<select id="selectByRoleId" resultMap="banHeadRecord" parameterType="Long">
		select * from `ban_head_record` where `role_id` = #{roleId}
	</select>

	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.BanHeadRecord">
		insert into 
		`ban_head_record` (`role_id`,`server_id`,`type`,`reason`,`duration`,`expire_time`,`create_time`) 
		values (#{roleId},#{serverId},#{type},#{reason},#{duration},#{expireTime},#{createTime})
	</insert>

	<update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.BanHeadRecord">
		update `ban_head_record` set
		`id` = #{id}
		,`role_id` = #{roleId}
		,`server_id` = #{serverId}
		,`type` = #{type}
		,`reason` = #{reason}
		,`duration` = #{duration}
		,`expire_time` = #{expireTime}
		where `id` = #{id}
	</update>
	<delete id="delete" parameterType="Long">
		delete from `ban_head_record` where `id` =
		#{id}
	</delete>
</mapper>