<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.KvkAutoStartJobMapper">
    <!-- 基本结果映射 -->
    <resultMap id="KvkAutoStartJob" type="com.lc.billion.icefire.web.bus.gm.model.KvkAutoStartJob">
        <id column="id" property="id" />
        <result column="server_ids" property="serverIds" />
        <result column="start_time" property="startTime" />
        <result column="status" property="status" />
        <result column="creator" property="creator" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 查询：通过ID查询记录 -->
    <select id="selectById" parameterType="long" resultMap="KvkAutoStartJob">
        SELECT
            *
        FROM
            kvk_auto_start_job
        WHERE
            id = #{id}
    </select>

    <!-- 插入：新增记录 -->
    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.KvkAutoStartJob" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO kvk_auto_start_job
            (server_ids, start_time, status, creator, updated_at, created_at)
        VALUES
            (#{serverIds}, #{startTime}, #{status}, #{creator}, #{updatedAt}, #{createdAt})
    </insert>

    <!-- 删除：通过ID删除记录 -->
    <delete id="delete" parameterType="long">
        DELETE FROM kvk_auto_start_job
        WHERE
            id = #{id}
    </delete>

    <!-- 更新 -->
    <delete id="updateStartTime" parameterType="com.lc.billion.icefire.web.bus.gm.model.KvkAutoStartJob">
        UPDATE kvk_auto_start_job SET
            start_time = #{startTime}, updated_at = #{updatedAt}
        WHERE id = #{id}
    </delete>

    <!-- 更新 -->
    <delete id="updateStatus" parameterType="com.lc.billion.icefire.web.bus.gm.model.KvkAutoStartJob">
        UPDATE kvk_auto_start_job SET
            status = #{status}, updated_at = #{updatedAt}
        WHERE id = #{id}
    </delete>


    <select id="selectPageByParam" resultMap="KvkAutoStartJob" parameterType="java.util.Map">
        select *
        from kvk_auto_start_job where 1=1
        <if test="serverId != null">
            AND `server_ids` like CONCAT('%', #{serverId}, '%')
        </if>
        <if test="status != null">
            AND `status` = #{status}
        </if>
        <if test="startTime != null">
            AND `start_time` &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND `start_time` &lt;= #{endTime}
        </if>
        order by `start_time` asc limit #{offset}, #{limit}
    </select>

    <select id="selectCountByParam" resultType="int" parameterType="java.util.Map">
        select count(1)
        from kvk_auto_start_job where 1=1
        <if test="serverId != null">
            AND `server_ids` like CONCAT('%', #{serverId}, '%')
        </if>
        <if test="status != null">
            AND `status` = #{status}
        </if>
        <if test="startTime != null">
            AND `start_time` &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND `start_time` &lt;= #{endTime}
        </if>
    </select>

    <select id="select24HourList" resultMap="KvkAutoStartJob" parameterType="java.util.Map">
        select *
        from kvk_auto_start_job
        where `status` = 0
        and `start_time` &gt;= #{startTime}
        and `start_time` &lt;= #{endTime}
        order by `start_time` asc
    </select>

    <select id="selectWaitJobByServerId" resultMap="KvkAutoStartJob" parameterType="java.util.Map">
        select *
        from kvk_auto_start_job
        where `server_ids` like CONCAT('%', #{serverIds}, '%')
        AND `status` = #{status}
    </select>

    <select id="selectWaitJobByTimeRange" resultMap="KvkAutoStartJob" parameterType="java.util.Map">
        select *
        from kvk_auto_start_job
        where `status` = 0
        and `start_time` &gt;= #{startTime}
        and `start_time` &lt;= #{endTime}
        limit 1
    </select>
</mapper>