<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.UserOptionsMapper">
	<resultMap id="userOptions" type="com.lc.billion.icefire.web.bus.gm.model.UserOptions">
		<result property="id" column="id"/>
		<result property="userName" column="user_name"/>
		<result property="optionType" column="option_type"/>
		<result property="options" column="options"/>
		<result property="serverId" column="server_id"/>
		<result property="optionTime" column="option_time"/>
	</resultMap>
	
	<select id="selectOptionsByUsername" resultMap="userOptions"  parameterType="String">
        select
        id
        ,user_name
        ,option_type
        ,options
        ,server_id
        ,option_time
        from user_options
        where user_name = #{username} order by option_time desc limit 500
    </select>
    
    <select id="selectUserOptions" resultMap="userOptions" >
        select
        id
        ,user_name
        ,option_type
        ,options
        ,server_id
        ,option_time
        from user_options order by option_time desc limit 500
    </select>
    
	<insert id="insertUserOptions" parameterType="com.lc.billion.icefire.web.bus.gm.model.UserOptions" useGeneratedKeys="true" keyProperty="id"  keyColumn="id">
		insert into user_options(
		    user_name
        ,option_type
        ,options
        ,server_id
        ,option_time
		) values(
			#{userName}
			,#{optionType}
			,#{options}
			,#{serverId}
			,#{optionTime}
		)
		<selectKey keyProperty="id" resultType="int" order="AFTER">  
            SELECT LAST_INSERT_ID();  
        </selectKey>  
	</insert>
	
	<select id="findByOptionType" resultMap="userOptions"  parameterType="Integer">
        select
        id
        ,user_name
        ,option_type
        ,options
        ,server_id
        ,option_time
        from user_options
        where option_type = #{optionType} order by option_time desc limit 500
    </select>
    
    <select id="findByNameAndType" resultMap="userOptions"  parameterType="com.lc.billion.icefire.web.bus.gm.model.UserOptions">
        select
        id
        ,user_name
        ,option_type
        ,options
        ,server_id
        ,option_time
        from user_options
        where option_type = #{optionType} and user_name = #{userName} order by option_time desc limit 500
    </select>
</mapper>
	