<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.WelfareDelAccountMapper">
	<resultMap id="welfareDelAccount" type="com.lc.billion.icefire.web.bus.api.entity.WelfareDelAccount">
		<result property="roleId" column="role_id"/>
		<result property="name" column="name"/>
		<result property="remarks" column="remarks"/>
		<result property="totalLimit" column="total_limit"/>
		<result property="setTime" column="set_time"/>
	</resultMap>

	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.api.entity.WelfareDelAccount">
		insert into welfare_delete_account (
			role_id
			,name
			,remarks
			,total_limit
			,set_time
		) values (
		    #{roleId}
		    ,#{name}
		    ,#{remarks}
			,#{totalLimit}
			,#{setTime}
		)
	</insert>

	<update id="update" parameterType="com.lc.billion.icefire.web.bus.api.entity.WelfareDelAccount">
		update welfare_delete_account set
		name = #{name}
		,remarks = #{remarks}
		,total_limit = #{totalLimit}
		,set_time = #{setTime}
		where role_id = #{roleId}
	</update>

    <delete id="delete" parameterType="Long">
        delete from welfare_delete_account
        where role_id = #{roleId}
    </delete>

	<select id="selectByRoleId" resultMap="welfareDelAccount" parameterType="Long">
		select
		role_id
		,name
		,remarks
		,total_limit
		,set_time
		from welfare_delete_account where role_id = #{roleId}
	</select>

	<select id="selectAll" resultMap="welfareDelAccount" >
		select
		role_id
		,name
		,remarks
		,total_limit
		,set_time
		from welfare_delete_account
	</select>
</mapper>
	