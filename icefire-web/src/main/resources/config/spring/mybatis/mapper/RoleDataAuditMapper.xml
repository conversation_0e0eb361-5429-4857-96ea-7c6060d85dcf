<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.RoleDataAuditMapper">
  <resultMap id="roleDataAudit" type="com.lc.billion.icefire.web.bus.gm.model.RoleDataAudit">
    <result property="id" column="id"/>
    <result property="status" column="status"/>
    <result property="type" column="type"/>
    <result property="info" column="info"/>
    <result property="applicant" column="applicant"/>
    <result property="approver" column="approver"/>
    <result property="createTime" column="createTime"/>
    <result property="passTime" column="passTime"/>
    <result property="isUpdate" column="isUpdate"/>
    <result property="roleId" column="role_id"/>
    <result property="serverId" column="server_id"/>
  </resultMap>

  <select id="selectAll" resultMap="roleDataAudit">
    select * from `role_data_audit` order by `id`
            desc
  </select>

  <select id="selectById" resultMap="roleDataAudit" parameterType="Integer">
    select *
    from `role_data_audit` where `id` = #{id}
  </select>

  <select id="countTotalNum" resultType="int">
    select COUNT(*) from `role_data_audit` where `type` = #{type};
  </select>

  <select id="selectByPage" resultMap="roleDataAudit">
    select * from `role_data_audit` where `type` = #{type} order by `createTime` desc limit #{page},#{pageSize};
  </select>

  <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.RoleDataAudit">
    insert into `role_data_audit`(`id`,`status`,`type`,`info`,`applicant`,`approver`,`createTime`,`passTime`,`isUpdate`,`role_id`,`server_id`)
    values(#{id},#{status},#{type},#{info},#{applicant},#{approver},#{createTime},#{passTime},#{isUpdate},#{roleId},#{serverId})
  </insert>

  <update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.RoleDataAudit">
    update `role_data_audit` set
      `id` = #{id}
       ,`status` = #{status}
       ,`type` = #{type}
       ,`info` = #{info}
       ,`applicant` = #{applicant}
       ,`approver` = #{approver}
       ,`createTime` = #{createTime}
       ,`passTime` = #{passTime}
       ,`isUpdate` = #{isUpdate}
       ,`role_id` = #{roleId}
       ,`server_id` = #{serverId}
    where `id` = #{id}
  </update>
  <delete id="delete" parameterType="Integer">
    delete from `role_data_audit` where `id` = #{id}
  </delete>

</mapper>