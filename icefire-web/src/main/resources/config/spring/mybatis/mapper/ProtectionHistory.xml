<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.ProtectionHistoryMapper">
    <resultMap id="ProtectionHistory" type="com.lc.billion.icefire.web.bus.gm.model.ProtectionHistory">
        <result property="id" column="id"/>
        <result property="serverId" column="server_id"/>
        <result property="roleId" column="role_id"/>
        <result property="allianceId" column="alliance_id"/>
        <result property="opAdmin" column="op_admin"/>
        <result property="protectionBeginTime" column="protection_begin_time"/>
        <result property="protectionEndTime" column="protection_end_time"/>
        <result property="protectionTime" column="protection_time"/>
        <result property="cause" column="cause"/>
    </resultMap>

    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.ProtectionHistory">
		insert into protection_history (
			id
			,server_id
			,role_id
			,alliance_id
			,op_admin
			,protection_begin_time
			,protection_end_time
			,protection_time
			,cause
		) values (
			#{id}
			,#{serverId}
			,#{roleId}
			,#{allianceId}
			,#{opAdmin}
			,#{protectionBeginTime}
			,#{protectionEndTime}
			,#{protectionTime}
			,#{cause}
		)
	</insert>


    <select id="selectByRoleId" resultMap="ProtectionHistory" parameterType="String">
		select
			id
			,server_id
			,role_id
			,alliance_id
			,op_admin
			,protection_begin_time
			,protection_end_time
			,protection_time
			,cause
		from protection_history where role_id = #{roleId}
	</select>



    <delete id="delete" parameterType="String">
		delete from protection_history where role_id = #{roleId}
	</delete>

    <select id="selectAll" resultMap="ProtectionHistory">
		select
			id
			,server_id
			,role_id
			,alliance_id
			,op_admin
			,protection_begin_time
			,protection_end_time
			,protection_time
			,cause
		from protection_history
	</select>

    <select id="selectByServerId" resultMap="ProtectionHistory" parameterType="String">
		select
			id
			,server_id
			,role_id
			,alliance_id
			,op_admin
			,protection_begin_time
			,protection_end_time
			,protection_time
			,cause
		from protection_history
		where server_id = #{serverId}
	</select>

	<select id="selectByAlliance" resultMap="ProtectionHistory" parameterType="String">
		select
		id
		,server_id
		,role_id
		,alliance_id
		,op_admin
		,protection_begin_time
		,protection_end_time
		,protection_time
		,cause
		from protection_history
		where alliance_id = #{allianceId}
	</select>

</mapper>
	