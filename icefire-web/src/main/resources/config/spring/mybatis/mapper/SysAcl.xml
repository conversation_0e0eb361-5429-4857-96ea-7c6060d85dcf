<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.SysAclMapper">
    <resultMap id="sysAcl" type="com.lc.billion.icefire.web.bus.user.entity.SysAcl">
        <id property="pid" column="pid"/>
        <result property="pname" column="pname"/>
        <result property="url" column="url"/>
        <result property="filter" column="filter"/>
        <result property="premark" column="premark"/>
    </resultMap>
    
    <select id="selectAll" resultMap="sysAcl">
    	SELECT * 
    	FROM sys_acl
    </select>

    <select id="selectAllByUid" resultMap="sysAcl" parameterType="Long">
        SELECT c.*
        FROM sys_admin_role as a
        LEFT JOIN sys_acl_role as b on a.rid = b.rid
        LEFT JOIN sys_acl as c on b.pid = c.pid
        WHERE a.uid = #{uid}
    </select>

</mapper>