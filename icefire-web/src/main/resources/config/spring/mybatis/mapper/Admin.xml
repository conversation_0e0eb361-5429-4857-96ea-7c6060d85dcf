<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.AdminMapper">
    <resultMap id="admin" type="com.lc.billion.icefire.web.bus.user.entity.Admin">
        <result property="id" column="id"/>
        <result property="name" column="username"/>
        <result property="password" column="password"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    
    <resultMap id="adminSet" type="com.lc.billion.icefire.web.bus.user.entity.Admin">
        <id property="id" column="id"/>
        <result property="name" column="username"/>
        <result property="password" column="password"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <collection property="sysRoles" ofType="com.lc.billion.icefire.web.bus.user.entity.SysRole">
        	<id property="rid" column="rid" />
        	<result property="rname" column="rname"/>
        	<result property="rremark" column="rremark"/>
        	<collection property="sysAcls" ofType="com.lc.billion.icefire.web.bus.user.entity.SysAcl">
        		<id property="pid" column="pid"/>
        		<result property="pname" column="pname"/>
        		<result property="url" column="url"/>
        		<result property="filter" column="filter"/>
        		<result property="premark" column="premark"/>
        	</collection>
        </collection>
    </resultMap>
    
    <select id="selectById" resultMap="admin" parameterType="Long">
        SELECT
        id
        ,username
        ,password
        ,type
        ,create_time
        FROM admin
        WHERE id = #{id}      
    </select>
    
    <select id="selectByName" resultMap="admin" parameterType="String">
        SELECT
        id
        ,username
        ,password
        ,type
        ,create_time
        FROM admin
        WHERE username = #{userName}  
    </select>
    
    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.user.entity.Admin" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO admin(
            username
            ,password
            ,type
            ,create_time
        ) VALUES(
            #{name}
            ,#{password}
            ,#{type}
            ,#{createTime}
        )
        
        <selectKey keyProperty="id" order="AFTER" resultType="Long">
            SELECT LAST_INSERT_ID();
        </selectKey>
    </insert>
    
    <update id="update" parameterType="com.lc.billion.icefire.web.bus.user.entity.Admin">
        UPDATE admin SET
        username = #{name}
        ,password = #{password}
        WHERE id = #{id}
    </update>

    <select id="selectUserNames" parameterType="String" resultMap="adminSet">
    	SELECT u.*, r.*
    	FROM admin u
    		left JOIN sys_admin_role ur on ur.uid = u.id
    		left JOIN sys_role r on r.rid = ur.rid where u.username != 'admin'
    </select>

    <select id="selectUserNameById" parameterType="Long" resultMap="adminSet">
    	SELECT u.*, r.*
    	FROM admin u
    		left JOIN sys_admin_role ur on ur.uid = u.id
    		left JOIN sys_role r on r.rid = ur.rid
    		WHERE u.id = #{id}
    </select>
    
    <select id="findUserByUserName" parameterType="String" resultMap="adminSet">
    	SELECT u.*, r.*, p.*
    	FROM admin u
    		INNER JOIN sys_admin_role ur on ur.uid = u.id
    		INNER JOIN sys_role r on r.rid = ur.rid
    		INNER JOIN sys_acl_role pr on pr.rid = r.rid
    		INNER JOIN sys_acl p on p.pid = pr.pid
    	WHERE u.username = #{userName}
    </select>

    <select id="findUserByRid" parameterType="Integer" resultMap="adminSet">
    	SELECT u.*
    	FROM admin u
    		INNER JOIN sys_admin_role ur on ur.uid = u.id
    		INNER JOIN sys_role r on r.rid = ur.rid
    		INNER JOIN sys_acl_role pr on pr.rid = r.rid
    	WHERE r.rid = #{rid}
    </select>

    <delete id="deleteAdmin">
        DELETE from admin where id = #{id}
    </delete>
    
</mapper>