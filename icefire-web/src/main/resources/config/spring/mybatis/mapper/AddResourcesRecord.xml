<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.AddResourcesMapper">
	<resultMap id="addResourcesRecord" type="com.lc.billion.icefire.web.bus.gm.model.AddResourcesRecord">
		<result property="id" column="id"/>
        <result property="successCount" column="success_count"/>
        <result property="totalCount" column="total_count"/>
		<result property="options" column="options"/>
		<result property="optionTime" column="option_time"/>
        <result property="comment" column="comment"/>
	</resultMap>
	
	<select id="selectOptionsById" resultMap="addResourcesRecord"  parameterType="Long">
        select
        id
        ,success_count
        ,total_count
        ,options
        ,option_time
        ,comment
        from modify_resources
        where id = #{id}
    </select>
    
    <select id="selectAddResourcesRecord" resultMap="addResourcesRecord" >
        select
        id
        ,success_count
        ,total_count
        ,options
        ,option_time
        ,comment
        from modify_resources
    </select>
    
	<insert id="insertAddResourcesRecord" parameterType="com.lc.billion.icefire.web.bus.gm.model.AddResourcesRecord" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into modify_resources(
            success_count
            ,total_count
            ,options
            ,option_time
		) values(
            #{successCount}
            ,#{totalCount}
			,#{options}
			,#{optionTime}
		)
		<selectKey keyProperty="id" resultType="int" order="AFTER">  
            SELECT LAST_INSERT_ID();  
        </selectKey>  
	</insert>

    <update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.AddResourcesRecord">
        update modify_resources set
        comment = #{comment}
        where id = #{id}
    </update>

    <delete id="delete" parameterType="Long">
        delete from modify_resources where id = #{id}
    </delete>

</mapper>
	