<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.BanAccountRecordMapper">
	<resultMap id="BanAccountRecord" type="com.lc.billion.icefire.web.bus.gm.model.BanAccountRecord">
		<result property="id" column="id"/>
		<result property="bannedTime" column="bannedTime"/>
		<result property="bannedDesc" column="bannedDesc"/>
		<result property="createTime" column="createTime"/>
		<result property="type" column="type"/>
		<result property="serverId" column="serverId"/>
		<result property="userId" column="userId"/>
		<result property="operatorUserId" column="operatorUserId"/>
		<result property="operatorUserName" column="operatorUserName"/>
		<result property="roleInfoSnapshot" column="roleInfoSnapshot"/>
	</resultMap>

	<select id="selectAll" resultMap="BanAccountRecord">
		select
		`id`
		,`bannedTime`
		,`bannedDesc`
		,`createTime`
		,`type`
		,`serverId`
		,`userId`
		,`operatorUserId`
		,`operatorUserName`
		,`roleInfoSnapshot`
		from ban_acccount_record
		order by id desc;
	</select>

	<select id="selectById" resultMap="BanAccountRecord" parameterType="long">
		select
		`id`
		,`bannedTime`
		,`bannedDesc`
		,`createTime`
		,`type`
		,`serverId`
		,`userId`
		,`operatorUserId`
		,`operatorUserName`
		,`roleInfoSnapshot`
		from ban_acccount_record
		where `id` = #{id}
	</select>

	<select id="selectAllByParam" resultMap="BanAccountRecord" parameterType="java.util.Map">
		select `id`
		,`bannedTime`
		,`bannedDesc`
		,`createTime`
		,`type`
		,`serverId`
		,`userId`
		,`operatorUserId`
		,`operatorUserName`
		,`roleInfoSnapshot`
		from ban_acccount_record where 1=1
		<if test="roleId != null">
			AND `userId` = #{roleId}
		</if>
		<if test="serverId != null">
			AND `serverId` = #{serverId}
		</if>
		<if test="startTime != null">
			AND `createTime` &gt;= #{startTime}
		</if>
		<if test="endTime != null">
			AND `createTime` &lt;= #{endTime}
		</if>
		order by `createTime` desc limit #{pageStart}, #{limitSize}
	</select>

	<select id="countAllByParam" resultType="int" parameterType="java.util.Map">
		select count(1)
		from ban_acccount_record where 1=1
		<if test="roleId != null">
			AND `userId` = #{roleId}
		</if>
		<if test="serverId != null">
			AND `serverId` = #{serverId}
		</if>
		<if test="startTime != null">
			AND `createTime` &gt;= #{startTime}
		</if>
		<if test="endTime != null">
			AND `createTime` &lt;= #{endTime}
		</if>
	</select>

	<select id="getRoleLatestRecord" resultMap="BanAccountRecord" parameterType="long">
		select
			`id`
			 ,`bannedTime`
			 ,`bannedDesc`
			 ,`createTime`
			 ,`type`
			 ,`serverId`
			 ,`userId`
			 ,`operatorUserId`
			 ,`operatorUserName`
			 ,`roleInfoSnapshot`
		from ban_acccount_record
		where `userId` = #{roleId}
		order by id desc limit 1;
	</select>

	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.BanAccountRecord">
		insert into ban_acccount_record(
		`bannedTime`
		,`bannedDesc`
		,`createTime`
		,`type`
		,`serverId`
		,`userId`
		,`operatorUserId`
		,`operatorUserName`
		,`roleInfoSnapshot`
		) values(
		#{bannedTime}
		,#{bannedDesc}
		,#{createTime}
		,#{type}
		,#{serverId}
		,#{userId}
		,#{operatorUserId}
		,#{operatorUserName}
		,#{roleInfoSnapshot}
		)
		<selectKey keyProperty="id" resultType="long" order="AFTER">
			SELECT LAST_INSERT_ID();
		</selectKey>
	</insert>

	<update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.BanAccountRecord">
		update ban_acccount_record set
		`bannedTime` = #{bannedTime}
		,`bannedDesc` = #{bannedDesc}
		,`createTime` = #{createTime}
		,`type` = #{type}
		,`serverId` = #{serverId}
		,`userId` = #{userId}
		,`operatorUserId` = #{operatorUserId}
		,`operatorUserName` = #{operatorUserName}
		where `id` = #{id}
	</update>

	<delete id="delete" parameterType="long">
		delete from ban_acccount_record where `id` = #{id}
	</delete>

</mapper>
	