<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.GroovyOperationRecordMapper">
    <!-- 基本结果映射 -->
    <resultMap id="GroovyOperationRecord" type="com.lc.billion.icefire.web.bus.gm.model.GroovyRecord">
        <id column="id" property="id" />
        <result column="operator" property="operator" />
        <result column="task_name" property="taskName" />
        <result column="server_id" property="serverId" />
        <result column="thread_mode" property="threadMode" />
        <result column="groovy_script" property="groovyScript" />
        <result column="response" property="response" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="task_status" property="taskStatus" />
        <result column="finish_num" property="finishNum" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 查询：通过ID查询记录 -->
    <select id="selectById" parameterType="int" resultMap="GroovyOperationRecord">
        SELECT
            id, operator, task_name, server_id, thread_mode, groovy_script,
            response, start_time, end_time, task_status, finish_num, created_time, updated_time
        FROM
            groovy_operation_record
        WHERE
            id = #{id}
    </select>

    <!-- 查询：获取所有记录 -->
    <select id="selectAll" resultMap="GroovyOperationRecord">
        SELECT
            id, operator, task_name, server_id, thread_mode, groovy_script,
            response, start_time, end_time, task_status, finish_num, created_time, updated_time
        FROM
            groovy_operation_record
    </select>

    <!-- 插入：新增记录 -->
    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.GroovyRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO groovy_operation_record
        (operator, task_name, server_id, thread_mode, groovy_script, response,
         start_time, end_time, task_status, finish_num, created_time, updated_time)
        VALUES
            (#{operator}, #{taskName}, #{serverId}, #{threadMode}, #{groovyScript}, #{response},
             #{startTime}, #{endTime}, #{taskStatus}, #{finishNum}, #{createdTime}, #{updatedTime})
    </insert>

    <update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.GroovyRecord">
        UPDATE groovy_operation_record
        SET
            operator = #{operator},
            task_name = #{taskName},
            server_id = #{serverId},
            thread_mode = #{threadMode},
            groovy_script = #{groovyScript},
            response = #{response},
            start_time = #{startTime},
            end_time = #{endTime},
            task_status = #{taskStatus},
            finish_num = #{finishNum},
            created_time = #{createdTime},
            updated_time = #{updatedTime}
        WHERE
            id = #{id}
    </update>

    <!-- 删除：通过ID删除记录 -->
    <delete id="deleteById" parameterType="int">
        DELETE FROM groovy_operation_record
        WHERE
            id = #{id}
    </delete>

    <!-- 查询：根据operator查询记录 -->
    <select id="selectByOperator" parameterType="string" resultMap="GroovyOperationRecord">
        SELECT
            id, operator, task_name, server_id, thread_mode, groovy_script,
            response, start_time, end_time, task_status, finish_num, created_time, updated_time
        FROM
            groovy_operation_record
        WHERE
            operator = #{operator}
    </select>

    <!-- 查询：根据taskName查询记录 -->
    <select id="selectByTaskName" parameterType="string" resultMap="GroovyOperationRecord">
        SELECT
            id, operator, task_name, server_id, thread_mode, groovy_script,
            response, start_time, end_time, task_status, finish_num, created_time, updated_time
        FROM
            groovy_operation_record
        WHERE
            task_name = #{taskName}
    </select>

    <!-- 查询：根据operator和taskName获取记录id -->
    <select id="selectIdByOperatorAndTaskName" parameterType="map" resultType="int">
        SELECT
            id
        FROM
            groovy_operation_record
        WHERE
            operator = #{operator} AND task_name = #{taskName}
    </select>

    <select id="selectGroovyOperationRecordsByCreatedTimeRange" resultMap="GroovyOperationRecord">
        SELECT *
        FROM your_table_name
        WHERE created_time BETWEEN #{createdTimeStart} AND #{createdTimeEnd}
    </select>

    <select id="selectResponseById" resultType="String">
        SELECT response
        FROM your_table_name
        WHERE id = #{id}
    </select>

    <select id="selectPageByParam" resultMap="GroovyOperationRecord" parameterType="java.util.Map">
        select `id`
        ,`operator`
        ,`task_name`
        ,`server_id`
        ,`thread_mode`
        ,`groovy_script`
        ,`response`
        ,`start_time`
        ,`end_time`
        ,`task_status`
        ,`created_time`
        ,`updated_time`
        from groovy_operation_record where 1=1
        <if test="taskName != null">
            AND `task_name` = #{taskName}
        </if>
        <if test="taskStatus != null">
            AND `task_status` = #{taskStatus}
        </if>
        <if test="operator != null">
            AND `operator` = #{operator}
        </if>
        <if test="serverId != null">
            AND `server_id` = #{serverId}
        </if>
        <if test="threadMode != null">
            AND `thread_mode` = #{threadMode}
        </if>
        <if test="startTime != null">
            AND `start_time` &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND `start_time` &lt;= #{endTime}
        </if>
        order by `created_time` desc limit #{offset}, #{limit}
    </select>

    <select id="selectCountByParam" resultType="int" parameterType="java.util.Map">
        select count(1)
        from groovy_operation_record where 1=1
        <if test="taskName != null">
            AND `task_name` = #{taskName}
        </if>
        <if test="taskStatus != null">
            AND `task_status` = #{taskStatus}
        </if>
        <if test="operator != null">
            AND `operator` = #{operator}
        </if>
        <if test="serverId != null">
            AND `server_id` = #{serverId}
        </if>
        <if test="threadMode != null">
            AND `thread_mode` = #{threadMode}
        </if>
        <if test="startTime != null">
            AND `start_time` &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND `start_time` &lt;= #{endTime}
        </if>
    </select>
</mapper>