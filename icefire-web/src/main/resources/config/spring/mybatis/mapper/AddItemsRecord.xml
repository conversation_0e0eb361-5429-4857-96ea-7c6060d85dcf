<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.AddItemsRecord">
	<resultMap id="addItemsRecord" type="com.lc.billion.icefire.web.bus.gm.model.AddItemsRecord">
		<result property="id" column="id"/>
        <result property="successCount" column="success_count"/>
        <result property="totalCount" column="total_count"/>
		<result property="options" column="options"/>
		<result property="optionTime" column="option_time"/>
        <result property="comment" column="comment"/>
	</resultMap>
	
	<select id="selectOptionsById" resultMap="addItemsRecord"  parameterType="Long">
        select
        id
        ,success_count
        ,total_count
        ,options
        ,option_time
        ,comment
        from add_items
        where id = #{id}
    </select>
    
    <select id="selectAddItemsRecord" resultMap="addItemsRecord" >
        select
        id
        ,success_count
        ,total_count
        ,options
        ,option_time
        ,comment
        from add_items
    </select>
    
	<insert id="insertAddItemsRecord" parameterType="com.lc.billion.icefire.web.bus.gm.model.AddItemsRecord" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into add_items(
            success_count
            ,total_count
            ,options
            ,option_time
		) values(
            #{successCount}
            ,#{totalCount}
			,#{options}
			,#{optionTime}
		)
		<selectKey keyProperty="id" resultType="int" order="AFTER">  
            SELECT LAST_INSERT_ID();  
        </selectKey>  
	</insert>

    <update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.AddItemsRecord">
        update add_items set
        comment = #{comment}
        where id = #{id}
    </update>

    <delete id="delete" parameterType="Long">
        delete from add_items where id = #{id}
    </delete>

</mapper>
	