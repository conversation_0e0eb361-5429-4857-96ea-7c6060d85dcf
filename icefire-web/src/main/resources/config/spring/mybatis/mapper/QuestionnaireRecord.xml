<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.QuestionnaireRecordMapper">
	<resultMap id="questionnaireRecord" type="com.lc.billion.icefire.web.bus.gm.model.QuestionnaireRecord">
		<result property="id" column="id"/>
		<result property="options" column="options"/>
		<result property="optionTime" column="option_time"/>
        <result property="comment" column="comment"/>
	</resultMap>
	
	<select id="selectOptionsById" resultMap="questionnaireRecord"  parameterType="Long">
        select
        id
        ,options
        ,option_time
        ,comment
        from questionnaire_record
        where id = #{id}
    </select>
    
    <select id="selectQuestionnaireRecord" resultMap="questionnaireRecord" >
        select
        id
        ,options
        ,option_time
        ,comment
        from questionnaire_record
    </select>
    
	<insert id="insertQuestionnaireRecord" parameterType="com.lc.billion.icefire.web.bus.gm.model.QuestionnaireRecord" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into questionnaire_record(
            options
            ,option_time
		) values(
			#{options}
			,#{optionTime}
		)
		<selectKey keyProperty="id" resultType="int" order="AFTER">  
            SELECT LAST_INSERT_ID();  
        </selectKey>  
	</insert>

    <update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.QuestionnaireRecord">
        update questionnaire_record set
        comment = #{comment}
        where id = #{id}
    </update>

    <delete id="delete" parameterType="Long">
        delete from questionnaire_record where id = #{id}
    </delete>

</mapper>
	