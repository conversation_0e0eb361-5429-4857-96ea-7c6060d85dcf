<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.RestrictedUserMapper">
    <resultMap id="restrictedUser" type="com.lc.billion.icefire.web.bus.risk.entity.RestrictedUser">
        <result property="roleId" column="role_id"/>
        <result property="id" column="id"/>
        <result property="keyId" column="key_id"/>
        <result property="createTime" column="create_time"/>
        <result property="typeId" column="type_id"/>
        <result property="context" column="context"/>
    </resultMap>

    <select id="getAll" resultMap="restrictedUser">
        select id, role_id, key_id, create_time, type_id, context
        from restricted_user
        order by role_id
    </select>

    <select id="findByRoleId" resultMap="restrictedUser" parameterType="Long">
        select id, role_id, key_id, create_time, type_id, context
        from restricted_user
        where role_id = #{roleId}
    </select>

    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.risk.entity.RestrictedUser"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into restricted_user(role_id, key_id, create_time, type_id, context)
        values (#{roleId}, #{keyId}, #{createTime}, #{typeId}, #{context})
    </insert>

    <update id="update" parameterType="com.lc.billion.icefire.web.bus.risk.entity.RestrictedUser">
        update restricted_user
        set role_id = #{roleId}
          , key_id  = #{keyId}
        where role_id = #{roleId}
    </update>

    <delete id="delete" parameterType="Long">
        delete
        from restricted_user
        where role_id = #{roleId}
    </delete>

    <delete id="deleteByRoleIdAndKeyId" parameterType="com.lc.billion.icefire.web.bus.risk.entity.RestrictedUser">
        delete
        from restricted_user
        where role_id = #{roleId}
          and key_id = #{keyId}
    </delete>
    <delete id="deleteKeyByType" parameterType="com.lc.billion.icefire.web.bus.risk.entity.RestrictedUser">
        delete
        from restricted_user
        where type_id = #{typeId}
          and key_id = #{keyId}
    </delete>
    <select id="findByKeyId" resultMap="restrictedUser"
            parameterType="com.lc.billion.icefire.web.bus.risk.entity.RestrictedUser">
        select id, role_id, key_id, create_time, type_id, context
        from restricted_user
        where key_id = #{keyId}
          and type_id = #{typeId}
    </select>
    <select id="findByRoleIdAndKeyId" resultType="com.lc.billion.icefire.web.bus.risk.entity.RestrictedUser">
        select id, role_id, key_id, create_time, type_id, context
        from restricted_user
        where key_id = #{keyId}
          and role_id = #{roleId}
          and type_id = #{typeId}
    </select>


</mapper>
