<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.ServerMapper">
	<resultMap id="server" type="com.lc.billion.icefire.web.bus.server.entity.Server">
		<result property="id" column="id"/>
		<result property="name" column="name"/>
		<result property="mysqlIp" column="mysql_ip"/>
		<result property="mysqlName" column="mysql_name"/>
		<result property="mysqlUser" column="mysql_user"/>
		<result property="mysqlPass" column="mysql_pass"/>
		<result property="serverIp" column="server_ip"/>
		<result property="serverInnerIp" column="server_inner_ip"/>
		<result property="serverHost" column="server_host"/>
		<result property="serverPort" column="server_port"/>
		<result property="wsPort" column="ws_port"/>
		<result property="serverStatus" column="server_status"/>
		<result property="serverHotState" column="server_hot_state"/>
<!-- 		<result property="recommend" column="recommend"/> -->
		<result property="diversionConfigurationByApp" column="diversion_configuration_by_app"/>
		<result property="diversionConfigurationByCountry" column="diversion_configuration_by_country"/>
		<result property="diversionConfigurationByWeight" column="diversion_configuration_by_weight"/>
		<result property="createTime" column="create_time"/>
		<result property="openTime" column="open_time"/>
		<result property="canMoveTo" column="can_move_to"/>
		<result property="testServer" column="test_server"/>
		<result property="onlineLimit" column="online_limit"/>
		<result property="roleLimit" column="role_limit"/>		
		<result property="params" column="params"/>
		<result property="rpcIsOpen" column="rpc_is_open"/>
		<result property="consulIp" column="consul_ip"/>
		<result property="consulPort" column="consul_port"/>
		<result property="serverGroup" column="server_group"/>
		<result property="serverPosition" column="server_position"/>
		<result property="serverConsulPort" column="server_consul_port"/>
		<result property="serverVersion" column="server_version"/>
		<result property="consulCanOut" column="consul_can_out"/>
		<result property="consulCanIn" column="consul_can_in"/>
	</resultMap>
	
	<select id="selectAll" resultMap="server">
		select
			id
			,name
			,mysql_ip
			,mysql_name
			,mysql_user
			,mysql_pass
			,server_ip
			,server_inner_ip
			,server_host
			,server_port
			,ws_port
			,server_status
			,server_hot_state
			,diversion_configuration_by_app
			,diversion_configuration_by_country
			,diversion_configuration_by_weight
			,create_time
			,open_time
			,can_move_to
			,test_server
			,online_limit
			,role_limit
			,params
			,rpc_is_open
			,consul_ip
			,consul_port
			,server_group
			,server_position
			,server_version
			,server_consul_port
			,consul_can_out
			,consul_can_in
		from server 
		order by id desc;
	</select>
	
	<select id="selectById" resultMap="server" parameterType="Long">
		select
			id
			,name
			,mysql_ip
			,mysql_name
			,mysql_user
			,mysql_pass
			,server_ip
			,server_inner_ip
			,server_host
			,server_port
			,ws_port
			,server_status
			,server_hot_state
			,diversion_configuration_by_app
			,diversion_configuration_by_country
			,diversion_configuration_by_weight
			,create_time
			,open_time
			,can_move_to
			,test_server
			,online_limit
			,role_limit
			,params
			,rpc_is_open
			,consul_ip
			,consul_port
			,server_group
			,server_position
			,server_version
			,server_consul_port
			,consul_can_out
			,consul_can_in
		from server
		where id = #{id}
	</select>
	
	<select id="selectRecommendServer" resultMap="server">
		select
			id
			,name
			,mysql_ip
			,mysql_name
			,mysql_user
			,mysql_pass
			,server_ip
			,server_inner_ip
			,server_host
			,server_port
			,ws_port
			,server_status
			,server_hot_state
			,diversion_configuration_by_app
			,diversion_configuration_by_country
			,diversion_configuration_by_weight
			,create_time
			,open_time
			,can_move_to
			,test_server
			,online_limit
			,role_limit
			,params
			,rpc_is_open
			,consul_ip
			,consul_port
			,server_group
			,server_position
			,server_version
			,server_consul_port
			,consul_can_out
			,consul_can_in
		from server
		where diversion_configuration_by_app is not null and diversion_configuration_by_country is not null and diversion_configuration_by_weight is not null 
		order by id desc;
	</select>
	<select id="selectNewServer" resultMap="server">
		select
			id
			,name
			,mysql_ip
			,mysql_name
			,mysql_user
			,mysql_pass
			,server_ip
			,server_inner_ip
			,server_host
			,server_port
			,ws_port
			,server_status
			,server_hot_state
			,diversion_configuration_by_app
			,diversion_configuration_by_country
			,diversion_configuration_by_weight
			,create_time
			,open_time
			,can_move_to
			,test_server
			,online_limit
			,role_limit
			,params
			,rpc_is_open
			,consul_ip
			,consul_port
			,server_group
			,server_position
			,server_version
			,server_consul_port
			,consul_can_out
			,consul_can_in
		from server
		where server_status = 4
		order by id desc;
	</select>
	
	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.server.entity.Server"  keyColumn="id">
		insert into server(
		    id
			,name
			,mysql_ip
			,mysql_name
			,mysql_user
			,mysql_pass
			,server_ip
			,server_inner_ip
			,server_host
			,server_port
			,ws_port
			,server_status
			,server_hot_state
			,diversion_configuration_by_app
			,diversion_configuration_by_country
			,diversion_configuration_by_weight
			,create_time
			,open_time
			,can_move_to
			,test_server
			,online_limit
			,role_limit
			,params
			,rpc_is_open
			,consul_ip
			,consul_port
			,server_group
			,server_position
			,server_version
			,server_consul_port
			,consul_can_out
			,consul_can_in
		) values(
		     #{id}
			,#{name}
			,#{mysqlIp}
			,#{mysqlName}
			,#{mysqlUser}
			,#{mysqlPass}
			,#{serverIp}
			,#{serverInnerIp}
			,#{serverHost}
			,#{serverPort}
			,#{wsPort}
			,#{serverStatus}
			,#{serverHotState}
			,#{diversionConfigurationByApp}
			,#{diversionConfigurationByCountry}
			,#{diversionConfigurationByWeight}
			,#{createTime}
			,#{openTime}
			,#{canMoveTo}
			,#{testServer}
			,#{onlineLimit}
			,#{roleLimit}
			,#{params}
			,#{rpcIsOpen}
			,#{consulIp}
			,#{consulPort}
			,#{serverGroup}
			,#{serverPosition}
			,#{serverVersion}
			,#{serverConsulPort}
			,#{consulCanOut}
			,#{consulCanIn}
		)
	</insert>
	
	<update id="update" parameterType="com.lc.billion.icefire.web.bus.server.entity.Server">
		update server set
			name = #{name} 
			,mysql_ip = #{mysqlIp}
			,mysql_name = #{mysqlName}
			,mysql_user = #{mysqlUser}
			,mysql_pass = #{mysqlPass}
			,server_ip = #{serverIp}
			,server_inner_ip = #{serverInnerIp}
			,server_host = #{serverHost}
			,server_port = #{serverPort}
			,ws_port = #{wsPort}
			,server_status = #{serverStatus}
			,server_hot_state = #{serverHotState}
			,diversion_configuration_by_app = #{diversionConfigurationByApp}
			,diversion_configuration_by_country = #{diversionConfigurationByCountry}
			,diversion_configuration_by_weight = #{diversionConfigurationByWeight}
			,open_time = #{openTime}
			,can_move_to = #{canMoveTo}
			,test_server = #{testServer}
			,online_limit = #{onlineLimit}
			,role_limit = #{roleLimit}
			,params = #{params}
			,rpc_is_open = #{rpcIsOpen}
			,consul_ip = #{consulIp}
			,consul_port = #{consulPort}
			,server_group = #{serverGroup}
			,server_position = #{serverPosition}
			,server_version = #{serverVersion}
			,server_consul_port = #{serverConsulPort}
			,consul_can_out = #{consulCanOut}
			,consul_can_in = #{consulCanIn}
		where id = #{id}
	</update>
	<!-- 批量更新服务器状态时避开新服，新服都自己处理 -->
	<update id="updateStatus" parameterType="Integer">
	   update server set server_status = #{serverStatus} WHERE server_status !=4
	</update>
	<delete id="delete" parameterType="Long">
		delete from server where id = #{id}
	</delete>

</mapper>
	