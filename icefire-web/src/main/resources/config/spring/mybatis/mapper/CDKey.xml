<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.CDKeyMapper">
	<resultMap id="CDKeyInfo" type="com.lc.billion.icefire.web.bus.gm.model.CDKeyInfo">
		<result property="keyId" column="keyId"/>
		<result property="groupId" column="groupId"/>
		<result property="creatorName" column="creatorName"/>
		<result property="rewards" column="rewards"/>
		<result property="type" column="type"/>
		<result property="used" column="used"/>
		<result property="status" column="status"/>
		<result property="remark" column="remark"/>
		<result property="startTime" column="startTime"/>
		<result property="expTime" column="expTime"/>
		<result property="createTime" column="createTime"/>
		<result property="param" column="param"/>
		<result property="country" column="country" />
		<result property="approver" column="approver"/>
		<result property="passTime" column="pass_time" />
		<result property="auditStatus" column="audit_status" />
		<result property="isCanUseMany" column="isCanUseMany"/>
		<result property="levelLimit" column="levelLimit"/>
	</resultMap>

	<resultMap id="UseRecord" type="com.lc.billion.icefire.web.bus.gm.model.CDKeyUseRecord">
		<result property="keyId" column="keyId"/>
		<result property="groupId" column="groupId"/>
		<result property="roleId" column="roleId"/>
		<result property="useTime" column="useTime"/>
	</resultMap>

	<select id="selectAll" resultMap="CDKeyInfo">
		select * from `cdkey_info` order by `keyId` desc
	</select>

	<select id="selectCDKeyGroupParamAll" resultType="java.util.HashMap">
		select `keyId`
			 , `groupId`
			 , `creatorName`
			 , `rewards`
			 , `type`
			 , `status`
			 , `remark`
			 , `param`
			 , DATE_FORMAT(`startTime`, '%Y-%m-%d %H:%i:%S') AS startTime
			 , DATE_FORMAT(`expTime`, '%Y-%m-%d %H:%i:%S')   AS expTime
			 , createTime
			 , `levelLimit`
			 , `isCanUseMany`
		from cdkey_info
		order by createTime desc;
	</select>

	<select id="selectByPage" resultMap="CDKeyInfo">
		select * from cdkey_info order by `createTime` desc limit #{page}, #{pageSize};
	</select>

	<select id="countTotalNum" resultType="int">
		select COUNT(*) from cdkey_info;
	</select>

	<select id="selectCDKeyGroupParam" resultType="java.util.HashMap">
		select `keyId`
			 , `groupId`
			 , `creatorName`
			 , `rewards`
			 , `type`
			 , `status`
			 , `remark`
			 , `param`
			 , DATE_FORMAT(`startTime`, '%Y-%m-%d %H:%i:%S') AS startTime
			 , DATE_FORMAT(`expTime`, '%Y-%m-%d %H:%i:%S')   AS expTime
			 , `levelLimit`
			 , `isCanUseMany`
		from cdkey_info
		where `groupId` = #{groupId};
	</select>

	<select id="selectDisposableCDKeyInfo" resultType="java.util.HashMap">
		SELECT r.roleId
			 , DATE_FORMAT(r.useTime, '%Y-%m-%d %H:%i:%S') AS useTime
			 , c.*
		FROM cdkey_use_record AS r
				 RIGHT JOIN
			 (SELECT keyId
				   , groupId
				   , creatorName
				   , rewards
				   , type
				   , used
				   , status
				   , remark
				   , DATE_FORMAT(startTime, '%Y-%m-%d %H:%i:%S') AS startTime
				   , DATE_FORMAT(expTime, '%Y-%m-%d %H:%i:%S')   AS expTime
				   , param
				   , levelLimit
				   , isCanUseMany
			  FROM cdkey_info
			  WHERE keyId = #{keyId})
				 AS c ON c.keyId = r.keyId;
	</select>

	<select id="selectByGroupId" resultMap="CDKeyInfo">
		select `keyId`
			 , `groupId`
			 , `creatorName`
			 , `rewards`
			 , `type`
			 , `used`
			 , `status`
			 , `remark`
			 , `startTime`
			 , `expTime`
			 , `createTime`
			 , `param`
			 , `country`
			 , `levelLimit`
			 , `isCanUseMany`
		from cdkey_info
		where `groupId` = #{groupId};
	</select>

	<select id="selectByKeyId" resultMap="CDKeyInfo" parameterType="String">
		select `keyId`
			 , `groupId`
			 , `creatorName`
			 , `rewards`
			 , `type`
			 , `used`
			 , `status`
			 , `remark`
			 , `param`
			 , `startTime`
			 , `expTime`
			 , `createTime`
			 , `country`
			 , `approver`
			 , `pass_time`
			 , `audit_status`
			 , `levelLimit`
			 , `isCanUseMany`
		from cdkey_info
		where `keyId` = #{keyId};
	</select>

	<insert id="insertCDKeyInfo" parameterType="com.lc.billion.icefire.web.bus.gm.model.CDKeyInfo">
		insert into cdkey_info( `keyId`
							  , `groupId`
							  , `creatorName`
							  , `rewards`
							  , `type`
							  , `used`
							  , `status`
							  , `remark`
							  , `param`
							  , `startTime`
							  , `expTime`
							  , `createTime`
							  , `country`
							  , `audit_status`
							  , `levelLimit`
		) values( #{keyId}
				, #{groupId}
				, #{creatorName}
				, #{rewards}
				, #{type}
				, #{used}
				, #{status}
				, #{remark}
				, #{param}
				, #{startTime}
				, #{expTime}
				, #{createTime}
				, #{country}
				, #{auditStatus}
				, #{levelLimit})
	</insert>

	<insert id="batchInsertCDKeyInfo" parameterType="java.util.List">
		insert into cdkey_info(
		`keyId`
		,`groupId`
		,`creatorName`
		,`rewards`
		,`type`
		,`used`
		,`status`
		,`remark`
		,`param`
		,`startTime`
		,`expTime`
		,`createTime`
		,`country`
		, `audit_status`
		, `levelLimit`
		, `isCanUseMany`
		) values
		<foreach collection="list" item="it" separator=",">
			(
			#{it.keyId}
			,#{it.groupId}
			,#{it.creatorName}
			,#{it.rewards}
			,#{it.type}
			,#{it.used}
			,#{it.status}
			,#{it.remark}
			,#{it.param}
			,#{it.startTime}
			,#{it.expTime}
			,#{it.createTime}
			,#{it.country}
			,#{it.auditStatus}
			,#{it.levelLimit}
			,#{it.isCanUseMany})
		</foreach>
	</insert>

	<update id="updateCDKeyInfo" parameterType="com.lc.billion.icefire.web.bus.gm.model.CDKeyInfo">
		update cdkey_info set `groupId`     = #{groupId}
							, `creatorName` = #{creatorName}
							, `rewards`     = #{rewards}
							, `type`        = #{type}
							, `used`        = #{used}
							, `status`      = #{status}
							, `remark`      = #{remark}
							, `param`       = #{param}
							, `startTime`   = #{startTime}
							, `expTime`     = #{expTime}
							, `createTime`  = #{createTime}
							, `approver`  = #{approver}
							, `pass_time`  = #{passTime}
							, `audit_status`  = #{auditStatus}
							, `levelLimit`    = #{levelLimit}
							, `isCanUseMany`  = #{isCanUseMany}
		where `keyId` = #{keyId}
	</update>

	<update id="updateCDKeyOverdueStatus" parameterType="java.util.List">
		update cdkey_info set
		`status` = 2
		where `groupId` in (
		<foreach collection="list" item="item" index="index"
				 separator=",">
			#{item}
		</foreach>
		)
	</update>

	<update id="updateCDKeyDiscardStatus" parameterType="String">
		update cdkey_info set `status` = 0
		where `groupId` = #{groupId};
	</update>

	<delete id="deleteCDKeyInfo" parameterType="String">
		delete from cdkey_info where `keyId` = #{keyId}
	</delete>


	<select id="selectRecordByKeyId" resultMap="UseRecord" parameterType="String">
		select * from cdkey_use_record
		where `keyId` = #{keyId};
	</select>

	<select id="selectRecordByRoleId" resultMap="UseRecord" parameterType="long">
		select * from cdkey_use_record
		where `roleId` = #{roleId};
	</select>

	<select id="selectDetailedRecordByRoleId" resultType="java.util.HashMap" parameterType="long">
		SELECT c.keyId
			 , c.groupId
			 , c.rewards
			 , c.type
			 , c.remark
			 , DATE_FORMAT(c.startTime, '%Y-%m-%d %H:%i:%S') AS startTime
			 , DATE_FORMAT(c.expTime, '%Y-%m-%d %H:%i:%S')   AS expTime
			 , param
			 , r.*
		FROM cdkey_info AS c
				 INNER JOIN
			 (SELECT roleId
				   , keyId
				   , DATE_FORMAT(useTime, '%Y-%m-%d %H:%i:%S') AS useTime
			  FROM cdkey_use_record
			  WHERE roleId = #{roleId}) AS r
			 ON r.keyId = c.keyId;
	</select>

	<select id="selectOneRecord" resultMap="UseRecord">
		select * from cdkey_use_record
		where `keyId` = #{keyId} and `roleId` = #{roleId};
	</select>

	<select id="selectRoleRecordByGroupId" resultMap="UseRecord">
		select * from cdkey_use_record
		where `groupId` = #{groupId} and `roleId` = #{roleId};
	</select>

	<insert id="insertUseRecord" parameterType="com.lc.billion.icefire.web.bus.gm.model.CDKeyUseRecord">
		insert into cdkey_use_record( `roleId`
									, `keyId`
									, `groupId`
									, `useTime`
		) values( #{roleId}
				, #{keyId}
				, #{groupId}
				, #{useTime})
	</insert>


	<update id="updateOverdueStatus" parameterType="String">
		update cdkey_info set `status` = 2
		where `status` = 1 and  `expTime` &lt; #{nowTime};
	</update>
	<update id="updateByGroupId" parameterType="com.lc.billion.icefire.web.bus.gm.model.CDKeyInfo">
		update cdkey_info
		set `creatorName`  = #{creatorName}
		  , `rewards`      = #{rewards}
		  , `type`         = #{type}
		  , `used`         = #{used}
		  , `status`       = #{status}
		  , `remark`       = #{remark}
		  , `param`        = #{param}
		  , `startTime`    = #{startTime}
		  , `expTime`      = #{expTime}
		  , `createTime`   = #{createTime}
		  , `approver`     = #{approver}
		  , `pass_time`    = #{passTime}
		  , `audit_status` = #{auditStatus}
		  , `levelLimit`   = #{levelLimit}
		  , `isCanUseMany` = #{isCanUseMany}
		where `groupId` = #{groupId}
	</update>

	<select id="selectPageGroupOrderByExpTime" resultType="java.util.HashMap">
		select groupId, expTime from (
		    select groupId, max(expTime) as expTime
		    from cdkey_info
			<if test="status != null">
				where status = #{status}
			</if>
		    group by groupId
							) as table_sub
		order by
			CASE
				WHEN expTime >= #{nowTime} THEN 0  -- 未过期记录优先
				ELSE 1  -- 已过期记录排后
			END,
		expTime asc limit #{limit} offset	#{offset}
	</select>

	<select id="countGroupOrderByExpTime" resultType="long">
		select count(*) from (
		select groupId, max(expTime) as expTime
		from cdkey_info
		<if test="status != null">
			where status = #{status}
		</if>
		group by groupId
		) as table_sub
	</select>

	<select id="selectOneByGroupIds" resultMap="CDKeyInfo">
		SELECT * FROM (
		SELECT *,
		ROW_NUMBER() OVER (PARTITION BY groupId ORDER BY keyId) AS rn
		FROM cdkey_info
		WHERE groupId IN (
		<foreach collection="groupIds" item="item" separator=",">
			#{item}
		</foreach>
		)
		) AS subquery
		WHERE rn = 1;
	</select>

	<select id="selectUseRecordByGroups" resultMap="UseRecord">
		select * from cdkey_use_record where `groupId` in (
		<foreach collection="list" item="item" index="index"
                 separator=",">
		    #{item}
		</foreach>
		)
	</select>

	<select id="selectUseCountByGroupIds" resultType="java.util.HashMap">
		select groupId, count(*) as count from cdkey_use_record where `groupId` in (
		<foreach collection="list" item="item" index="index" separator=",">
			#{item}
		</foreach>
		) group by groupId
	</select>

</mapper>
