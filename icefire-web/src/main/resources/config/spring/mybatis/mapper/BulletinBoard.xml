<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.BulletinBoardMapper">
	<resultMap id="BulletinBoard" type="com.lc.billion.icefire.web.bus.gm.model.BulletinBoard">
		<result property="id" column="id"/>
		<result property="serverId" column="serverId"/>
		<result property="contentKey" column="contentKey"/>
		<result property="platform" column="platform"/>
		<result property="weights" column="weights"/>
		<result property="startTime" column="startTime"/>
		<result property="expTime" column="expTime"/>
		<result property="createTime" column="createTime"/>
		<result property="pictureId" column="pictureId"/>
		<result property="redirectId" column="redirectId"/>
	</resultMap>

	<select id="selectAll" resultMap="BulletinBoard">
		select
		`id`
		,`serverId`
		,`contentKey`
		,`platform`
		,`weights`
		,`startTime`
		,`expTime`
		,`createTime`
		,`pictureId`
		,`redirectId`
		from bulletin_board
		order by weights desc;
	</select>

	<select id="selectById" resultMap="BulletinBoard" parameterType="long">
		select
		`id`
		,`serverId`
		,`contentKey`
		,`platform`
		,`weights`
		,`startTime`
		,`expTime`
		,`createTime`
		,`pictureId`
		,`redirectId`
		from bulletin_board
		where `id` = #{id}
	</select>

	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.BulletinBoard">
		insert into bulletin_board(
		`serverId`
		,`contentKey`
		,`platform`
		,`weights`
		,`startTime`
		,`expTime`
		,`createTime`
		,`pictureId`
		,`redirectId`
		) values(
		#{serverId}
		,#{contentKey}
		,#{platform}
		,#{weights}
		,#{startTime}
		,#{expTime}
		,#{createTime}
		,#{pictureId}
		,#{redirectId}
		)
		<selectKey keyProperty="id" resultType="long" order="AFTER">
			SELECT LAST_INSERT_ID();
		</selectKey>
	</insert>

	<update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.BulletinBoard">
		update bulletin_board set
		`serverId` = #{serverId}
		,`contentKey` = #{contentKey}
		,`platform` = #{platform}
		,`weights` = #{weights}
		,`startTime` = #{startTime}
		,`expTime` = #{expTime}
		,`pictureId` = #{pictureId}
		,`redirectId` = #{redirectId}
		where `id` = #{id}
	</update>

	<delete id="delete" parameterType="long">
		delete from bulletin_board where `id` = #{id}
	</delete>

</mapper>
	