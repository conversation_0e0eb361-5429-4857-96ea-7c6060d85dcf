<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.EmailMapper">
	<resultMap id="email" type="com.lc.billion.icefire.web.bus.gm.model.Email">
		<result property="id" column="id" />
		<result property="category" column="category" />
		<result property="title" column="title" />
		<result property="content" column="content" />
		<result property="attachment" column="attachment" />
		<result property="resource" column="resource" />
		<result property="condition" column="condition" />
		<result property="expire" column="expire" />
		<result property="createTime" column="create_time" />
	</resultMap>

	<select id="selectAll" resultMap="email">
		select * from email order by id
		desc
	</select>

	<select id="selectById" resultMap="email" parameterType="String">
		select *
		from email where id = #{id}
	</select>

	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.Email">
		insert into email(`id`,`category`,`title`,`content`,`attachment`,`resource`,`condition`,`expire`) values(#{id},#{category},#{title},#{content},#{attachment},#{resource},#{condition},#{expire})
	</insert>

	<update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.Email">
		update email set
		`id` = #{id}
		,`category` = #{category}
		,`title` = #{title}
		,`content` = #{content}
		,`attachment` = #{attachment}
		,`resource` = #{resource}
		,`condition` = #{condition}
		,`expire` = #{expire}
		where id = #{id}
	</update>
	<delete id="delete" parameterType="String">
		delete from email where id =
		#{id}
	</delete>

</mapper>
	