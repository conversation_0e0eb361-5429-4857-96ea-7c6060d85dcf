<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.AllianceMemberOpHistoryMapper">
    <!-- 基本结果映射 -->
    <resultMap id="AllianceMemberOpHistory" type="com.lc.billion.icefire.web.bus.gm.model.AllianceMemberOpHistory">
        <id column="id" property="id" />
        <result column="op_type" property="opType" />
        <result column="op_content" property="opContent" />
        <result column="operator" property="operator" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 查询：通过ID查询记录 -->
    <select id="selectById" parameterType="int" resultMap="AllianceMemberOpHistory">
        SELECT
            *
        FROM
            alliance_member_op_history
        WHERE
            id = #{id}
    </select>

    <!-- 查询：获取所有记录 -->
    <select id="selectAll" resultMap="AllianceMemberOpHistory">
        SELECT
            *
        FROM
            alliance_member_op_history
    </select>

    <!-- 插入：新增记录 -->
    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.AllianceMemberOpHistory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO alliance_member_op_history
            (op_type, op_content, operator, updated_at, created_at)
        VALUES
            (#{opType}, #{opContent}, #{operator},  #{updatedAt}, #{createdAt})
    </insert>

    <!-- 删除：通过ID删除记录 -->
    <delete id="delete" parameterType="int">
        DELETE FROM alliance_member_op_history
        WHERE
            id = #{id}
    </delete>


    <select id="selectPageByParam" resultMap="AllianceMemberOpHistory" parameterType="java.util.Map">
        select *
        from alliance_member_op_history where 1=1
        order by `created_at` desc limit #{offset}, #{limit}
    </select>

    <select id="selectCountByParam" resultType="int" parameterType="java.util.Map">
        select count(1)
        from alliance_member_op_history where 1=1
    </select>
</mapper>