<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.WechatOrderMapper">
    <resultMap id="wechatOrder" type="com.lc.billion.icefire.web.bus.wechat.entity.WechatOrder">
        <id property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="goodsCount" column="goods_count"/>
        <result property="goodsId" column="goods_id"/>
        <result property="zoneId" column="zone_id"/>
        <result property="giftId" column="gift_id"/>
        <result property="serverId" column="server_id"/>
        <result property="createTime" column="create_time"/>
        <result property="orderStatus" column="order_status"/>
        <result property="roleId" column="role_id"/>
        <result property="wxOpenId" column="wx_openid"/>
    </resultMap>
    
    <select id="selectByOrderId" resultMap="wechatOrder" parameterType="String">
        SELECT *
        FROM wechat_order
        WHERE order_id = #{orderId}
    </select>
    <update id="updateOrderStatus" parameterType="com.lc.billion.icefire.web.bus.wechat.entity.WechatOrder">
        UPDATE wechat_order SET
        order_status = #{orderStatus}
        WHERE order_id = #{orderId}
    </update>
    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.wechat.entity.WechatOrder">
        INSERT INTO wechat_order
        (order_id, role_id, wx_openid, goods_count, goods_id, zone_id, gift_id,  server_id, create_time, order_status)
        VALUES(#{orderId}, #{roleId}, #{wxOpenId}, #{goodsCount}, #{goodsId}, #{zoneId}, #{giftId},  #{serverId}, #{createTime}, #{orderStatus});
    </insert>
</mapper>