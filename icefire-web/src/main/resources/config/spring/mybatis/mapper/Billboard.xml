<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.BillboardMapper">
	<resultMap id="Billboard" type="com.lc.billion.icefire.web.bus.gm.model.Billboard">
		<result property="id" column="id"/>
		<result property="serverId" column="serverId"/>
		<result property="contentKey" column="contentKey"/>
		<result property="viewable" column="viewable"/>
		<result property="newUser" column="newUser"/>
		<result property="startTime" column="startTime"/>
		<result property="expTime" column="expTime"/>
		<result property="platform" column="platform"/>
		<result property="createTime" column="createTime"/>
		<result property="type" column="type"/>
	</resultMap>
	
	<select id="selectAll" resultMap="Billboard">
        select
        	`id`
            ,`serverId`
            ,`contentKey`
            ,`viewable`
            ,`newUser`
            ,`platform`
            ,`startTime`
            ,`expTime`
            ,`createTime`
            ,`type`
        from billboard
        order by id desc;
    </select>
    
    <select id="selectByType" resultMap="Billboard" parameterType="int">
		select
			`id`
            ,`serverId`
            ,`contentKey`
            ,`viewable`
            ,`newUser`
            ,`platform`
            ,`startTime`
            ,`expTime`
            ,`createTime`
            ,`type`
		from billboard
		where `type` = #{type}
	</select>
	
	<select id="selectById" resultMap="Billboard" parameterType="int">
		select
			`id`
            ,`serverId`
            ,`contentKey`
            ,`viewable`
            ,`newUser`
            ,`platform`
            ,`startTime`
            ,`expTime`
            ,`createTime`
            ,`type`
		from billboard
		where `id` = #{id}
	</select>
	
	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.Billboard">
		insert into billboard(
		    `serverId`
		    ,`contentKey`
            ,`viewable`
            ,`newUser`
            ,`platform`
            ,`startTime`
            ,`expTime`
            ,`createTime`
            ,`type`
		) values(
			#{serverId}
			,#{contentKey}
			,#{viewable}
			,#{newUser}
			,#{platform}
			,#{startTime}
			,#{expTime}
			,#{createTime}
			,#{type}
		)
		<selectKey keyProperty="id" resultType="int" order="AFTER">  
            SELECT LAST_INSERT_ID();  
        </selectKey>  
	</insert>
	
	<update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.Billboard">
		update billboard set 
		  `serverId` = #{serverId}
		  ,`contentKey` = #{contentKey}
		  ,`viewable` = #{viewable}
		  ,`newUser` = #{newUser}
		  ,`platform` = #{platform}
		  ,`startTime` = #{startTime}
		  ,`expTime` = #{expTime}
		  ,`type` = #{type}
		where `id` = #{id}
	</update>
	
	<delete id="delete" parameterType="int">
		delete from billboard where `id` = #{id}
	</delete>

</mapper>
	