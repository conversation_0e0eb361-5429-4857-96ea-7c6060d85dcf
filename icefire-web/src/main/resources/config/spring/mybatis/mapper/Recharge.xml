<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.RechargeMapper">
	<resultMap id="recharge" type="com.lc.billion.icefire.web.bus.api.entity.Recharge">
		<result property="id" column="id"/>
		<result property="userId" column="user_id"/>
		<result property="roleId" column="role_id"/>
		<result property="serverId" column="server_id"/>
		<result property="cityLevel" column="city_level"/>
		<result property="platform" column="platform"/>
		<result property="state" column="state"/>
		<result property="isTest" column="is_test"/>
		<result property="welfare" column="welfare"/>
		<result property="productId" column="product_id"/>
		<result property="currency" column="currency"/>
		<result property="currencyName" column="currency_name"/>
		<result property="localCurrency" column="local_currency"/>
		<result property="diamond" column="diamond"/>
		<result property="createTime" column="create_time"/>
		<result property="deliveryTime" column="delivery_time"/>
		<result property="originId" column="origin_id"/>
		<result property="orderType" column="order_type"/>
		<result property="discount" column="discount"/>
		<result property="plat_orderId" column="orderid"/>
		<result property="packageName" column="packagename"/>
		<result property="plat_productId" column="productid"/>
		<result property="purchaseTime" column="purchasetime"/>
		<result property="purchaseState" column="purchasestate"/>
		<result property="developerPayload" column="developerpayload"/>
		<result property="purchaseToken" column="purchasetoken"/>
		<result property="receipt" column="receipt"/>
		<result property="receiptBack" column="receipt_back"/>
		<result property="combinationItem" column="combination_item"/>
		<result property="appsFlyerId" column="apps_flyer_id"/>
		<result property="extData" column="ext_data"/>
		<result property="iosOriginalTID" column="ios_original_tid"/>
		<result property="season" column="season"/>
	</resultMap>

	<resultMap id="rechargeRange" type="com.lc.billion.icefire.web.bus.api.entity.RechargeRange">
		<result property="createTime" column="create_time"/>
		<result property="productId" column="productid"/>
	</resultMap>
	
	<select id="selectById" resultMap="recharge" parameterType="String">
		select
		id
		,user_id
		,role_id
		,server_id
		,city_level
		,platform
		,state
		,is_test
		,welfare
		,product_id
		,currency
		,currency_name
		,local_currency
		,diamond
		,create_time
		,delivery_time
		,origin_id
		,order_type
		,discount
		,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
		from recharge where id = #{id}
	</select>

	<select id="selectByOrderId" resultMap="recharge" parameterType="String">
		select
		id
		,user_id
		,role_id
		,server_id
		,city_level
		,platform
		,state
		,is_test
		,welfare
		,product_id
		,currency
		,currency_name
		,local_currency
		,diamond
		,create_time
		,delivery_time
		,origin_id
		,order_type
		,discount
		,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
		from recharge where orderid = #{orderId}
	</select>
	
	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.api.entity.Recharge">
		insert into recharge (
			id
	        ,user_id
	        ,role_id
	        ,server_id
	        ,city_level
	        ,platform
	        ,state
	        ,is_test
	        ,welfare
	        ,product_id
	        ,currency
	        ,currency_name
			,local_currency
	        ,diamond
	        ,create_time
	        ,discount
	        ,combination_item
	        ,apps_flyer_id
			,ext_data
			,productid
			,ios_original_tid
			,season
		) values (
		    #{id}
			,#{userId}
			,#{roleId}
			,#{serverId}
			,#{cityLevel}
			,#{platform}
			,#{state}
			,#{isTest}
			,#{welfare}
			,#{productId}
			,#{currency}
			,#{currencyName}
			,#{localCurrency}
			,#{diamond}
			,#{createTime}
			,#{discount}
			,#{combinationItem}
			,#{appsFlyerId}
			,#{extData}
			,#{plat_productId}
			,#{iosOriginalTID}
			,#{season}
		)
	</insert>
	
	<update id="update" parameterType="com.lc.billion.icefire.web.bus.api.entity.Recharge">
		update recharge set 
            id = #{id}
			,state = #{state}
			,is_test = #{isTest}
			,orderid = #{plat_orderId}
			,currency = #{currency}
			,local_currency = #{localCurrency}
			,packagename = #{packageName}
			,productid = #{plat_productId}
			,purchasetime = #{purchaseTime}
			,purchasestate = #{purchaseState}
			,developerpayload = #{developerPayload}
			,purchasetoken = #{purchaseToken}
			,receipt = #{receipt}
			,receipt_back = #{receiptBack}
			,delivery_time = #{deliveryTime}
			,origin_id = #{originId}
			,order_type = #{orderType}
			,apps_flyer_id = #{appsFlyerId}
			,ext_data = #{extData}
			,ios_original_tid = #{iosOriginalTID}

		where id = #{id}
	</update>
	
	<delete id="delete" parameterType="String">
		delete from recharge where id = #{id}
	</delete>
	
	<select id="selectOrdersByRoleAndStatus" resultMap="recharge" parameterType="java.util.Map">
        SELECT
        id
        ,user_id
        ,role_id
        ,server_id
		,city_level
        ,platform
        ,state
        ,is_test
        ,welfare
        ,product_id
        ,currency
        ,currency_name
		,local_currency
        ,diamond
        ,create_time
        ,delivery_time
        ,origin_id
        ,order_type
        ,discount
        ,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
        FROM recharge
        WHERE role_id = #{roleId}
        <if test="status != 0">
        	AND state = #{status}
        </if>
        <if test="startDate != null">
        	AND create_time >= #{startDate}
        </if>
		<if test="endDate != null">
			AND create_time &lt;= #{endDate}
		</if>
        ORDER BY create_time DESC
    </select>
    
    <update id="updateOrderStatus" parameterType="java.util.Map">
        UPDATE recharge SET
        state = #{status}
        WHERE id = #{id}
    </update>

	<update id="updateStateByOrderIds" parameterType="java.util.Map">
		UPDATE recharge SET
		state = #{state}
		WHERE id IN
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item.id}
		</foreach>
	</update>

	<select id="selectLastOrdersByRoleAndPriceId" resultMap="recharge" parameterType="java.util.Map">
		SELECT
		id
		,user_id
		,role_id
		,server_id
		,city_level
		,platform
		,state
		,is_test
		,welfare
		,product_id
		,currency
		,currency_name
		,local_currency
		,diamond
		,create_time
		,delivery_time
		,origin_id
		,order_type
		,discount
		,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
		FROM recharge
		WHERE role_id = #{roleId} and productid = #{priceId} and state = #{state} order by create_time desc limit 1;
	</select>


	<select id="selectOrdersByRoleAndProducId" resultMap="recharge" parameterType="java.util.Map">
		SELECT
		id
		,user_id
		,role_id
		,server_id
		,city_level
		,platform
		,state
		,is_test
		,welfare
		,product_id
		,currency
		,currency_name
		,local_currency
		,diamond
		,create_time
		,delivery_time
		,origin_id
		,order_type
		,discount
		,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
		FROM recharge
		WHERE role_id = #{roleId} and product_id = #{productId} and state = #{state}
	</select>

	<select id="selectOptionalPackageOrder" resultMap="recharge" parameterType="java.util.Map">
		SELECT
		id
		,user_id
		,role_id
		,server_id
		,city_level
		,platform
		,state
		,is_test
		,welfare
		,product_id
		,currency
		,currency_name
		,local_currency
		,diamond
		,create_time
		,delivery_time
		,origin_id
		,order_type
		,discount
		,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
		FROM recharge
		WHERE role_id = #{roleId} and ext_data = #{packageId} and state = #{state} limit 1
	</select>

	<select id="selectOrderByPlatOrderId" resultMap="recharge" parameterType="String">
		SELECT
		id
		,user_id
		,role_id
		,server_id
		,city_level
		,platform
		,state
		,is_test
		,welfare
		,product_id
		,currency
		,currency_name
		,local_currency
		,diamond
		,create_time
		,delivery_time
		,origin_id
		,order_type
		,discount
		,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
		FROM recharge
		WHERE orderid = #{orderId}
	</select>

	<select id="selectByIOSOriginalTID" resultMap="recharge" parameterType="String">
		select
		id
		,user_id
		,role_id
		,server_id
		,city_level
		,platform
		,state
		,is_test
		,welfare
		,product_id
		,currency
		,currency_name
		,local_currency
		,diamond
		,create_time
		,delivery_time
		,origin_id
		,order_type
		,discount
		,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
		from recharge where ios_original_tid = #{originalTID}
		order by create_time desc limit 1
	</select>

	<select id="selectByPurchaseToken" resultMap="recharge" parameterType="String">
		select
		id
		,user_id
		,role_id
		,server_id
		,city_level
		,platform
		,state
		,is_test
		,welfare
		,product_id
		,currency
		,currency_name
		,local_currency
		,diamond
		,create_time
		,delivery_time
		,origin_id
		,order_type
		,discount
		,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
		from recharge where purchasetoken = #{purchaseToken}
	</select>

	<select id="selectByPurchaseTimeRange" resultMap="recharge" parameterType="java.util.Map">
		SELECT
		id
		,user_id
		,role_id
		,server_id
		,city_level
		,platform
		,state
		,is_test
		,welfare
		,product_id
		,currency
		,currency_name
		,local_currency
		,diamond
		,create_time
		,delivery_time
		,origin_id
		,order_type
		,discount
		,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
		FROM recharge
		WHERE state = #{state} AND platform = #{platform} AND is_test = #{isTest} AND purchasetime BETWEEN #{startTime} AND #{endTime}
	</select>

	<select id="selectByPlatOrderIds" resultMap="recharge">
		SELECT
		id
		,user_id
		,role_id
		,server_id
		,city_level
		,platform
		,state
		,is_test
		,welfare
		,product_id
		,currency
		,currency_name
		,local_currency
		,diamond
		,create_time
		,delivery_time
		,origin_id
		,order_type
		,discount
		,orderid
		,packagename
		,productid
		,purchasetime
		,purchasestate
		,developerpayload
		,purchasetoken
		,receipt
		,receipt_back
		,combination_item
		,apps_flyer_id
		,ext_data
		,ios_original_tid
		,season
		FROM recharge
		WHERE orderid in
		<foreach collection="list" item="orderId" index="index" open="(" close=")" separator=",">
			#{orderId}
		</foreach>
	</select>

	<update id="updateForXsolla" parameterType="com.lc.billion.icefire.web.bus.api.entity.Recharge">
		update recharge set
		id = #{id}
		,state = #{state}
		,is_test = #{isTest}
		,orderid = #{plat_orderId}
		,currency = #{currency}
		,local_currency = #{localCurrency}
		,packagename = #{packageName}
		,product_id = #{productId}
		,productid = #{plat_productId}
		,purchasetime = #{purchaseTime}
		,purchasestate = #{purchaseState}
		,developerpayload = #{developerPayload}
		,purchasetoken = #{purchaseToken}
		,receipt = #{receipt}
		,receipt_back = #{receiptBack}
		,delivery_time = #{deliveryTime}
		,origin_id = #{originId}
		,order_type = #{orderType}
		,apps_flyer_id = #{appsFlyerId}
		,ext_data = #{extData}
		,ios_original_tid = #{iosOriginalTID}

		where id = #{id}
	</update>

	<select id="selectRangeRecharge" resultMap="rechargeRange" parameterType="java.util.Map">
		SELECT
		productid
		,create_time
		FROM recharge
		WHERE role_id = #{roleId}
		<if test="status != 0">
			AND state = #{status}
		</if>
		<if test="startDate != null">
			AND create_time >= #{startDate}
		</if>
		<if test="endDate != null">
			AND create_time &lt;= #{endDate}
		</if>
	</select>

</mapper>
	