<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.AccountMapper">
	<resultMap id="account" type="com.lc.billion.icefire.web.bus.api.account.entity.Account">
		<result property="id" column="id"/>
		<result property="createTime" column="createTime"/>
		<result property="advertisingId" column="advertisingId"/>
		<result property="mainRoleId" column="mainRoleId"/>
		<result property="lastLoginTime" column="lastLoginTime"/>
		<result property="needSendBindMail" column="needSendBindMail"/>
		<result property="lastDeviceId" column="lastDeviceId"/>
		<result property="currPlatform" column="currPlatform"/>
		<result property="appKey" column="appKey"/>
		<result property="appVersion" column="appVersion"/>
		<result property="country" column="country"/>
		<result property="channel" column="channel"/>
        <result property="ip" column="ip"/>
	</resultMap>

	<select id="selectById" resultMap="account" parameterType="Long">
		SELECT
			id
			,createTime
			,advertisingId
			,mainRoleId
			,lastLoginTime
			,needSendBindMail
			,lastDeviceId
			,currPlatform
			,appKey
			,appVersion
			,country
			,channel
            ,ip
		FROM account
		WHERE id = #{id}
	</select>

	<select id="selectByRoleId" resultMap="account" parameterType="Long">
		SELECT
			id
			,createTime
			,advertisingId
			,mainRoleId
			,lastLoginTime
			,needSendBindMail
			,lastDeviceId
			,currPlatform
			,appKey
			,appVersion
			,country
			,channel
            ,ip
		FROM account
		WHERE mainRoleId = #{mainRoleId}
	</select>

	<select id="selectByIp" resultMap="account" parameterType="String">
		SELECT
		id
		,advertisingId
		,mainRoleId
		,ip
		FROM account
		WHERE ip = #{ip}
	</select>

	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.api.account.entity.Account" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		INSERT INTO account(
			createTime
			,advertisingId
			,mainRoleId
			,lastLoginTime
			,needSendBindMail
			,lastDeviceId
			,currPlatform
			,appKey
			,appVersion
			,country
			,channel
            ,ip
		) VALUES(
			#{createTime}
			,#{advertisingId}
			,#{mainRoleId}
			,#{lastLoginTime}
			,#{needSendBindMail}
			,#{lastDeviceId}
			,#{currPlatform}
			,#{appKey}
			,#{appVersion}
			,#{country}
			,#{channel}
            ,#{ip}
		)
	</insert>

	<insert id="save" parameterType="com.lc.billion.icefire.web.bus.api.account.entity.Account">
		INSERT INTO account(
		 id
		,createTime
		,advertisingId
		,mainRoleId
		,lastLoginTime
		,needSendBindMail
		,lastDeviceId
		,currPlatform
		,appKey
		,appVersion
		,country
		,channel
		,ip
		) VALUES(
		#{id}
		,#{createTime}
		,#{advertisingId}
		,#{mainRoleId}
		,#{lastLoginTime}
		,#{needSendBindMail}
		,#{lastDeviceId}
		,#{currPlatform}
		,#{appKey}
		,#{appVersion}
		,#{country}
		,#{channel}
		,#{ip}
		)
	</insert>
	
	<update id="update" parameterType="com.lc.billion.icefire.web.bus.api.account.entity.Account">
		UPDATE account SET
			mainRoleId = #{mainRoleId}
			,lastLoginTime = #{lastLoginTime}
			,needSendBindMail = #{needSendBindMail}
			,lastDeviceId = #{lastDeviceId}
			,currPlatform = #{currPlatform}
			,appKey = #{appKey}
			,appVersion = #{appVersion}
			,country = #{country}
			,channel = #{channel}
            ,ip = #{ip}
		WHERE id = #{id}
	</update>
	
	<delete id="delete" parameterType="Long">
		DELETE FROM account WHERE id = #{id}
	</delete>

</mapper>
	