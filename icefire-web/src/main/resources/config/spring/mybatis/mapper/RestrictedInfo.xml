<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.RestrictedInfoMapper">
    <resultMap id="restrictedInfo" type="com.lc.billion.icefire.web.bus.risk.entity.RestrictedInfo">
        <result property="id" column="id"/>
        <result property="keyInfo" column="key_info"/>
        <result property="createTime" column="create_time"/>
        <result property="auth" column="auth"/>
        <result property="typeId" column="type_id"/>
    </resultMap>

    <select id="getAll" resultMap="restrictedInfo">
        select id, key_info, type_id, create_time, auth
        from restricted_info
    </select>
    <select id="findOne" resultType="com.lc.billion.icefire.web.bus.risk.entity.RestrictedInfo">
        select id, key_info, create_time, auth, type_id
        from restricted_info
        where key_info = #{keyInfo}
          and type_id = #{typeId}
    </select>

    <select id="searchInfo" resultType="com.lc.billion.icefire.web.bus.risk.entity.RestrictedInfo"
            resultMap="restrictedInfo">
        select id, key_info, type_id, create_time, auth
        from restricted_info
        where key_info like CONCAT('%', #{keyInfo}, '%')
    </select>

    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.risk.entity.RestrictedInfo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into restricted_info(key_info, create_time, auth, type_id)
        values (#{keyInfo}, #{createTime}, #{auth}, #{typeId})
    </insert>

    <insert id="insertMany" parameterType="list" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert ignore into restricted_info (key_info, create_time, auth, type_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.keyInfo}, #{item.createTime}, #{item.auth}, #{item.typeId})
        </foreach>
    </insert>

    <delete id="deleteOne" parameterType="com.lc.billion.icefire.web.bus.risk.entity.RestrictedInfo">
        delete
        from restricted_info
        where key_info = #{keyInfo}
          and type_id = #{typeId}
    </delete>


</mapper>
