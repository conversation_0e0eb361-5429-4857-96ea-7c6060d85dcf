<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.AccountBindMapper">
	<resultMap id="accountBind" type="com.lc.billion.icefire.web.bus.api.account.entity.AccountBind">
		<result property="id" column="id"/>
		<result property="platformId" column="platformId"/>
		<result property="platform" column="platform"/>
		<result property="bindTime" column="bindTime"/>
		<result property="accId" column="accId" />
	</resultMap>

	<select id="findById" resultMap="accountBind" parameterType="Long">
		SELECT
			id
			,platformId
			,platform
			,bindTime
			,accId
		FROM account_bind
		WHERE id = #{id}
	</select>
	
	<select id="findByPlatformIdAndPlatform" resultMap="accountBind">
		SELECT
			id
			,platformId
			,platform
			,bindTime
			,accId
		FROM account_bind
		WHERE platformId = #{platformId}
		AND platform = #{platform}
	</select>

	<select id="findByPlatform" resultMap="accountBind">
		SELECT
			id
			,platformId
			,platform
			,bindTime
			,accId
		FROM account_bind
		WHERE platformId = #{platformId}
	</select>
	
	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.api.account.entity.AccountBind" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		INSERT INTO account_bind(
			platformId
			,platform
			,bindTime
			,accId
		) VALUES(
			#{platformId}
			,#{platform}
			,#{bindTime}
			,#{accId}
		)
	</insert>
	
	<update id="update" parameterType="com.lc.billion.icefire.web.bus.api.account.entity.AccountBind">
		UPDATE account_bind SET
			bindTime = #{bindTime}
			,platformId = #{platformId}
			,platform = #{platform}
			,accId=#{accId}
		WHERE id = #{id}
	</update>
	
	<delete id="delete" parameterType="Long">
		DELETE FROM account_bind WHERE id = #{id}
	</delete>

	<select id="findByAccId" resultMap="accountBind" parameterType="Long">
		SELECT
		id
		,platformId
		,platform
		,bindTime
		,accId
		FROM account_bind
		WHERE accId = #{accId}
	</select>

	<select id="findAllPlatformByAccId" resultType="java.lang.String" parameterType="Long">
		SELECT
		platform
		FROM account_bind
		WHERE accId = #{accId}
	</select>
	
	<select id="findByPlatformIdAndPlatformLike" resultMap="accountBind">
		select
			id
			,platformId
			,platform
			,bindTime
		from account_bind
		where platform = #{platform}
		and platformId like CONCAT(#{platformId},'%')
	</select>

</mapper>
	