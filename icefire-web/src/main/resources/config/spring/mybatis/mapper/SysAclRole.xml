<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.SysAclRoleMapper">

    <resultMap id="sysAclRole" type="com.lc.billion.icefire.web.bus.user.entity.SysAclRole">
        <id property="pid" column="pid" />
        <id property="rid" column="rid"/>
    </resultMap>
    
    <select id="deleteByRid" resultMap="sysAclRole" parameterType="Long">
        DELETE
        FROM sys_acl_role
        WHERE rid = #{rid}
    </select>

    <select id="deleteByPid" resultMap="sysAclRole" parameterType="Long">
        DELETE
        FROM sys_acl_role
        WHERE pid = #{pid}
    </select>

    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.user.entity.SysAclRole">
		insert into sys_acl_role (
			pid
			,rid
		) values (
		    #{pid}
		    ,#{rid}
		)
	</insert>
    
    <update id="update" parameterType="com.lc.billion.icefire.web.bus.user.entity.SysAclRole">
        UPDATE sys_acl_role SET
        pid = #{pid}
        ,rid = #{rid}
        WHERE pid = #{pid} and rid = #{rid}
    </update>
    
</mapper>