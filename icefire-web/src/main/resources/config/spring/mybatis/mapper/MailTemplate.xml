<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.MailTemplateMapper">
    <!-- 基本结果映射 -->
    <resultMap id="MailTemplate" type="com.lc.billion.icefire.web.bus.gm.model.MailTemplate">
        <id column="id" property="id" />
        <result column="operator" property="operator" />
        <result column="name" property="name" />
        <result column="mail_type" property="mailType" />
        <result column="mail_content" property="mailContent" />
        <result column="creator" property="creator" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 查询：通过ID查询记录 -->
    <select id="selectById" parameterType="int" resultMap="MailTemplate">
        SELECT
            *
        FROM
            mail_template
        WHERE
            id = #{id}
    </select>

    <!-- 查询：获取所有记录 -->
    <select id="selectAll" resultMap="MailTemplate">
        SELECT
            *
        FROM
            mail_template
    </select>

    <!-- 插入：新增记录 -->
    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.MailTemplate" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO mail_template
        (name, mail_type, mail_content, creator, updated_at, created_at)
        VALUES
            (#{name}, #{mailType}, #{mailContent}, #{creator}, #{updatedAt}, #{createdAt})
    </insert>

    <!-- 删除：通过ID删除记录 -->
    <delete id="delete" parameterType="int">
        DELETE FROM mail_template
        WHERE
            id = #{id}
    </delete>


    <select id="selectPageByParam" resultMap="MailTemplate" parameterType="java.util.Map">
        select *
        from mail_template where 1=1
        <if test="mailType != null">
            AND `mail_type` = #{mailType}
        </if>
        order by `created_at` desc limit #{offset}, #{limit}
    </select>

    <select id="selectCountByParam" resultType="int" parameterType="java.util.Map">
        select count(1)
        from mail_template where 1=1
        <if test="mailType != null">
            AND `mail_type` = #{mailType}
        </if>
    </select>
</mapper>