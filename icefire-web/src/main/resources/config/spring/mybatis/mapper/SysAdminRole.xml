<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.SysAdminRoleMapper">

    <resultMap id="sysAdminRole" type="com.lc.billion.icefire.web.bus.user.entity.SysAdminRole">
        <id property="uid" column="uid" />
        <id property="rid" column="rid"/>
    </resultMap>
    
    <select id="deleteByUid" resultMap="sysAdminRole" parameterType="Long">
        DELETE
        FROM sys_admin_role
        WHERE uid = #{uid}
    </select>

    <select id="deleteByRid" resultMap="sysAdminRole" parameterType="Long">
        DELETE
        FROM sys_admin_role
        WHERE rid = #{rid}
    </select>

    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.user.entity.SysAdminRole">
		insert into sys_admin_role (
			uid
			,rid
		) values (
		    #{uid}
		    ,#{rid}
		)
	</insert>
    
    <update id="update" parameterType="com.lc.billion.icefire.web.bus.user.entity.SysAdminRole">
        UPDATE sys_admin_role SET
        uid = #{uid}
        ,rid = #{rid}
        WHERE uid = #{uid} and rid = #{rid}
    </update>
    
</mapper>