<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.UserMapper">
	<resultMap id="user" type="com.lc.billion.icefire.web.bus.user.entity.User">
		<result property="id" column="id"/>
		<result property="roleId" column="role_id"/>
		<result property="type" column="type"/>
		<result property="serverId" column="server_id"/>
		<result property="whitelist" column="whitelist"/>
		<result property="createTime" column="create_time"/>
		<result property="euopen" column="euopen"/>
		<result property="chatProtocal" column="chat_protocal"/>
		<result property="roleName" column="role_name"/>
		<result property="roleHead" column="role_head"/>
		<result property="roleMainCityLevel" column="role_main_city_level"/>
		<result property="roleAllianceName" column="role_alliance_name"/>
        <result property="roleAllianceAliasName" column="role_alliance_alias_name"/>
		<result property="lockStatus" column="lock_status"/>
		<result property="lockEndTime" column="lock_end_time"/>
		<result property="isGM" column="isGM"/>
		<result property="isSendBindMail" column="is_send_bind_mail"/>
		<result property="lastLoginTime" column="last_login_time"/>
		<result property="accId" column="acc_id"/>
		<result property="power" column="power"/>
		<result property="activeServerId" column="active_server_id"/>
		<result property="targetServerId" column="target_server_id"/>
		<result property="loginCnt" column="login_cnt"/>
		<result property="allianceId" column="alliance_id"/>
	</resultMap>
	
	<sql id="queryConditionUser">
		<where>
			<if test="id != null">AND id = #{id}</if>
			<if test="roleId != null">AND role_id = #{roleId}</if>
			<if test="type != null">AND type = #{type}</if>
			<if test="isGM == true">AND isGM=true</if>
		</where>
	</sql>
	
	<select id="selectById" resultMap="user" parameterType="Long">
		select
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,euopen
		,chat_protocal
		,role_name
		,role_head
		,role_main_city_level
		,role_alliance_name
        ,role_alliance_alias_name
		,lock_status
		,lock_end_time
		,isGM
		,is_send_bind_mail
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		, login_cnt
		, alliance_id
		from user
		where id = #{id}
	</select>

	<select id="selectByRoleId" resultMap="user" parameterType="Long">
		select
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,euopen
		,chat_protocal
		,role_name
		,role_head
		,role_main_city_level
		,role_alliance_name
        ,role_alliance_alias_name
		,lock_status
		,lock_end_time
		,isGM
		,is_send_bind_mail
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		, login_cnt
		, alliance_id
		from user
		where role_id = #{id}
	</select>
	
	<select id="selectByCreateTime" resultMap="user" parameterType="map">
		select
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,euopen
		,chat_protocal
		,role_name
		,role_head
		,role_main_city_level
		,role_alliance_name
        ,role_alliance_alias_name
		,lock_status
		,lock_end_time
		,isGM
		,is_send_bind_mail
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		, login_cnt
		, alliance_id
		from user
		
		<![CDATA[
        where create_time >= #{startTime} and create_time <= #{endTime}
        ]]>
	</select>

	<select id="selectByRoleNameList" resultMap="user" parameterType = "java.util.List">
		select
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,euopen
		,chat_protocal
		,role_name
		,role_head
		,role_main_city_level
		,role_alliance_name
        ,role_alliance_alias_name
		,lock_status
		,lock_end_time
		,isGM
		,is_send_bind_mail
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		,login_cnt
		, alliance_id
		from user
		where (server_id,binary role_name) in(
		<foreach collection="list" item="item" separator=",">
			(
			#{item.serverId}
			,#{item.roleName}
			)
		</foreach>
		)
	</select>

	<select id="selectByRoleIdList" resultMap="user" parameterType = "java.util.List">
		select
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,euopen
		,chat_protocal
		,role_name
		,role_head
		,role_main_city_level
		,role_alliance_name
        ,role_alliance_alias_name
		,lock_status
		,lock_end_time
		,isGM
		,is_send_bind_mail
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		,login_cnt
		, alliance_id
		from user
		where role_id in
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.user.entity.User" useGeneratedKeys="true" keyProperty="id"  keyColumn="id">
		insert into user(
		    role_id
			,type
			,server_id
			,whitelist
			,create_time
			,euopen
			,chat_protocal
			,role_name
			,role_head
			,role_main_city_level
			,role_alliance_name
            ,role_alliance_alias_name
			,lock_status
			,lock_end_time
			,isGM
			,is_send_bind_mail
			,last_login_time
			,acc_id
			,power
			,active_server_id
			,target_server_id
						, login_cnt
						, alliance_id
		) values(
			#{roleId}
			,#{type}
			,#{serverId}
			,#{whitelist}
			,#{createTime}
			,#{euopen}
			,#{chatProtocal}
			,#{roleName}
			,#{roleHead}
			,#{roleMainCityLevel}
			,#{roleAllianceName}
            ,#{roleAllianceAliasName}
			,#{lockStatus}
			,#{lockEndTime}
			,#{isGM}
			,#{isSendBindMail}
			,#{lastLoginTime}
			,#{accId}
			,#{power}
			,#{activeServerId}
			,#{targetServerId}
				, #{loginCnt}
				, #{allianceId}
		)
	</insert>

	<update id="update" parameterType="com.lc.billion.icefire.web.bus.user.entity.User">
		update user set
			role_id = #{roleId}
			,whitelist = #{whitelist}
			,euopen = #{euopen}
		    ,chat_protocal = #{chatProtocal}
			,role_name = #{roleName}
			,role_head = #{roleHead}
			,role_main_city_level = #{roleMainCityLevel}
			,role_alliance_name = #{roleAllianceName}
            ,role_alliance_alias_name = #{roleAllianceAliasName}
			,lock_status = #{lockStatus}
			,lock_end_time = #{lockEndTime}
			,isGM = #{isGM}
			,is_send_bind_mail = #{isSendBindMail}
			,last_login_time = #{lastLoginTime}
			,acc_id = #{accId}
			,power = #{power}
					  , login_cnt=#{loginCnt}
					  , alliance_id=#{allianceId}
		where id = #{id}
	</update>


	<update id="updateRoleBaseInfo" parameterType="com.lc.billion.icefire.web.bus.user.entity.User">
		update user set
			role_id = #{roleId}
					  ,role_name = #{roleName}
					  ,role_head = #{roleHead}
					  ,role_main_city_level = #{roleMainCityLevel}
					  ,role_alliance_name = #{roleAllianceName}
					  ,role_alliance_alias_name = #{roleAllianceAliasName}
					  ,power = #{power}
					  , alliance_id=#{allianceId}
		where id = #{id}
	</update>

	<delete id="delete" parameterType="Long">
		delete from user where id = #{id}
	</delete>

    <select id="findByRoleId" resultMap="user" parameterType="Long">
		select
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,euopen
		,chat_protocal
		,role_name
		,role_head
		,role_main_city_level
		,role_alliance_name
        ,role_alliance_alias_name
		,lock_status
		,lock_end_time
		,isGM
		,is_send_bind_mail
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		, login_cnt
		, alliance_id
		from user
		where role_id = #{roleId}
	</select>

	<select id="findByGM" resultMap="user">
		select
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,euopen
		,chat_protocal
		,role_name
		,role_head
		,role_main_city_level
		,role_alliance_name
        ,role_alliance_alias_name
		,lock_status
		,lock_end_time
		,isGM
		,is_send_bind_mail
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		,login_cnt
		, alliance_id
		from user
		<where>
			isGM = true
			<if test="id != null">AND id = #{id}</if>
			<if test="serverId != null">AND server_id = #{serverId}</if>
		</where>
	</select>

	<select id="findByName" resultMap="user" parameterType="map">
		select
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,euopen
		,chat_protocal
		,role_name
		,role_head
		,role_main_city_level
		,role_alliance_name
        ,role_alliance_alias_name
		,lock_status
		,lock_end_time
		,isGM
		,is_send_bind_mail
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		, login_cnt
		, alliance_id
		from user
		where binary role_name=#{roleName} and server_id=#{serverId}
	</select>


	<select id="selectByAccId" resultMap="user" parameterType="Long">
		SELECT
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,euopen
		,chat_protocal
		,role_name
		,role_head
		,role_main_city_level
		,role_alliance_name
        ,role_alliance_alias_name
		,lock_status
		,lock_end_time
		,isGM
		,is_send_bind_mail
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		, login_cnt
		, alliance_id
		FROM user
		WHERE acc_id = #{accId}
	</select>

	<select id="findBanAccList" resultMap="user">
		select
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,role_name
		,role_main_city_level
		,lock_end_time
		,isGM
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		,login_cnt
		from user
		where lock_status = #{lock_status}
		limit #{limit}, #{count}
	</select>

	<select id="selectUserAmountByAccId" resultType="int" parameterType="Long">
		SELECT
		count(1)
		FROM user
		WHERE acc_id = #{accId}
	</select>

	<select id="selectUserAmountByAccIdAndServerId" resultType="int">
		SELECT
		count(1)
		FROM user
		WHERE acc_id = #{accId} AND server_id = #{serverId}
	</select>

	<select id="selectServerIdsByRoleIds" resultMap="user" parameterType = "java.util.List">
		SELECT
		role_id
		,server_id
		FROM user
		WHERE role_id IN
		<foreach collection="list" item="roleIdList" open="(" separator="," close=")">
			#{roleIdList}
		</foreach>
	</select>

	<update id="updateSrcServer" parameterType="map">
		update user set
		server_id = #{serverId}
		where role_id = #{roleId}
	</update>

	<select id="fuzzyQueryUserByName" resultMap="user">
		select
		id
		,role_id
		,type
		,server_id
		,whitelist
		,create_time
		,euopen
		,chat_protocal
		,role_name
		,role_head
		,role_main_city_level
		,role_alliance_name
        ,role_alliance_alias_name
		,lock_status
		,lock_end_time
		,isGM
		,is_send_bind_mail
		,last_login_time
		,acc_id
		,power
		,active_server_id
		,target_server_id
		, login_cnt
		, alliance_id
		from user
		where role_name like #{name};
	</select>
	<update id="updateActiveServer" parameterType="map">
		update user set
		active_server_id = #{activeServerId}
		where role_id = #{roleId}
	</update>
</mapper>
	