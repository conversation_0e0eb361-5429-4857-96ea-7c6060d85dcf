<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.WechatAccountMapper">
    <resultMap id="wechatAccount" type="com.lc.billion.icefire.web.bus.wechat.entity.WechatAccount">
        <id property="accId" column="accId"/>
        <result property="wxOpenId" column="wx_open_id"/>
        <result property="wxUnionId" column="wx_union_id"/>
    </resultMap>
    
    <select id="selectByAccId" resultMap="wechatAccount" parameterType="Long">
        SELECT *
        FROM wechat_account
        WHERE accId = #{accId}
    </select>
    <select id="selectByOpenId" resultMap="wechatAccount" parameterType="String">
        SELECT *
        FROM wechat_account
        WHERE wx_open_id = #{wxOpenId}
    </select>
    <update id="update" parameterType="com.lc.billion.icefire.web.bus.wechat.entity.WechatAccount">
        UPDATE wechat_account SET
        wx_open_id = #{wxOpenId}
        ,wx_union_id = #{wxUnionId}
        WHERE accId = #{accId}
    </update>
    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.wechat.entity.WechatAccount">
        insert into wechat_account (
        accId,
        wx_open_id
        ,wx_union_id
        ) values (
        #{accId},
        #{wxOpenId},
        #{wxUnionId}
        )
    </insert>
</mapper>