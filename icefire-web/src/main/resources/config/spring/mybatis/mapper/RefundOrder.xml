<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.RefundOrderMapper">
    <resultMap id="refundOrder" type="com.lc.billion.icefire.web.bus.gm.model.RefundOrder">
        <result property="platform" column="platform"/>
        <result property="originId" column="platform_order_id"/>
        <result property="refundTime" column="refund_time"/>
        <result property="reason" column="reason"/>
        <result property="id" column="id"/>
        <result property="productId" column="product_id"/>
        <result property="currency" column="currency"/>
        <result property="roleId" column="role_id"/>
        <result property="serverId" column="server_id"/>
    </resultMap>


    <select id="selectOrders" resultMap="refundOrder" parameterType="int">
        select `platform`, `platform_order_id`, `refund_time`, `reason`, `id`, `product_id`,`currency`,`role_id`, `server_id`
        from refund_order order by `refund_time` desc limit 0, #{limit}
    </select>


    <select id="selectOrdersByRole" resultMap="refundOrder" parameterType="java.util.Map">
        select `platform`, `platform_order_id`, `refund_time`, `reason`, `id`, `product_id`,`currency`,`role_id`, `server_id`
         from refund_order where 1=1
            <if test="roleId != null">
                AND `role_id` = #{roleId}
            </if>
            <if test="startTime != null">
                AND `refund_time` &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND `refund_time` &lt;= #{endTime}
            </if>
            order by `refund_time` desc limit #{pageStart}, #{limitSize}
    </select>


    <select id="selectOrdersWithOutRoleId" resultMap="refundOrder" parameterType="java.util.Map">
        select `platform`, `platform_order_id`, `refund_time`, `reason`, `id`, `product_id`,`currency`,`role_id`, `server_id`
        from refund_order where `role_id` = 0
        <if test="startTime != null">
            AND `refund_time` &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND `refund_time` &lt;= #{endTime}
        </if>
        order by `refund_time` desc
        <if test="limit != null">
            limit 0, #{limit}
        </if>
    </select>


    <!-- 多条件查询  -->


    <!--
    replace into `refund_order` (`platform`, `platform_order_id`, `refund_time`, `reason`, `id`, `product_id`,`role_id`, `server_id`)
    -->

    <insert id="insertBatch" parameterType="java.util.List">
        insert ignore into `refund_order` (`platform`, `platform_order_id`, `refund_time`, `reason`, `id`, `product_id`,`currency`,`role_id`, `server_id`)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.platform}, #{item.originId}, #{item.refundTime}, #{item.reason},
            #{item.id}, #{item.productId}, #{item.currency}, #{item.roleId}, #{item.serverId})
        </foreach>
    </insert>


    <!--  mysql 连接参数需要添加 allowMultiQueries=true -->
<!--    <update id="updateBatch" parameterType="java.util.List">-->
<!--        <foreach collection="list" item="item" index="index" open="" close="" separator=";">-->
<!--            update `refund_order`-->
<!--            <set>-->
<!--                `id`=#{item.id},-->
<!--                `product_id`=#{item.productId},-->
<!--                `currency`=#{item.currency},-->
<!--                `role_id`=#{item.roleId},-->
<!--                `server_id`=#{item.serverId}-->
<!--            </set>-->
<!--            <where>-->
<!--                `platform`=#{item.platform} and `platform_order_id`= #{item.originId}-->
<!--            </where>-->
<!--        </foreach>-->
<!--    </update>-->
    <insert id="updateBatch" parameterType="java.util.List">
        replace into `refund_order` (`platform`, `platform_order_id`, `refund_time`, `reason`, `id`, `product_id`,`currency`,`role_id`, `server_id`)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.platform}, #{item.originId}, #{item.refundTime}, #{item.reason},
            #{item.id}, #{item.productId}, #{item.currency}, #{item.roleId}, #{item.serverId})
        </foreach>
    </insert>

</mapper>
	