<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.ImportRewardMapper">
	<resultMap id="importReward" type="com.lc.billion.icefire.web.bus.gm.model.ImportReward">
		<result property="id" column="id"/>
        <result property="successCount" column="success_count"/>
        <result property="totalCount" column="total_count"/>
		<result property="options" column="options"/>
		<result property="optionTime" column="option_time"/>
        <result property="comment" column="comment"/>
	</resultMap>
	
	<select id="selectOptionsById" resultMap="importReward"  parameterType="Long">
        select
        id
        ,success_count
        ,total_count
        ,options
        ,option_time
        ,comment
        from import_reward
        where id = #{id}
    </select>
    
    <select id="selectImportReward" resultMap="importReward" >
        select
        id
        ,success_count
        ,total_count
        ,options
        ,option_time
        ,comment
        from import_reward
    </select>

    <select id="countTotalNum" resultType="int">
        select COUNT(*) from import_reward;
    </select>

    <select id="selectByPage" resultMap="importReward">
        select * from import_reward order by `option_time` desc limit #{offset}, #{limit};
    </select>
    
	<insert id="insertImportReward" parameterType="com.lc.billion.icefire.web.bus.gm.model.ImportReward" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into import_reward(
            success_count
            ,total_count
            ,options
            ,option_time
		) values(
            #{successCount}
            ,#{totalCount}
			,#{options}
			,#{optionTime}
		)
		<selectKey keyProperty="id" resultType="int" order="AFTER">  
            SELECT LAST_INSERT_ID();  
        </selectKey>  
	</insert>

    <update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.ImportReward">
        update import_reward set
        comment = #{comment}
        where id = #{id}
    </update>

    <delete id="delete" parameterType="Long">
        delete from import_reward where id = #{id}
    </delete>

</mapper>
	