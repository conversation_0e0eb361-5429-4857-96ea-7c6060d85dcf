<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.LanguageMapper">
	<resultMap id="langEmail" type="com.lc.billion.icefire.web.bus.gm.model.LanguageEmail">
		<result property="key" column="key"/>
		<result property="category" column="category"/>
		<result property="content" column="language"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
	</resultMap>
	
	<select id="selectAll" resultMap="langEmail">
        select
            `key`
            ,`category`
            ,`language`
            ,`create_time`
            ,`update_time`
        from lang_email
        order by create_time desc;
    </select>

	<select id="selectByCategory" resultMap="langEmail" parameterType="java.lang.Integer">
		select
		`key`
		,`category`
		,`language`
		,`create_time`
		,`update_time`
		from lang_email
		<where>
			<if test="category !=null and category>0">
				`category` = #{category}
			</if>
		</where>
		order by create_time desc;
	</select>
	
	<select id="selectById" resultMap="langEmail" parameterType="java.lang.String">
		select
		`key`
		,`category`
		,`language`
		,`create_time`
		,`update_time`
		from lang_email
		where `key` = #{key}
	</select>
	
	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.LanguageEmail">
		insert into lang_email(
		    `key`
		    ,`category`
			,`language`
			,`create_time`
			,`update_time`
		) values(
			#{key}
			,#{category}
			,#{content}
			,#{createTime}
			,#{updateTime}
		)  
	</insert>
	
	<update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.LanguageEmail">
		update lang_email set
			`category`=#{category}
		  	,`language` = #{content}
		  	,`update_time` = #{updateTime}
		where `key` = #{key}
	</update>
	
	<delete id="delete" parameterType="java.lang.String">
		delete from lang_email where `key` = #{key}
	</delete>

</mapper>
	