<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.WelfareAccountMapper">
	<resultMap id="welfareAccount" type="com.lc.billion.icefire.web.bus.api.entity.WelfareAccount">
		<result property="roleId" column="role_id"/>
		<result property="name" column="name"/>
		<result property="remarks" column="remarks"/>
		<result property="setLimit" column="set_limit"/>
		<result property="curLimit" column="cur_limit"/>
		<result property="totalLimit" column="total_limit"/>
		<result property="setTime" column="set_time"/>
	</resultMap>

	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.api.entity.WelfareAccount">
		insert into welfare_account (
			role_id
			,name
			,remarks
			,set_limit
			,cur_limit
			,total_limit
			,set_time
		) values (
		    #{roleId}
		    ,#{name}
		    ,#{remarks}
			,#{setLimit}
			,#{curLimit}
			,#{totalLimit}
			,#{setTime}
		)
	</insert>

	<select id="selectByRoleId" resultMap="welfareAccount" parameterType="Long">
		select
		role_id
		,name
		,remarks
		,set_limit
		,cur_limit
		,total_limit
		,set_time
		from welfare_account where role_id = #{roleId}
	</select>
	
    <update id="update" parameterType="com.lc.billion.icefire.web.bus.api.entity.WelfareAccount">
        update welfare_account set
		name = #{name}
		,remarks = #{remarks}
		,set_limit = #{setLimit}
        ,cur_limit = #{curLimit}
        ,total_limit = #{totalLimit}
		,set_time = #{setTime}
        where role_id = #{roleId}
    </update>
	
	<delete id="delete" parameterType="Long">
		delete from welfare_account where role_id = #{roleId}
	</delete>
	
	<select id="selectAll" resultMap="welfareAccount" >
		select
		role_id
		,name
		,remarks
		,set_limit
		,cur_limit
		,total_limit
		,set_time
		from welfare_account
	</select>
</mapper>
	