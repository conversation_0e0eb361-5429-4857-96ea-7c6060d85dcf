<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.WechatRewardInfoMapper">
    <resultMap id="wechatRewardInfo" type="com.lc.billion.icefire.web.bus.wechat.entity.WechatRewardInfo">
        <id property="id" column="id"/>
        <result property="typeId" column="type_id"/>
        <result property="accountId" column="account_id"/>
        <result property="serverId" column="server_id"/>
        <result property="roleId" column="role_id"/>
        <result property="rewardStatus" column="reward_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="selectByTypeIdAndAccountId" resultMap="wechatRewardInfo" parameterType="map">
        SELECT *
        FROM `wechat_reward_info`
        WHERE account_id = #{accountId}
          AND type_id = #{typeId}
        LIMIT 0,1
    </select>
    <update id="updateRewardStatus" parameterType="com.lc.billion.icefire.web.bus.wechat.entity.WechatRewardInfo">
        UPDATE `wechat_reward_info`
        SET reward_status = #{rewardStatus},
            update_time   = #{updateTime}
        WHERE type_id = #{typeId}
          AND account_id = #{accountId}
        LIMIT 0,1
    </update>
    <insert id="updateInsert" parameterType="com.lc.billion.icefire.web.bus.wechat.entity.WechatRewardInfo">
        INSERT INTO `wechat_reward_info` (type_id, account_id, server_id, role_id, reward_status, create_time,
                                          update_time)
        VALUES (#{typeId}, #{accountId}, #{serverId}, #{roleId}, #{rewardStatus}, #{createTime},
                #{updateTime})
        ON DUPLICATE KEY
            UPDATE reward_status = #{rewardStatus},
                   update_time= #{updateTime}
    </insert>
</mapper>