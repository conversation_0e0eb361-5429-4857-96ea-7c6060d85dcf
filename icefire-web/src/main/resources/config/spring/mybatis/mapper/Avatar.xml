<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.AvatarMapper">
	<resultMap id="avatarUploadRecord" type="com.lc.billion.icefire.web.bus.gm.model.AvatarUploadRecord">
		<result property="roleId" column="roleId"/>
		<result property="serverId" column="serverId"/>
		<result property="createTime" column="createTime"/>
		<result property="avatarURL" column="avatarURL"/>
		<result property="errorCode" column="errorCode"/>
		<result property="pass" column="pass"/>
		<result property="checkParams" column="checkParams"/>
	</resultMap>
	<resultMap id="avatarReportRecord" type="com.lc.billion.icefire.web.bus.gm.model.AvatarReportRecord">
		<result property="roleId" column="roleId"/>
		<result property="amount" column="amount"/>
		<result property="lastTime" column="lastTime"/>
		<result property="avatarURL" column="avatarURL"/>
	</resultMap>

	<select id="selectAllReportRecord" resultMap="avatarReportRecord">
		select
		`roleId`
		,`amount`
		,`lastTime`
		,`avatarURL`
		from avatar_report_record order by amount desc;
	</select>

	<select id="selectReportMax" resultType="int">
		select count(1) from avatar_report_record;
	</select>

	<select id="selectReportRecordPagination" resultMap="avatarReportRecord">
		select
		`roleId`
		,`amount`
		,`lastTime`
		,`avatarURL`
		from avatar_report_record order by amount desc limit #{page}, #{pageSize} ;
	</select>

	<select id="selectReportByRoleId" resultMap="avatarReportRecord" parameterType="long">
		select
		`roleId`
		,`amount`
		,`lastTime`
		,`avatarURL`
		from avatar_report_record
		where `roleId` = #{roleId}
	</select>

	<insert id="insertReport" parameterType="com.lc.billion.icefire.web.bus.gm.model.AvatarUploadRecord">
		insert into avatar_report_record(
		`roleId`
		,`amount`
		,`lastTime`
		,`avatarURL`
		) values(
		#{roleId}
		,#{amount}
		,#{lastTime}
		,#{avatarURL}
		)
	</insert>

	<update id="updateReport" parameterType="com.lc.billion.icefire.web.bus.gm.model.AvatarUploadRecord">
		update avatar_report_record set
		`amount` = #{amount}
		,`lastTime` = #{lastTime}
		,`avatarURL` = #{avatarURL}
		where `roleId` = #{roleId}
	</update>

	<delete id="deleteReport" parameterType = "java.util.List">
		delete from avatar_report_record  where `roleId` in
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<delete id="deleteOverdueReport">
		delete from avatar_report_record  where (REPLACE(UNIX_TIMESTAMP(current_timestamp(3)),'.','') - `lastTime`) > 30  * 24 * 60 * 1000;
	</delete>

	<select id="selectAllUploadRecord" resultMap="avatarUploadRecord">
		select
		`roleId`
		,`serverId`
		,`createTime`
		,`avatarURL`
		,`errorCode`
		,`pass`
		,`checkParams`
		from avatar_upload_record;
	</select>

	<select id="selectAllPassedUploadRecord" resultMap="avatarUploadRecord">
		select
		`roleId`
		,`serverId`
		,`createTime`
		,`avatarURL`
		,`errorCode`
		,`pass`
		,`checkParams`
		from avatar_upload_record
		where pass is true;
	</select>

	<select id="selectAllNotPassUploadRecord" resultMap="avatarUploadRecord">
		select
		`roleId`
		,`serverId`
		,`createTime`
		,`avatarURL`
		,`errorCode`
		,`pass`
		,`checkParams`
		from avatar_upload_record
		where pass is false;
	</select>

	<select id="selectUploadByRoleId" resultMap="avatarUploadRecord" parameterType="long">
		select
		`roleId`
		,`serverId`
		,`createTime`
		,`avatarURL`
		,`errorCode`
		,`pass`
		,`checkParams`
		from avatar_upload_record
		where `roleId` = #{roleId}
	</select>

	<select id="selectUploadParamByRoleIdList" resultType="java.util.HashMap" parameterType = "java.util.List">
		select
		`roleId`
		,`avatarURL`
		from avatar_upload_record
		where `roleId` in
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectPassedUploadMax" resultType="int">
		select count(1) from avatar_upload_record where pass is true;
	</select>

	<select id="selectNotPassUploadMax" resultType="int">
		select count(1) from avatar_upload_record where pass is false;
	</select>

	<select id="selectPassedUploadRecordPagination" resultMap="avatarUploadRecord">
		select
		`roleId`
		,`serverId`
		,`createTime`
		,`avatarURL`
		,`errorCode`
		,`pass`
		,`checkParams`
		from avatar_upload_record where pass is true limit #{page}, #{pageSize} ;
	</select>

	<select id="selectNotPassUploadRecordPagination" resultMap="avatarUploadRecord">
		select
		`roleId`
		,`serverId`
		,`createTime`
		,`avatarURL`
		,`errorCode`
		,`pass`
		,`checkParams`
		from avatar_upload_record where pass is not true limit #{page}, #{pageSize} ;
	</select>

	<insert id="insertUpload" parameterType="com.lc.billion.icefire.web.bus.gm.model.AvatarUploadRecord">
		insert into avatar_upload_record(
		`roleId`
		,`serverId`
		,`createTime`
		,`avatarURL`
		,`errorCode`
		,`pass`
		,`checkParams`
		) values(
		#{roleId}
		,#{serverId}
		,#{createTime}
		,#{avatarURL}
		,#{errorCode}
		,#{pass}
		,#{checkParams}
		)
	</insert>

	<update id="updateUpload" parameterType="com.lc.billion.icefire.web.bus.gm.model.AvatarUploadRecord">
		update avatar_upload_record set
		`serverId` = #{serverId}
		,`createTime` = #{createTime}
		,`avatarURL` = #{avatarURL}
		,`errorCode` = #{errorCode}
		,`pass` = #{pass}
		,`checkParams` = #{checkParams}
		where `roleId` = #{roleId}
	</update>

	<delete id="deleteUpload" parameterType = "java.util.List">
		delete from avatar_upload_record  where roleId in
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<delete id="deleteOverdueUpload">
		delete from avatar_upload_record  where (REPLACE(UNIX_TIMESTAMP(current_timestamp(3)),'.','') - `createTime`) > (30 * 24 * 60 * 60 * 1000);
	</delete>

</mapper>
	