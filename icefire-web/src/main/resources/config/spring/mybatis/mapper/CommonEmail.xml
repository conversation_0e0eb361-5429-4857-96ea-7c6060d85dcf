<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.CommonEmailMapper">
    <resultMap id="commonEmail" type="com.lc.billion.icefire.web.bus.gm.model.CommonEmail">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="serverIds" column="server_ids"/>
        <result property="sendCountrys" column="send_countrys"/>
        <result property="contentLang" column="content_lang"/>
        <result property="items" column="items"/>
        <result property="resources" column="resources"/>
        <result property="expireTime" column="expire_time"/>
        <result property="createTime" column="create_time"/>
        <result property="channelF" column="channel_f"/>
		<result property="title" column="title"/>
		<result property="sendLanguage" column="sendLanguage"/>
		<result property="levelLimit" column="levelLimit"/>
		<result property="validTime" column="validTime"/>
		<result property="roleIds" column="roleIds"/>
		<result property="applicant" column="applicant"/>
		<result property="approver" column="approver"/>
		<result property="passTime" column="pass_time"/>
		<result property="mailStatus" column="mail_status"/>
    </resultMap>

    <select id="selectAll" resultMap="commonEmail">
		select * from `commonemail` order by `id`
		desc
	</select>

    <select id="selectById" resultMap="commonEmail" parameterType="String">
		select *
		from `commonemail` where `id` = #{id}
	</select>

	<select id="countTotalNum" resultType="int">
		select COUNT(*) from commonemail;
	</select>

	<select id="selectByPage" resultMap="commonEmail">
		select * from commonemail order by `create_time` desc limit #{page}, #{pageSize};
	</select>

    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.CommonEmail">
		insert into `commonemail`(`type`,`server_ids`,`send_countrys`,`content_lang`,`items`,`resources`,`expire_time`,`create_time`,`channel_f`,`version`,`title`,`sendLanguage`,`levelLimit`,`validTime`,`roleIds`,`applicant`,`approver`,`pass_time`,`mail_status`)
		values(#{type},#{serverIds},#{sendCountrys},#{contentLang},#{items},#{resources},#{expireTime},#{createTime},#{channelF},#{version},#{title},#{sendLanguage},#{levelLimit},#{validTime},#{roleIds},#{applicant},#{approver},#{passTime},#{mailStatus})
	</insert>

    <update id="update" parameterType="com.lc.billion.icefire.web.bus.gm.model.CommonEmail">
		update `commonemail` set
		`id` = #{id}
		,`type` = #{type}
		,`server_ids` = #{serverIds}
		,`send_countrys` = #{sendCountrys}
		,`content_lang` = #{contentLang}
		,`items` = #{items}
		,`resources` = #{resources}
		,`expire_time` = #{expireTime}
		,`channel_f` = #{channelF}
		,`version` = #{version}
		,`title` = #{title}
		,`sendLanguage` = #{sendLanguage}
		,`levelLimit` = #{levelLimit}
		,`validTime` = #{validTime}
		,`roleIds` = #{roleIds}
		,`applicant` = #{applicant}
	    ,`approver` = #{approver}
	    ,`pass_time` = #{passTime}
		,`mail_status` = #{mailStatus}
		where `id` = #{id}
	</update>
    <delete id="delete" parameterType="String">
		delete from `commonemail` where `id` =
		#{id}
	</delete>

</mapper>
	