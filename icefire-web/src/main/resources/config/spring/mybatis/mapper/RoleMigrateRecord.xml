<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.RoleMigrateRecordMapper">
    <resultMap id="roleMigrateRecord" type="com.lc.billion.icefire.web.bus.gm.model.RoleMigrateRecord">
        <id property="id" column="id"/>
        <id property="roleId" column="role_id"/>
        <result property="srcServerId" column="src_server_id"/>
        <result property="toServerId" column="to_server_id"/>
        <result property="migType" column="mig_type"/>
        <result property="opDesc" column="op_desc"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    
    <select id="selectAll" resultMap="roleMigrateRecord">
    	SELECT * 
    	FROM role_migrate_record order by `create_time` desc limit 100
    </select>

    <select id="selectByRoleId" resultMap="roleMigrateRecord">
        SELECT *
        FROM role_migrate_record where `role_id` = #{roleId} order by `create_time` desc limit 100
    </select>
    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.gm.model.RoleMigrateRecord">
        insert into role_migrate_record (
        role_id
        ,src_server_id
        ,to_server_id
        ,op_desc
        ,mig_type
        ,create_time
        ) values (
         #{roleId}
        ,#{srcServerId}
        ,#{toServerId}
        ,#{opDesc}
        ,#{migType}
        ,#{createTime}
        )
    </insert>
</mapper>