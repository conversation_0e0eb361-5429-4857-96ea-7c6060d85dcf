<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.SubscriptionMapper">
	<resultMap id="subscription" type="com.lc.billion.icefire.web.bus.api.entity.Subscription">
		<result property="platOrderid" column="plat_orderid"/>
		<result property="userId" column="user_id"/>
		<result property="roleId" column="role_id"/>
		<result property="serverId" column="server_id"/>
		<result property="isTest" column="is_test"/>
		<result property="orderType" column="order_type"/>
		<result property="rechargeId" column="recharge_id"/>
		<result property="originRechargeId" column="origin_recharge_id"/>
		<result property="originPlatOrderid" column="origin_plat_orderid"/>
		<result property="productId" column="product_id"/>
		<result property="startTime" column="start_time"/>
		<result property="endTime" column="end_time"/>
		<result property="state" column="state"/>
		<result property="updateTime" column="update_time"/>
	</resultMap>
	
	<select id="selectById" resultMap="subscription" parameterType="String">
		select
		plat_orderid
		,user_id
		,role_id
		,server_id
		,is_test
		,order_type
		,recharge_id
		,origin_recharge_id
		,origin_plat_orderid
		,product_id
		,start_time
		,end_time
		,state
		,update_time
		from subscription where plat_orderid = #{platOrderid}
	</select>
	
	<insert id="insert" parameterType="com.lc.billion.icefire.web.bus.api.entity.Subscription">
		insert into subscription (
			plat_orderid
			,user_id
			,role_id
			,server_id
			,is_test
			,order_type
			,recharge_id
			,origin_recharge_id
			,origin_plat_orderid
			,product_id
			,start_time
			,end_time
			,state
			,update_time
		) values (
		    #{platOrderid}
		    ,#{userId}
		    ,#{roleId}
			,#{serverId}
			,#{isTest}
			,#{orderType}
			,#{rechargeId}
			,#{originRechargeId}
			,#{originPlatOrderid}
			,#{productId}
			,#{startTime}
			,#{endTime}
			,#{state}
			,#{updateTime}
		)
	</insert>
	
	<update id="update" parameterType="com.lc.billion.icefire.web.bus.api.entity.Subscription">
        update subscription set
        state = #{state}
        ,update_time = #{updateTime}
        where plat_orderid = #{platOrderid}
    </update>

</mapper>
	