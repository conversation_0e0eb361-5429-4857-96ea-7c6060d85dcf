<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.SysRoleMapper">
    <resultMap id="sysRole" type="com.lc.billion.icefire.web.bus.user.entity.SysRole">
        <id property="rid" column="rid" />
        <result property="rname" column="rname"/>
        <result property="rremark" column="rremark"/>
    </resultMap>
    <resultMap id="sysRoleSet" type="com.lc.billion.icefire.web.bus.user.entity.SysRole">
        <id property="rid" column="rid" />
        <result property="rname" column="rname"/>
        <result property="rremark" column="rremark"/>
        <collection property="sysAcls" ofType="com.lc.billion.icefire.web.bus.user.entity.SysAcl">
            <id property="pid" column="pid"/>
            <result property="pname" column="pname"/>
            <result property="url" column="url"/>
            <result property="filter" column="filter"/>
            <result property="premark" column="premark"/>
        </collection>
    </resultMap>
    
    <select id="selectByRid" resultMap="sysRole" parameterType="Integer">
        SELECT
        rid
        ,rname
        ,rremark
        FROM sys_role
        WHERE rid = #{rid}
    </select>
    
    <insert id="insert" parameterType="com.lc.billion.icefire.web.bus.user.entity.SysRole" useGeneratedKeys="true" keyProperty="rid" keyColumn="rid">
        INSERT INTO sys_role(
            rname
            ,rremark
        ) VALUES(
            #{rname}
            ,#{rremark}
        )
        <selectKey keyProperty="rid" order="AFTER" resultType="Integer">
            SELECT LAST_INSERT_ID();
        </selectKey>
    </insert>
    
    <update id="update" parameterType="com.lc.billion.icefire.web.bus.user.entity.SysRole">
        UPDATE sys_role SET
        rname = #{rname}
        ,rremark = #{rremark}
        WHERE rid = #{rid}
    </update>

    <select id="selectAllRoles" parameterType="String" resultMap="sysRole">
    	SELECT u.*
    	FROM sys_role u
    </select>
    
    <select id="selectRoles" parameterType="String" resultMap="sysRoleSet">
    	SELECT u.*, pr.*, p.*
    	FROM sys_role u
    		left JOIN sys_acl_role pr on pr.rid = u.rid
    		left JOIN sys_acl p on p.pid = pr.pid where u.rname != 'admin'
    </select>

    <select id="selectRoleByRid" parameterType="Integer" resultMap="sysRoleSet">
    	SELECT u.*, pr.*, p.*
    	FROM sys_role u
    		left JOIN sys_acl_role pr on pr.rid = u.rid
    		left JOIN sys_acl p on p.pid = pr.pid
    		where u.rid = #{rid}
    </select>
    
</mapper>