package com.lc.billion.icefire.game.msg.handler.impl.chat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.common.JsonUtils;
import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.common.TokenUtils;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.rpc.bean.ActivityCalendar;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.core.support.Time;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.async.allianceBattle.AllianceBattleMatchOperation;
import com.lc.billion.icefire.game.biz.battle.FightTest;
import com.lc.billion.icefire.game.biz.config.*;
import com.lc.billion.icefire.game.biz.dao.impl.RankDaoImpl;
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceMemberDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.*;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceBattleInfoDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceBossNodeDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.internalmsg.login.ClosePlayerReason;
import com.lc.billion.icefire.game.biz.manager.*;
import com.lc.billion.icefire.game.biz.manager.activity.LuckyBagActivityManager;
import com.lc.billion.icefire.game.biz.manager.activity.WhispererActivityManager;
import com.lc.billion.icefire.game.biz.manager.alliance.AllianceCurrencyManager;
import com.lc.billion.icefire.game.biz.manager.alliance.AllianceMessageBordManager;
import com.lc.billion.icefire.game.biz.manager.gvg.GVGGameDataVoManager;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.model.activity.worldBossDongzhuo.DongzhuoBossActivityContext;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.alliance.affair.AllianceAffairType;
import com.lc.billion.icefire.game.biz.model.buff.BuffSourceType;
import com.lc.billion.icefire.game.biz.model.city.BuildingGroup;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.device.DeviceType;
import com.lc.billion.icefire.game.biz.model.email.AbstractEmail;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.email.SystemEmail;
import com.lc.billion.icefire.game.biz.model.email.SystemEmailType;
import com.lc.billion.icefire.game.biz.model.fightpower.FightPowerType;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.model.hero.Hero;
import com.lc.billion.icefire.game.biz.model.hero.HeroState;
import com.lc.billion.icefire.game.biz.model.item.ItemUtils;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.lottery.LotteryData;
import com.lc.billion.icefire.game.biz.model.lottery.RoleLottery;
import com.lc.billion.icefire.game.biz.model.migrate.MigrateServer;
import com.lc.billion.icefire.game.biz.model.milestone.Milestone;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.mission.chapterMission.ChapterMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.chapterMission.PlayerChapterMission;
import com.lc.billion.icefire.game.biz.model.mission.dailyMission.DailyMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.dailyMission.PlayerDailyMission;
import com.lc.billion.icefire.game.biz.model.mission.feederMission.FeederMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.feederMission.PlayerFeederMission;
import com.lc.billion.icefire.game.biz.model.mission.mainMission.PlayerMainMission;
import com.lc.billion.icefire.game.biz.model.people.PeopleBornConditionType;
import com.lc.billion.icefire.game.biz.model.player.LoginExtParam;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.popularwill.PopularWillEventType;
import com.lc.billion.icefire.game.biz.model.record.RoleRecord;
import com.lc.billion.icefire.game.biz.model.role.*;
import com.lc.billion.icefire.game.biz.model.scene.MapData;
import com.lc.billion.icefire.game.biz.model.scene.MapGrid;
import com.lc.billion.icefire.game.biz.model.scene.MapGridLoopIterator;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.model.share.ShareCallbackType;
import com.lc.billion.icefire.game.biz.model.shieldRecord.ShieldEventType;
import com.lc.billion.icefire.game.biz.model.sience.ScienceInfo;
import com.lc.billion.icefire.game.biz.model.work.WorkQueueType;
import com.lc.billion.icefire.game.biz.service.AbstractOnlineState;
import com.lc.billion.icefire.game.biz.service.impl.DaoService;
import com.lc.billion.icefire.game.biz.service.impl.ServerInfoServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.ZeroScheduleServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.account.GroovyEngineServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.achieve.AchievementServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.activity.handler.fission.FissionActivityHandler;
import com.lc.billion.icefire.game.biz.service.impl.activity.handler.whisperer.WhispererActivityService;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceBossService;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceChatExport;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceHelpServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.allianceTech.AllianceTechServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.alliancegift.AllianceGiftServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.alliancemark.AllianceMarkServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.allianceBattle.AllianceBattleService;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.army.processor.ArmyProcessor;
import com.lc.billion.icefire.game.biz.service.impl.army.processor.ArmyProcessorFactory;
import com.lc.billion.icefire.game.biz.service.impl.bingo.BingoActivityServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.buff.effect.PropertyBuffEffect;
import com.lc.billion.icefire.game.biz.service.impl.cdkey.CDKeyServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.chatsdk.GameType;
import com.lc.billion.icefire.game.biz.service.impl.email.MailCreator;
import com.lc.billion.icefire.game.biz.service.impl.email.MailSdkService;
import com.lc.billion.icefire.game.biz.service.impl.excellentMark.ExcellentMarkService;
import com.lc.billion.icefire.game.biz.service.impl.expedition.ExpeditionNumberGateService;
import com.lc.billion.icefire.game.biz.service.impl.guide.StoryServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.gvg.GVGGameService;
import com.lc.billion.icefire.game.biz.service.impl.gvg.GVGLocalTestService;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroOutput;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.kvk.KVKGameService;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.login.OnlineStateFactory;
import com.lc.billion.icefire.game.biz.service.impl.migrate.MigrateService;
import com.lc.billion.icefire.game.biz.service.impl.milestone.event.AbstractMilestoneEvent;
import com.lc.billion.icefire.game.biz.service.impl.mission.MissionChapterServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.mission.MissionConstants;
import com.lc.billion.icefire.game.biz.service.impl.mission.MissionServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.officials.OfficialsService;
import com.lc.billion.icefire.game.biz.service.impl.player.PlayerServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.prop.extention.PropExtentionService;
import com.lc.billion.icefire.game.biz.service.impl.push.IosPushServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.push.PushHelper;
import com.lc.billion.icefire.game.biz.service.impl.push.WechatPushServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.recharge.RechargeServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.regioncapital.RegionCapitalService;
import com.lc.billion.icefire.game.biz.service.impl.role.RoleCityServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.share.ShareServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.siegeengines.SiegeEnginesServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.soldier.SoldierServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.soldier.SoldierUpdateReasonType;
import com.lc.billion.icefire.game.biz.service.impl.vip.VipServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.work.WorkOutput;
import com.lc.billion.icefire.game.biz.service.impl.work.WorkServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.biz.service.rpc.GameRPCToGVGControlProxyService;
import com.lc.billion.icefire.game.biz.service.rpc.GameRPCToWebProxyService;
import com.lc.billion.icefire.game.biz.util.DateUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.msg.GameMessageConfigManager;
import com.lc.billion.icefire.game.msg.MessageRecorder;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.game.net.NetHelper;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.game.support.LogReasons.ItemLogReason;
import com.lc.billion.icefire.game.support.LogReasons.MoneyLogReason;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.GVGBattleFieldTimeLineDao;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.StrongHoldNodeDao;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.GVGBattleFieldTimeLine;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGBattleService;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.TVTBattleService;
import com.lc.billion.icefire.kvkcontrol.biz.service.KVKControlService;
import com.lc.billion.icefire.kvkseason.biz.service.impl.honor.KvkHonorService;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kvk.KvkSeasonServiceImpl;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.SeasonTaskService;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.*;
import com.lc.billion.icefire.protocol.structure.PsPushSetting;
import com.lc.billion.icefire.protocol.structure.PsShareArg;
import com.lc.billion.icefire.rpc.service.gvg.IGameRemoteGVGControlService;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.rpc.ICommonRemoteWebService;
import com.simfun.sgf.net.msg.MessageHandler;
import com.simfun.sgf.net.msg.MessageHandlerFactory;
import com.simfun.sgf.utils.JavaUtils;
import com.simfun.sgf.utils.TimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetSocketAddress;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @sine 2016年1月4日 下午12:29:10
 */
@Controller
public class CgChatRoomSendMessageHandler extends CgAbstractMessageHandler<CgChatRoomSendMessage> {
    public static final Logger logger = LoggerFactory.getLogger(CgChatRoomSendMessageHandler.class);

    @Autowired
    private ServiceDependency srvDpd;
    @Autowired
    private RechargeServiceImpl rechargeServiceImpl;
    @Autowired
    private ActivityRecordDao activityRecordDao;
    @Autowired
    private RoleLotteryManager lotteryManager;
    @Autowired
    private RoleItemManager itemMgr;
    @Autowired
    private RoleCurrencyManager roleCurrencyManager;
    @Autowired
    private PlayerHeroDao heroDao;
    @Autowired
    private ResourceOutputManager resourceOutputManager;
    @Autowired
    private SoldierServiceImpl soldierService;
    @Autowired
    private SoldierManager soldierManager;
    @Autowired
    private RoleTechDao roleTechDao;
    @Autowired
    private RoleCityManager roleCityManager;
    @Autowired
    private RoleCityServiceImpl roleCityService;
    @Autowired
    private AllianceTechServiceImpl allianceTechService;
    @Autowired
    private AllianceGiftServiceImpl allianceGiftService;
    @Autowired
    private CityBuildDao cityBuildDao;
    @Autowired
    private MissionManager missionManager;
    @Autowired
    private MissionServiceImpl missionService;
    @Autowired
    private MissionChapterServiceImpl missionChapterService;
    @Autowired
    private PlayerChapterMissionDao chapterMissionDao;
    @Autowired
    private AllianceHelpServiceImpl allianceHelpServicel;
    @Autowired
    private AllianceDao allianceDao;
    @Autowired
    private RoleHeroManager roleHeroManager;
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private ItemServiceImpl itemService;
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private WorldServiceImpl worldService;
    @Autowired
    private AllianceManager allianceManager;
    @Autowired
    private AllianceMemberDao allianceMemberDao;
    @Autowired
    private RankDaoImpl rankDao;
    @Autowired
    private AllianceBattleInfoDao allianceBattleInfoDao;
    @Autowired
    private WechatPushServiceImpl wechatPushService;
    @Autowired
    private IosPushServiceImpl iosPushService;
    @Autowired
    private PushHelper pushHelper;
    @Autowired
    private AllianceMessageBordManager allianceMessageBordManager;
    @Autowired
    private AllianceMarkServiceImpl markService;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private MilestoneManager milestoneManager;
    @Autowired
    private SceneServiceImpl sceneService;
    @Autowired
    private RoleCityDao roleCityDao;
    @Autowired
    private ArmyProcessorFactory armyProcessorFactory;
    @Autowired
    private MigrateService migrateService;
    @Autowired
    private VipServiceImpl vipService;
    @Autowired
    private AllianceCurrencyManager allianceCurrencyManager;
    @Autowired
    private GVGGameService gvgGameService;
    @Autowired
    private MailCreator mailCreator;
    @Autowired
    private ExcellentMarkService excellentMarkService;
    @Autowired
    private AllianceServiceImpl allianceService;
    @Autowired
    private PropExtentionService propExtentionService;
    @Autowired
    private OfficialsService officialsService;
    @Autowired
    private KVKGameService kvkGameService;
    @Autowired
    private WhispererActivityManager whispererActivityManager;
    @Autowired
    private WorldExploreEventManager eventManager;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private RoleExtraManager roleExtraManager;
    @Autowired
    private BingoActivityServiceImpl bingoActivityService;
    @Autowired
    private FightTest fightTest;
    @Autowired
    private OnlineStateFactory stateFactory;
    @Autowired
    private NewStrongestLordsActivityManager newStrongestLordsActivityManager;
    @Autowired
    private RegionCapitalService regionCapitalService;
    @Autowired
    private RoleDeviceManager roleDeviceManager;
    @Autowired
    private StoryServiceImpl storyService;
    @Autowired
    private AllianceBossService allianceBossService;
    @Autowired
    private AllianceBossNodeDao bossNodeDao;
    @Autowired
    private ZeroScheduleServiceImpl zeroScheduleService;
    @Autowired
    private AllianceBattleService allianceBattleService;
    @Autowired
    private SeasonWarmUpActivityService seasonWarmUpActivityService;
    @Autowired
    private MessageHandlerFactory messageHandlerFactory;
    @Autowired
    private GameMessageConfigManager messageConfigManager;
    @Autowired
    private DaoService daoService;
    @Autowired
    private RoleServerInfoManager roleServerInfoManager;
    @Autowired
    private GVGLocalTestService gvgLocalTestService;
    @Autowired
    private GVGStrongHoldService GVGStrongHoldService;
    @Autowired
    private GVGGameDataVoManager gvgGameDataVoManager;
    @Autowired
    private StrongHoldNodeDao strongHoldNodeDao;
    @Autowired
    private PlayerServiceImpl playerService;
    @Autowired
    private GVGBattleFieldTimeLineDao gvgBattleFieldTimeLineDao;
    @Autowired
    private GroovyEngineServiceImpl groovyEngineService;
    @Autowired
    private SiegeEnginesServiceImpl siegeEnginesServiceImpl;
    @Autowired
    private GameRPCToGVGControlProxyService gameRPCToGVGControlProxyService;
    @Autowired
    AllianceChatExport chatExport;

    @Override
    public void handle(Role role, final CgChatRoomSendMessage message) {
        Player player = worldService.getWorld().getPlayer(role);
        String body = message.getMessage();
        InetSocketAddress remoteAddress = player.getNetSession().getRemoteAddress();
        // long currentTimeMillis = TimeUtil.getNow();
        // if (body.startsWith("#")) {
        if (body.startsWith("#") && ServerConfigManager.isDev()) {
            logger.info("注意：permission pass,begin GM operation!!!!");
            String[] split = body.split(" ");
            processCommand(role,split);
        } // #addSoldier 30007 10000
        // 屏蔽字
        // body = checkStringService.clean(body);

        // SendMessageOperation oper = new SendMessageOperation(chatService, role, message.getRoomId(), body, message.getToken());

        // asyncService.execute(oper);

        else {
            logger.info("GM命令使用异常, roleId={}, ip={}, content={}", role.getId(), remoteAddress.getHostString(), body);
        }
    }


    private void processCommand(Role role, String[] split){
        var player = role.getPlayer();
        String cmd = split[0];
        // SceneServiceImpl sceneService = srvDpd.getSceneService();
        switch (cmd) {
            case "#CgMsg":  // doc: required String className; optional String msgBody;
                try {
                    String className = split[1];
                    if (className == null) {
                        break;
                    }
                    Class<?> clazz = Class.forName("com.lc.billion.icefire.protocol." + className);
                    int msgType = messageConfigManager.getMessageType(clazz);
                    if (msgType == -1) {
                        break;
                    }
                    MessageHandler handler = messageHandlerFactory.getMessageHandler(msgType);
                    if (handler == null) {
                        break;
                    }
                    String msgBody;
                    if (split.length == 3) {
                        msgBody = split[2];
                    } else {
                        msgBody = "{}";
                    }
                    TBase msg = (TBase) NetHelper.thriftFromJson(clazz, msgBody);
                    handler.handle(msg, role.getPlayer());
                } catch (ExpectedException ignored) {

                } catch (Exception e) {
                    ErrorLogUtil.exceptionLog("CgMsg error",e,"body",String.join(" ",split));
                }

                break;
            case "#copy":  // doc: required long fromRoleId; optional long toRoleId;
                // 复制账号，参数只有两个，需要复制过来的账号获取复制过去的账号
                // 1. fromRoleId, 2. toRoleId
                if (split.length < 2) {
                    break;
                }
                Long sourceRoleId = Long.parseLong(split[1]);
                Long targetRoleId = role.getRoleId();
                if (split.length == 3) {
                    targetRoleId = Long.parseLong(split[2]);
                }
                RoleServerInfo roleServerInfo = roleServerInfoManager.findRoleServerInfo(role.getRoleId());
                daoService.copyRole(roleServerInfo.getDB(), sourceRoleId, targetRoleId);
                break;
            case "#countResource":  // doc: required int currencyId
                Currency currency = Currency.findById(Integer.parseInt(split[1]));
                resourceOutputManager.countResourceOutput(player.getId(), currency);
                break;
            case "#shutdown":  // doc:
                Application.close();
                System.exit(0);
                break;
            case "#money":  // doc: required int currencyId; required long count;
                currency = Currency.findById(Integer.parseInt(split[1]));
                long count = Long.parseLong(split[2]);
                roleCurrencyManager.add(role, currency, count, MoneyLogReason.GM_CMD_GIVE_MONEY, "chat gm cmd");
                break;
            case "#moneySet":  // doc: required int currencyId; required long count;
                currency = Currency.findById(Integer.parseInt(split[1]));
                long setCount = Long.parseLong(split[2]);
                roleCurrencyManager.gmSetMoney(role, currency, setCount);
                break;
            case "#item":  // doc: required int itemId; required int count;
                itemMgr.addItem(role, split[1], Integer.parseInt(split[2]), ItemLogReason.GM_EMAIL_GET);
                break;
            case "#message":  // doc: required int playCount; required long expireTime; required int sort; required String context;
                int playCount = Integer.parseInt(split[1]);
                long expireTime = Long.parseLong(split[2]) * Time.SEC;
                int sort = Integer.parseInt(split[3]);
                String context = split[4];
                this.srvDpd.getNoticeService().systemMarqueeGM(context, playCount, expireTime + TimeUtil.getNow(), sort, "first_name", "second_name");
                // this.srvDpd.getNoticeService().systemMarquee(split[1], 10 * TimeUtils.SECONDS_MILLIS, TimeUtil.getNow() + Time.MIN);
                break;
            case "#buy": {  // doc: required String productId; optional String data;
                String data = split.length >= 3 ? split[2] : "";
                this.rechargeServiceImpl.gmcharge(role, split[1], data,1);
                break;
            }
            case "#buymulti": {  // doc: required String productId; required int times; optional String data;
                var times = Integer.parseInt(split[2]);
                String data = split.length >= 4 ? split[3] : "";
                this.rechargeServiceImpl.gmcharge(role, split[1], data,times);
                break;
            }
            case "#triggerLibao":  // doc: required String productId; required String data;
                String productId = split[1];
                long durationTime = Long.parseLong(split[2]);
                srvDpd.getLiBaoService().GMTriggerLibao(role, productId, durationTime);
                break;
            case "#test":  // doc:
                MapData mapData = this.srvDpd.getWorldService().getWorld().getMapData(Application.getServerId());
                Iterator<MapGrid> cacheLoopIterator = new MapGridLoopIterator(mapData, 750, 750, 1);
                while (cacheLoopIterator.hasNext()) {
                    MapGrid mapGrid = cacheLoopIterator.next();
                    logger.debug(mapGrid.toString());
                }
                break;
            case "#email":  // doc: required String mailId
                String mailId = split[1];
                SystemEmail systemMail = this.srvDpd.getMailCreator().createSystemMail(role.getPersistKey(), role.getCurrentServerId(), mailId, ItemLogReason.GM_EMAIL_GET);
                this.srvDpd.getMailSender().sendOneMail(systemMail);
                // 发送系统邮件奖励
                break;
            case "#dropTest":  // doc: required String dropId; required int times;
                // 掉落测试
                logger.info("掉落ID：{}", split[1]);
                for (int i = 0; i < Integer.parseInt(split[2]); i++) {
                    List<SimpleItem> itemList = this.srvDpd.getDropService().drop(split[1]);
                    logger.info("次数i:{}, list:{}", i, itemList);
                }
                break;
            case "#citymove":  // doc: required int x; required int y;
                if (split.length >= 2) {
                    this.srvDpd.getRoleCityService().directMoveCity(role, Point.getInstance(Integer.parseInt(split[1]), Integer.parseInt(split[2])), false, false, true);
                }
                break;
            case "#randomcitymove":
                this.srvDpd.getRoleCityService().randomMoveCity(role, true, false, false);
                break;
            case "#createalliance":
                String str = String.valueOf(TimeUtil.getNow());
                this.srvDpd.getAllianceService().createAlliance(role, str, str, str, "1", 1, "2", 2, true, str, str, 0, false);
                break;
            case "#outalliance":
                this.srvDpd.getAllianceService().dismissAlliance(role);
                break;
            case "#protection":
                long protectRemainTime = 602 * TimeUtil.SECONDS_MILLIS;
                srvDpd.getProtectionService().protectCity(role, protectRemainTime, false, ShieldEventType.DEBUGE_TEST);
                srvDpd.getBuffService().sendBuffList(role);
                break;
            case "#stopProtection":
                srvDpd.getProtectionService().stopProtection(role, false, ShieldEventType.DEBUGE_TEST, null);
                srvDpd.getBuffService().sendBuffList(role);
                break;
            case "#warCrazy":
                srvDpd.getWarCrazyService().warCrazy(role, "GM add warCrazy buff");
                srvDpd.getBuffService().sendBuffList(role);
                break;
            case "#stopWarCrazy":
                srvDpd.getWarCrazyService().stopWarCrazy(role, "GM stop warCrazy buff");
                srvDpd.getBuffService().sendBuffList(role);
                break;
            case "#addBuff":  // doc: required String buffMetaId;
                if (split.length < 2) {
                    return;
                }
                var buffMetaId = split[1];
                BuffConfig.BuffMeta buffMeta = this.srvDpd.getConfigService().getConfig(BuffConfig.class).getBuffMeta(buffMetaId);
                if (buffMeta == null) {
                    return;
                }
                srvDpd.getBuffService().addBuff(role, new PropertyBuffEffect(), BuffSourceType.DROP, buffMeta.getEffectParaTime(), buffMeta.getId(), buffMeta.getGroup());
                srvDpd.getBuffService().sendBuffList(role);
                break;
            case "#getBuffList":
                srvDpd.getBuffService().sendBuffList(role);
                break;
            case "#missionnewplayersetday":  // doc: required int day;
                int day = Integer.parseInt(split[1]);
                long newCreateTime = TimeUtil.getNow() - Time.DAY * (day - 1) - Time.HOUR;
                role.setCreateTime(newCreateTime);
                break;
            case "#lottery":  // doc: required String mateId; required boolean tenContinuous;
                String mateId = split[1];
                boolean tenContinuous = Boolean.parseBoolean(split[2]);
                this.srvDpd.getLotteryService().lotteryBox(role, mateId, tenContinuous);
                break;
            case "#stopArena":
                srvDpd.getArenaService().gmStop();
                break;
            case "#startArena":
                srvDpd.getArenaService().gmStart();
                break;
            case "#rewardArena":  // doc: required int rewardType; optional String period;
                String period = "";
                if (split.length > 2) {
                    period = split[2];
                }
                srvDpd.getArenaService().gmSendRankReward(Integer.parseInt(split[1]), period);
                break;
            case "#addArenaScore":  // doc: required long score;
                srvDpd.getArenaService().gmAddScore(role, Long.parseLong(split[1]));
                break;
            case "#switchArena":
                srvDpd.getArenaService().gmSwitchSeason();
                break;
            case "#lotteryrset":
                RoleLottery roleLottery = lotteryManager.getRoleLottery(player.getId());
                for (LotteryData lotteryData : roleLottery.getId2LotteryData().values()) {
                    lotteryData.setFreeTimes(0);
                    lotteryData.setNextFreeTime(0L);
                }
                lotteryManager.updateRoleLottery(roleLottery);
                break;
            case "#addBpExp":  // doc: required long exp;
                long exp = Long.parseLong(split[1]);
                srvDpd.getBattlePassService().addScore(role, null, exp);
                break;
            case "#callWhispererBoss":  // doc: required String metaId;
                srvDpd.getWhispererActivityService().selectWhispererAllianceBoss(role, split[1], Time.HOUR);
                break;
            case "#addDailyExp":  // doc: required int exp;
                srvDpd.getMissionDailyService().addDailyScore(role, Integer.parseInt(split[1]));
                break;
            case "#addEquipMaterial":
                srvDpd.getItemService().debugAddEquipMaterial(role);
                break;
            case "#addSoldier":  // doc: required String soldierMetaId; required int count;
                srvDpd.getSoldierService().immediateTrainFinish(role, split[1], Integer.parseInt(split[2]), SoldierUpdateReasonType.GM_GIVE, null, false);
                ErrorLogUtil.errorLog("开始执行debug--------1:", "roleId",role.getId());
                srvDpd.getFightPowerServiceImpl().change(role, FightPowerType.SOLDIER);
                ErrorLogUtil.errorLog("开始执行debug--------2:", "roleId",role.getId());
                break;
            case "#addEquip":  // doc: required String heroMetaId; required int index; required int level;
                if (split.length == 4) {
                    srvDpd.getHeroService().addHeroEquip(role, split[1], NumberUtils.toInt(split[2]), NumberUtils.toInt(split[3]),ItemLogReason.GM_CMD_GIVE_ITEM);
                }
                break;
            case "#addEquipAll":  // doc: optional int equipCount; optional int equipLv;
                int equipLv = 1;
                boolean isCustomLevel = false;
                int equipCount = 1;
                if (split.length >= 2) {
                    equipCount = NumberUtils.toInt(split[1]);
                    if (split.length == 3) {
                        equipLv = NumberUtils.toInt(split[2]);
                        isCustomLevel = true;
                    }
                }
                srvDpd.getHeroService().addHeroEquipAll(role, isCustomLevel, equipCount, equipLv);
                break;
            case "#heroGet":  // doc: required String heroMetaId;
                srvDpd.getHeroService().heroGet(role, split[1], ItemLogReason.DEBUG_ADD_ITEM);
                break;
            case "#heroGetAll":  // doc: required int level; required int star;
                srvDpd.getHeroService().heroGetAll(role, Integer.parseInt(split[1]), Integer.parseInt(split[2]));
                break;
            case "#heroCompound":  // doc: required String heroMetaId;
                srvDpd.getHeroService().heroCompound(role, split[1]);
                break;
            case "#heroAddExp":  // doc: required String heroMetaId; required int number;
                srvDpd.getHeroService().heroAddExp(role, split[1], Integer.parseInt(split[2]));
                break;
            case "#reCalWaterSpeend":
                resourceOutputManager.updateResourceOutputSpeed(player.getRoleId(), Currency.WATER);
                break;
            case "#equipClear":
                srvDpd.getHeroService().GmEquipClear(role);
                break;
            case "#reCalFoodSpeend":
                resourceOutputManager.updateResourceOutputSpeed(player.getRoleId(), Currency.FOOD);
                break;
            case "#heroPro":
                break;
            case "#addWounded":  // doc: required String soldierMetaId; required int count;
                Map<String, Integer> addWounded = new HashMap<>();
                addWounded.put(split[1], Integer.valueOf(split[2]));
                soldierService.addWounded(role, addWounded, SoldierUpdateReasonType.GM_ADD, Strings.EMPTY, true);
                break;
            case "#moneyall":
                int[] res = new int[]{1, 6, 7, 8, 9, 10, 11, 12, 13, 18};
                for (int v : res) {
                    var c = Currency.findById(v);
                    if (c == null) {
                        continue;
                    }
                    roleCurrencyManager.add(role, c, 1000000, MoneyLogReason.GM_CMD_GIVE_MONEY, "GM指令");
                }
                break;
            case "#heroSkillUse":  // doc: required String heroMetaId; required String skillMetaId;
                srvDpd.getHeroService().heroSkillUse(role, split[1], split[2]);
                break;
            case "#techUpgrade":  // doc: required String techMetaId; required boolean immediate;
                String techQueueId = null;
                if (split.length > 1) {
                    techQueueId = split[1];
                }
                srvDpd.getTechService().upgradeSience(role, split[1], split[2].toLowerCase().equals("true"), false, techQueueId);
                break;
            case "#techAll":
                srvDpd.getTechService().GmTechAll(role);
                break;
            case "#clearTech":
                Map<Integer, ScienceInfo> map = roleTechDao.getScienceInfoMap(role.getPersistKey());
                for (ScienceInfo si : map.values()) {
                    roleTechDao.delete(si);
                }
                break;
            case "#heroSkillResetTime":  // doc: required String heroMetaId;
                Hero hero1 = heroDao.getPlayerHero(player.getId(), split[1]);
                if (hero1 == null)
                    return;
                hero1.getLastSkillFlushTime().forEach((k, v) -> {
                    if (v != 0L) {
                        long resetTime = v - TimeUtil.DAY_MILLIS;
                        hero1.getLastSkillFlushTime().put(k, resetTime);
                    }
                });
                hero1.getSkillCounter().forEach((k, v) -> {
                    if (v != 0) {
                        hero1.getSkillCounter().put(k, 0);
                    }
                });
                role.send(HeroOutput.wrapperHeroMod(hero1));

                break;
            case "#buildingSetLevel":// 设置建筑等级  // doc: required int groupId; required int level;
                CityBuild cityBuild = roleCityManager.getBuildingMaxByGroupId(player.getRoleId(), Integer.parseInt(split[1]));
                int oldLevel = cityBuild.getLevel();
                BuildingConfig buildingConfig = configService.getConfig(BuildingConfig.class);
                int buildingLevel = Integer.parseInt(split[2]);
                BuildingConfig.BuildingMeta buildingMeta = buildingConfig.get(cityBuild.getGroupId(), buildingLevel);
                if (buildingMeta == null) {
                    return;
                }
                cityBuild.setLevel(buildingLevel);
                if (oldLevel < buildingLevel) {
                    roleCityManager.initBuilding(role, cityBuild);
                }
                cityBuildDao.save(cityBuild);
                if (Integer.parseInt(split[1]) == BuildingGroup.CASTLE.getGroup()) {
                    // SimpleRole simpleRole = srvDpd.getWorldService().getWorld().getSimpleRole(player.getId());
                    // simpleRole.setLevel(Integer.parseInt(split[2]));
                    role.setLevel(buildingLevel);
                    roleDao.save(role);
                }
                roleCityService.onUpgradeBuilding(role, cityBuild);
                GcCityUpdated psCityUpdated = roleCityManager.toSomeBuildUpdate(player.getId(), cityBuild);
                role.send(new GcCityUpdated(psCityUpdated));

                // 向ts同步信息
                // roleCityService.toTsBuildInfo(role.getRoleId(), cityBuild);
                break;
            case "#landUpgrade":  // doc: required int groupId; required String queueId;
                int buildGroupId = Integer.valueOf(split[1]);
                String queueId = split[2];
                roleCityService.upgradeBuildingForGm(role, buildGroupId, queueId);
                break;
            case "#finishAllChapterMission":// 完成所有章节任务 需要重新登录
                PlayerChapterMission playerChapterMission = missionManager.getPlayerChapterMission(player.getRoleId());
                for (ChapterMissionItem missionItem : playerChapterMission.getCurrMissions().values()) {
                    if (missionItem.getStatus() != 1) {
                        missionItem.setStatus(MissionConstants.MISSION_STATUS_FINISH);
                        role.send(MissionServiceImpl.toUpdate2(missionItem));
                    }
                }
                playerChapterMission.setChapterState(ChapterState.finish);
                // chapterService.getChapterRewards(player, playerChapterMission.getCurChapterId());
                chapterMissionDao.save(playerChapterMission);
//					srvDpd.getMissionChapterService().onEnterWorld(role);
                break;
            case "#finishToChapter": {  // doc: required int chapter;
                // 完成至第n章节
                var chapter = Integer.parseInt(split[1]);

                var curChapter = missionChapterService.getCurrentChapter(role);
                while (curChapter < chapter) {
                    var missions = missionManager.getPlayerChapterMission(player.getRoleId());
                    for (ChapterMissionItem missionItem : missions.getCurrMissions().values()) {
                        if (missionItem.getStatus() != 1) {
                            missionItem.setStatus(MissionConstants.MISSION_STATUS_FINISH);
                            role.send(MissionServiceImpl.toUpdate2(missionItem));
                        }
                    }
                    missions.setChapterState(ChapterState.finish);
                    // chapterService.getChapterRewards(player, playerChapterMission.getCurChapterId());
                    chapterMissionDao.save(missions);

                    //领奖
                    missionChapterService.getChapterRewards(role, null);

                    var after = missionChapterService.getCurrentChapter(role);
                    if (after <= curChapter) {
                        break;
                    }
                    curChapter = after;
                }
                // chapterService.getChapterRewards(player, playerChapterMission.getCurChapterId());
//					srvDpd.getMissionChapterService().onEnterWorld(role);
                break;
            }
            case "#setMainTask":// 设置成某个主线任务 需要重新登录  // doc: required String missionId;
                PlayerMainMission mainMissions = missionManager.getPlayerMainMission(role.getId());
                MissionConfig.MissionMeta missionMeta1 = srvDpd.getConfigService().getConfig(MissionConfig.class).getById(split[1]);
                mainMissions.getMission().setAward(false);
                mainMissions.getMission().setMissionId(split[1]);
                mainMissions.getMission().setProgress(0);
                mainMissions.getMission().setStatus(MissionConstants.MISSION_STATUS_UNFINISH);
                mainMissions.getMission().setMissionType(missionMeta1.getMissionType());
                missionService.checkMission(role);
                missionManager.saveMainMission(mainMissions);
                break;
            case "#finishMainTask":// 完成某个主线任务 需要重新登录
                mainMissions = missionManager.getPlayerMainMission(role.getId());
                mainMissions.getMission().setStatus(MissionConstants.MISSION_STATUS_FINISH);
                missionManager.saveMainMission(mainMissions);
                break;
            case "#finishChapterTask":// 完成某个章节任务 需要重新登录  // doc: required String missionId;
                PlayerChapterMission chapterMission = missionManager.getPlayerChapterMission(role.getId());
                ChapterMissionItem chapterMissionItem = chapterMission.getCurrMissions().get(split[1]);
                chapterMissionItem.setStatus(MissionConstants.MISSION_STATUS_FINISH);
                missionManager.saveChapterMission(chapterMission);
                break;
            case "#finishFeederTask":// 完成某个支线任务 需要重新登录  // doc: required String missionId;
                PlayerFeederMission feederMission = missionManager.getPlayerFeederMission(role.getPersistKey());
                FeederMissionItem feederMissionItem = feederMission.getCurrMissions().get(split[1]);
                feederMissionItem.setStatus(MissionConstants.MISSION_STATUS_FINISH);
                missionManager.saveFeederMission(feederMission);
                break;
            case "#finishDailyTask":// 完成某个日常任务 需要重新登录  // doc: required String missionId;
                PlayerDailyMission dailyMission = missionManager.getPlayerDailyMission(role.getId());
                DailyMissionItem dailyMissionItem = dailyMission.getCurrMissions().get(split[1]);
                dailyMissionItem.setStatus(MissionConstants.MISSION_STATUS_FINISH);
                missionManager.saveDailyMission(dailyMission);
                srvDpd.getMissionDailyService().getDailyMissionMsg(role);
                break;
            case "#allianceGold":  // doc: required int num;
                Alliance alliance = allianceDao.findById(role.getAllianceId());
                if (alliance == null) {
                    return;
                }
                int num = Integer.parseInt(split[1]);
                allianceHelpServicel.addAllianceHelpDailyGold(role, num, alliance);
                break;

            case "#delHero":  // doc: required String heroId;
                roleHeroManager.delHero(role, split[1]);
                break;
            case "#delAllHeroes":
                roleHeroManager.delAllHero(role);
                break;
            case "#reCalcSpeed":  // doc: required String currencyName;
                String currencyName = split[1];
                resourceOutputManager.updateResourceOutputSpeed(player.getRoleId(), Currency.valueOf(currencyName));
                break;
            case "#heroRest":  // doc: required String heroId;
                String heroId1 = split[1];
                Hero hero2 = roleHeroManager.getHeroByHeroId(player.getRoleId(), heroId1);
                if (hero2 != null) {
                    hero2.setState(HeroState.IDLE);
                    heroDao.save(hero2);
                }
                break;
            case "#itemGive":  // doc: required String itemId;
                SimpleItem si = new SimpleItem(split[1], 1);
                itemService.give(role, si, ItemLogReason.USE_ITEM);
                break;
            case "#comment":
                srvDpd.getEvaluationService().getEvaluationReward(role, "1");
                break;
            case "#allianceCleanUpLord":  // doc: required int days;
                alliance = allianceDao.findById(role.getAllianceId());
                if (alliance == null) {
                    return;
                }
                alliance.setSendAllianceCleanUpLordEmailTag(0);
                AllianceMember allianceMember = allianceMemberDao.findById(alliance.getLeaderId());
                allianceMember.setOfflineTime(TimeUtil.getNow() - Long.parseLong(split[1]) * TimeUtil.DAY_MILLIS);
                allianceManager.allianceCleanUpLord(alliance);
                break;
            case "#pushWechat":  // doc: required String pushType;
                String token = "oSKbG60nEQO_m9iJpyhvSsjNXE5s"; // wangbb
                //String token = "oSKbG633rGytWG9gCPMhCLewObkE"; // liujingxi
                if (split.length > 1) {
                    token = split[1];
                }
                var pushType = PsPushType.CITY_UNDER_ATTACK;
                ServerConfigManager.getInstance().getGameConfig().setServerPushSwitch(true);
                var pushInfo = Application.getBean(PlayerPushInfoDao.class).findById(role.getRoleId());
                pushInfo.resetLastPushTime(pushType);
                var setting = new PsPushSetting(pushType, true);
                Application.getBean(RoleSettingManager.class).pushSettingMod(role, Collections.singletonList(setting));
                RoleDevice roleDevice = roleDeviceManager.getRoleDevice(role.getId());
                roleDevice.setPushPlatform(PsPushPlatformType.WX_MINI_GAME);
                roleDeviceManager.save(roleDevice);
                pushHelper.registerPushToken(role, token);
                pushHelper.underAttack(role);
                pushHelper.growthConstruction(role, List.of("@WECHAT_PUSH_TITLE_BUILDING@"), List.of("@building_name_330101@", "@SUBSCRIBE_PARAM_2@"));
                pushHelper.growthConstruction(role, List.of("@WECHAT_PUSH_TITLE_TECH@"), List.of("@building_name_330101@", "@SUBSCRIBE_PARAM_3@"));
                pushHelper.growthConstruction(role, List.of("@WECHAT_PUSH_TITLE_SOLDIER@"), List.of("@building_name_330101@", "@SUBSCRIBE_PARAM_4@"));
                pushHelper.loginRewardNotice(role);
                pushHelper.offlineRecall(role);
                ServerConfigManager.getInstance().getGameConfig().setServerPushSwitch(false);
                break;
            case "#pushIos":
                pushType = PsPushType.CITY_UNDER_ATTACK;
                ServerConfigManager.getInstance().getGameConfig().setServerPushSwitch(true);
                pushInfo = Application.getBean(PlayerPushInfoDao.class).findById(role.getRoleId());
                pushInfo.resetLastPushTime(pushType);

                List<PsPushSetting> settings = new ArrayList<>();
                settings.add(new PsPushSetting(PsPushType.CITY_UNDER_ATTACK, true));
                for (var type : PsPushType.values()) {
                    settings.add(new PsPushSetting(type, true));
                }
                Application.getBean(RoleSettingManager.class).pushSettingMod(role, settings);
                // 测试机userId
                token = "10001";
                roleDevice = roleDeviceManager.getRoleDevice(role.getId());
                roleDevice.setPushPlatform(PsPushPlatformType.IOS);
                roleDeviceManager.save(roleDevice);
                pushHelper.registerPushToken(role, token);
                // 按pushHelper中的接口顺序推送消息
                pushHelper.underAttack(role);
                pushHelper.beScouted(role);
                pushHelper.protectiveCoverTimeOver(role);
                // 无配置
                pushHelper.tooManyWounded(role);
                pushHelper.exploreNewEvent(role);
                pushHelper.reinforceArrive(role);
                pushHelper.armyReturn(role);
                pushHelper.troopsBeHealed(role);
                pushHelper.onlineReward(role);
                pushHelper.staminaNotice(role);
                pushHelper.ArenaNotice(role);
                pushHelper.ActivityStartNotice(role, List.of("@ACTIVITY_ZHUJIU_TAB@"), List.of("@ACTIVITY_ZHUJIU_TAB@", String.valueOf(6000)));

                pushHelper.growthConstruction(role, List.of("@WECHAT_PUSH_TITLE_BUILDING@"), List.of("@building_name_330101@", "@SUBSCRIBE_PARAM_2@"));
                pushHelper.growthConstruction(role, List.of("@WECHAT_PUSH_TITLE_SOLDIER@"), List.of("@building_name_330101@", "@SUBSCRIBE_PARAM_4@"));
                pushHelper.growthConstruction(role,List.of("@WECHAT_PUSH_TITLE_TECH@"), List.of("@science_name_11002@", "@SUBSCRIBE_PARAM_3@"));
                pushHelper.growthOfflineReward(role);
                pushHelper.growthBattleReward(role);
                pushHelper.growthAllianceDonate(role);
                pushHelper.loginRewardNotice(role);
                pushHelper.offlineRecall(role);
                pushHelper.firstRechargeRecall(role);
                pushHelper.whispererBossAppear(role, List.of("@ACTIVITY_ZHUJIU_TAB@"), List.of("@ZHUJIU_BOSS_NAME_80101@"));
                ServerConfigManager.getInstance().getGameConfig().setServerPushSwitch(false);
                break;
            case "#allianceMessageBord":
                allianceMessageBordManager.pushAllianceMessageBord(role, role.getAllianceId());
                break;
            case "#allianceMarkSet":
                markService.allianceMarkSet(role, "1", 100, 100, "测试", 0.001);
                break;
            case "#allianceMarkDel":
                markService.allianceMarkDel(role, "1");
                break;
            case "#abTest":  // doc: required String key; required String value;
                roleManager.modifyRoleAbStamp(role, split[1], split[2]);
                break;
            case "#flushRank":
                srvDpd.getRankService().flushRankByGM();
                break;
            case "#addUser":{
                // 压测测试服务器刷新玩家数量~临时测试~把刷出来的点存入aoi中~不存库~重启服务器~玩家自动消失~
                long cnt = 0L;
                long fail = 0L;
                long now = System.currentTimeMillis();
                AtomicLong idGen = new AtomicLong(System.currentTimeMillis());
                for (int i = 0; i < 10000; i++) {
                    if (fail > 1000) {
                        break;
                    }
                    var simPlayer = new Player();
                    simPlayer.setSimUser(true);
                    simPlayer.setId(idGen.incrementAndGet());
                    simPlayer.setUserId(simPlayer.getId());
                    simPlayer.setoServerId(Application.getServerId());
                    simPlayer.setCurrentServerId(Application.getServerId());
                    simPlayer.setOnlineState(stateFactory.getState(AbstractOnlineState.Type.LOGINED));
                    CgLogin login = new CgLogin();
                    login.setUid(simPlayer.getId());
                    login.setRoleId(simPlayer.getRoleId());
                    login.setTimestamp(0L);
                    String expectSign = TokenUtils.getLoginToken(login.getUid(), login.getRoleId(), login.getTimestamp());
                    login.setSign(expectSign);
                    login.setDevice(PsDeviceType.ANDROID);
                    login.setLanguage("Chinese");
                    login.setDeviceId(String.valueOf(i));
                    login.setRollback(false);
                    login.setGaid(String.valueOf(i));
                    login.setAppKey("com.ninestudio.starlight.sg");
                    login.setClientVersion("1.0.0");
                    login.setCountry("CN");
                    login.setPlatform(1);
                    login.setChannelF("");
                    login.setLoginToken("");
                    srvDpd.getLoginService().login(simPlayer, login);

                    logger.info("创建账号成功...{}", i);
                }

                logger.info("finally create success {}, fail {} cost={}", cnt, fail, (System.currentTimeMillis() - now));
                break;
            }
            case "#milestone": {  // doc: required String metaId; required String progress;
                Milestone milestone = milestoneManager.getMilestoneByMetaId(role.getCurrentServerId(), split[1]);
                MilestoneConfig.MilestoneMeta meta = srvDpd.getMilestoneService().getMilestoneMeta(Application.getServerId(), split[1]);
                AbstractMilestoneEvent milestoneEvent = (AbstractMilestoneEvent) milestone.getType().getEvent();
                milestoneEvent.updateProcessBase(milestone, meta, Integer.parseInt(split[2]), role.getAllianceId());
                break;
            }
            case "#resetMilestone":
                int serverId = split.length > 1 ? Integer.parseInt(split[1]) : Application.getServerId();
                srvDpd.getMilestoneService().resetServerMilestone(serverId);
                break;
            case "#setmilestonechapter":  // doc: required String metaId;
                int chapterId = Integer.parseInt(split[1]);
                srvDpd.getMilestoneService().setMilestoneChapter(chapterId);
                srvDpd.getMilestoneService().pushMilestone(role, null);
                break;
            case "#finishmilestonechapter":  // doc: required int chapterId;
                chapterId = Integer.parseInt(split[1]);
                srvDpd.getMilestoneService().fininshMilestoneChapter(chapterId);
                break;
            case "#openResLevelMax":
                serverId = split.length > 1 ? Integer.parseInt(split[1]) : Application.getServerId();
                srvDpd.getMilestoneService().GmOpenResLevelMax(serverId);
                break;
            case "#cacheGlobalRankInfos":
                srvDpd.getRankService().scheduleFlushGlobalRank();
                break;
            case "#attackNpc":
                var cityPos = roleCityDao.findById(role.getRoleId()).getPosition();
                var nodeList = sceneService.getAoiNodesByType(role.getCurrentServerId(), cityPos, SceneNodeType.NPC);
                if (!JavaUtils.bool(nodeList)) {
                    //Point point = Point.getInstance(cityPos.getX() + 1, cityPos.getY() + 1);
                    //NpcNode npcNode = npcNodeDao.create("", 0, role.getCurrentServerId(), point, "10010", role.getRoleId());
                    //sceneService.add(npcNode, true);
                    return;
                }
                var soldiers = soldierManager.getAllSoldier(role);
                var idCount = 2000;
                String idsString = "11001,11002,11003,21004,21005,21006";
                Set<String> ids = new HashSet<>(Arrays.asList(idsString.split(",")));
                for (var id : ids) {
                    var s = soldiers.get(id);
                    if (s == null || s.getCount() < idCount) {
                        soldierService.addSoldiers(role, id, idCount, SoldierUpdateReasonType.GM_GIVE, false);
                    }
                }
                var npc = RandomUtils.random(nodeList);
                CgArmySetout armyMessage = new CgArmySetout();
                armyMessage.setSetoutType(PsArmyType.ATTACK);
                armyMessage.setNodeType(PsMapNodeType.NPC);
                armyMessage.setX(npc.getX());
                armyMessage.setY(npc.getY());
                armyMessage.setBodyType(PsArmyBodyType.NORMAL);
                for (var id : ids) {
                    armyMessage.putToSoldiers(id, idCount);
                }
                ArmySetoutParam param = new ArmySetoutParam(armyMessage);
                param.setSkipCheck(true);
                param.setServerId(role.getCurrentServerId());
                Collection<Hero> heros = roleHeroManager.getHeros(role.getRoleId());
                List<String> heroIds = new ArrayList<>();
                heros.forEach(hero -> {
                    heroIds.add(hero.getMetaId());
                });
                param.setHeros(heroIds);
                ArmyProcessor processer = armyProcessorFactory.getProcessorByArmyType(param.getArmyType());
                processer.excute(role, param);
                break;
            case "#attackTest":  // doc: required String npcMetaId1; required String npcMetaId2; required String args;
                fightTest.doNpcVsNpcTest(split[1], split[2], split[3]);
                break;
            case "#resourceLevel":  // doc: required int x; required int y;
                int pointX = Integer.parseInt(split[1]);
                int pointY = Integer.parseInt(split[2]);
                int resourceLevel = srvDpd.getSceneService().getResourceLevelByXAndY(role.getCurrentServerId(), pointX, pointY);
                GcNoticeInfo noticeInfo = new GcNoticeInfo();
                noticeInfo.setContent("（" + pointX + "," + pointY + ") 属于资源带：" + resourceLevel);
                noticeInfo.setType(PsNoticeType.DEBUG_LOG);
                role.send(noticeInfo);
                break;
            case "#migrateServer":  // doc: required String serverId;
                serverId = Integer.parseInt(split[1]);
                migrateService.cgMigrateServer(role, serverId, false);
                break;
            case "#migrateServerList":
                List<MigrateServer> migrateServerList = migrateService.migrateServerList(role, false);
                logger.info("迁服列表：{}", migrateServerList.toString());
                break;
            case "#cdkeyExchange":  // doc: required String keyId;
                CDKeyServiceImpl cdKeyService = srvDpd.getCdKeyService();
                cdKeyService.exchangeCDKeyRewards(role, split[1]);
                break;
            case "#rewardVipLogin":
                vipService.rewardVipLogin(role);
                break;
            case "#rewardVipLevel":
                vipService.rewardVipLevel(role);
                break;
            case "#itemUse": {  // doc: required String itemMetaId; required int count; optional String param1;
                String item = split[1];
                int itemCount = Integer.parseInt(split[2]);
                String param1 = "";
                if (split.length > 3) {
                    param1 = split[3];
                }
                if (itemCount <= 0 || itemCount > ItemUtils.MAX_USE_COUNT) {// 参数不合规范，直接返回
                    ErrorLogUtil.errorLog("道具使用个数限制,CgItemUse count is error", "itemCount",itemCount,"maxCount", ItemUtils.MAX_USE_COUNT);
                    return;
                }

                itemService.useItemByMetaId(role, item, itemCount, param1);
                break;
            }
            case "#setVipLevel":  // doc: required int level;
                int newLevel = Integer.parseInt(split[1]);
                vipService.setVipLevel(role, newLevel);
                break;
            case "#whispererAddHelpRemainCount":  // doc: required int count;
                Application.getBean(WhispererActivityService.class).gmAddHelpCount(role, Integer.parseInt(split[1]));
                break;
            case "#allianceCurrencyAdd":
                Alliance alliance1 = allianceDao.findById(role.getAllianceId());
                if (alliance1 == null) {
                    return;
                }
                allianceCurrencyManager.add(alliance1, Currency.ALLIANCE_HONOR, 99999999, MoneyLogReason.GM_CMD_GIVE_MONEY, "gm add for test");
                allianceCurrencyManager.add(alliance1, Currency.FOOD, 99999999, MoneyLogReason.GM_CMD_GIVE_MONEY, "gm add for test");
                allianceCurrencyManager.add(alliance1, Currency.GOLD, 99999999, MoneyLogReason.GM_CMD_GIVE_MONEY, "gm add for test");
                allianceCurrencyManager.add(alliance1, Currency.WOOD, 99999999, MoneyLogReason.GM_CMD_GIVE_MONEY, "gm add for test");
                allianceCurrencyManager.add(alliance1, Currency.IRON, 99999999, MoneyLogReason.GM_CMD_GIVE_MONEY, "gm add for test");
                allianceCurrencyManager.add(alliance1, Currency.WATER, 99999999, MoneyLogReason.GM_CMD_GIVE_MONEY, "gm add for test");
                break;
            case "#refreshRecommendCache":
                excellentMarkService.gmRefreshRecommendCache();
                break;
            case "#autoJoinRecommendAlliance":
                srvDpd.getExcellentMarkService().autoJoinBestAlliance(role);
                break;
            case "#getRecommendRole":
                if (JavaUtils.bool(role.getAllianceId())) {
                    Alliance alliance3 = allianceDao.findById(role.getAllianceId());
                    alliance3.setLastRoleRecommendTime(0L);
                    allianceDao.save(alliance3);
                }
                excellentMarkService.allianceGetExcellentRoleList(role);
                break;
            case "#getRecommendAlliance":
                RoleRecordDao roleRecordDao = Application.getBean(RoleRecordDao.class);
                RoleRecord roleRecord = roleRecordDao.findById(role.getId());
                roleRecord.setLastAllianceRecommendTime(0L);
                roleRecordDao.save(roleRecord);

                excellentMarkService.roleGetExcellentAllianceList(role, true);
                break;
            case "#setAllianceExcellent":   // doc: required int mark;
                if (JavaUtils.bool(role.getAllianceId())) {
                    Alliance alliance3 = allianceDao.findById(role.getAllianceId());
                    int mark = Integer.parseInt(split[1]);
                    alliance3.setExcellentMark(mark);
                    allianceDao.save(alliance3);
                }
                break;
            case "#setRoleExcellent":   // doc: required int mark;
                int mark = Integer.parseInt(split[1]);
                role.setExcellentMark(mark);
                roleDao.save(role);
                break;
            case "#allianceChangeAlias": {   // doc: required int handleAllianceId; required string oldName; required string aliasName;
                // Long handleAllianceId = 10300000000000044L;
                // String oldName = "CUNT";
                // String aliasName = "CXXX";
                Long handleAllianceId = Long.parseLong(split[1]);
                String oldName = split[2];
                String aliasName = split[3];

                AllianceDao allianceDao = Application.getBean(AllianceDao.class);
                Alliance handleAlliance = allianceDao.findById(handleAllianceId);
                if (handleAlliance == null) {
                    ErrorLogUtil.errorLog("[CHAT] OneAllianceChangeNameHandle error~ alliance  is null ","allianceId", handleAllianceId);
                    return;
                }
                if (!oldName.equals(handleAlliance.getAliasName())) {
                    ErrorLogUtil.errorLog("[CHAT] OneAllianceChangeNameHandle error ", "allianceId",handleAllianceId, "联盟名字",handleAlliance.getAliasName());
                    return;
                }
                ErrorLogUtil.errorLog("[CHAT] OneAllianceChangeNameHandle alliance is " ,"handleAlliance",handleAlliance);
                handleAlliance.setAliasName(aliasName);
                allianceDao.save(handleAlliance);

                break;
            }
            case "#totalCnt":
                // 通知客户端现在服务器一共有多少玩家
                GcNoticeInfo roleCntNotice = new GcNoticeInfo();
                roleCntNotice.setType(PsNoticeType.DEBUG_LOG);
                roleCntNotice.setTitle("roleCnt");
                roleCntNotice.setContent(String.valueOf(srvDpd.getRoleDao().count()));
                role.send(roleCntNotice);
                break;
            case "#setKing":
                int season = split.length > 1 ? Integer.parseInt(split[1]) : Application.getSeason();
                Application.getBean(KvkSeasonServiceImpl.class).setKing(role, season);
                break;
            case "#clearKvk":
                Application.getBean(KvkSeasonServiceImpl.class).clearKvk();
                break;
            case "#cgGVGAllianceQualified": {
                var matchType = GvgMatchType.findById(Integer.parseInt(split[1]));
                if (matchType == null) {
                    return;
                }
                gvgGameService.cgGVGAllianceQualified(role, matchType);
            }
                break;
            case "#cgGVGSelectTime":
                /*
                 * int selectTime = NumberUtils.toInt(split[1]);
                 * gvgGameService.cgGVGSelectTime(role, selectTime);
                 * gvgGameService.cgGVGSelectMember(role, Arrays.asList(role.getId()), Collections.emptyList(), GvgMatchType.FRIENDLY);
                 */
                break;
            case "#enterGVG": {
                var info = gvgGameDataVoManager.findGVGAllianceSignUpInfoVoByRole(role);
                if (info == null) {
                    ErrorLogUtil.errorLog("[GVG]#enterGVG, info null", "role", role.getRoleId(), "alliance", role.getAllianceId());
                    return;
                }

                GvgMatchType matchType = info.getGvgAllianceLineUpInfo().getGvgMatchType();
                gvgGameService.enterGVG(role, matchType);
            }
                break;
            case "#goBackServer":
                gvgGameService.goBackServer(role.getId());
                break;
            case "#gvgEnd":
                Application.getBean(GVGBattleService.class).battleStop();
                break;
            case "#tvtSettlement":
                Application.getBean(TVTBattleService.class).debugSettlement();
                break;
            case "#excellentDayPush":
                excellentMarkService.dayFirstLoginGmTest(role);
                break;
            case "#unlockTitle":  // doc: required string metaId;
                srvDpd.getTitleService().unlockTitleForGM(role, split[1]);
                break;
            case "#unlockAllFuncs":
                srvDpd.getUnlockService().gmUnlockAll(role);
                break;
            case "#rmTitle":  // doc: required string metaId;
                srvDpd.getTitleService().removeUnlockTitleForGM(role, split[1]);
                break;
            case "#clearTitleCache":
                srvDpd.getTitleService().clearTitleHistoryCache();
                break;
            case "#expedition":  // doc: required int level;
                srvDpd.getExpeditionService().updateLevel(role, Integer.parseInt(split[1]));
                break;
            case "#innerPveLevel":
                Application.getBean(ExpeditionNumberGateService.class).updateInnerLevel(role, Integer.parseInt(split[1]));
                break;
            case "#expeditionNumberGate":
                Application.getBean(ExpeditionNumberGateService.class).updateLevel(role, Integer.parseInt(split[1]));
                break;
            case "#expeditionTime": // 关卡挂机奖励时间修改  // doc: optional int time;
                srvDpd.getExpeditionService().gmSetHangupTime(role, split.length == 2 ? Long.parseLong(split[1]) : null);
                break;
            case "#expeditionMul":  // doc: required int level;
                srvDpd.getExpeditionMulService().updateLevel(role, Integer.parseInt(split[1]));
                break;
            case "#searchNpc":  // doc: required int npcLevel;
                int npcLevel = NumberUtils.toInt(split[1]);
                srvDpd.getNodeEventService().searchNodeHandler(player, PsSearchType.NPC, 1, npcLevel, false);
                break;
            case "#searchRes":  // doc: required int resId; required int resLevel;
                int resId = NumberUtils.toInt(split[1]);
                int resLevel = NumberUtils.toInt(split[2]);
                srvDpd.getNodeEventService().searchNodeHandler(player, PsSearchType.New_Res, resId, resLevel, false);
                srvDpd.getNodeEventService().searchNodeHandler(player, PsSearchType.New_Res, resId, resLevel, true);
                break;
            case "#getPropDetail":  // doc: required int propId;
                int propId = NumberUtils.toInt(split[1]);
                String msg = propExtentionService.getPropDetail(role.getPersistKey(), propId).toString();
                debugMsg(role, "属性查询", msg);
                break;
            case "#setKingAppointBegin":
                officialsService.gmSetKingAppointBegin(role, role.getoServerId());
                break;
            case "#setKingAppointBeginKvK":
                officialsService.gmSetKingAppointBegin(role, Application.getServerId());
                break;
            case "#setRoleOfficial":  // doc: required long targetId; required string officialId; required int opType;
                Long targetId = Long.parseLong(split[1]);
                String officialId = split[2];
                int opType = NumberUtils.toInt(split[3]);
                Role targetRole = roleDao.findById(targetId);
                officialsService.gmSetRoleOfficial(role, targetRole, officialId, opType);
                break;
            case "#addKingOfficialReceoure":
                Integer resource = Integer.parseInt(split[1]);
                officialsService.addAndGetSkillResource(resource, role.getoServerId());
                break;
            case "#addAllianceBuildingPoint":
                long allianceBuildingPoint = Long.parseLong(split[1]);
                Alliance alliance2 = allianceDao.findById(role.getAllianceId());
                allianceCurrencyManager.add(alliance2, Currency.ALLIANCE_BUILDING_HONOR, allianceBuildingPoint, MoneyLogReason.GM_CMD_GIVE_MONEY, "");
                break;
            case "#finishdAllianceBuilding":
               srvDpd.getAllianceBuildingService().gmFinishAllianceBuilding(role.getAllianceId());
                break;
            case "#unlockAllAllianceBuildingTech":
                srvDpd.getAllianceBuildingService().unlockAllAllianceBuildingTech(role.getAllianceId());
                break;
            case "#alliancebuildingNpcSet":
                srvDpd.getAllianceBuildingService().alliancebuildingNpcSet(role.getAllianceId());
                break;
            case "#alliancebuildingPosSet":
                var buildMsg = new CgAllianceBuildingSet();
                var buildingId = Integer.parseInt(split[1]);
                buildMsg.setBuildingId(buildingId);
                buildMsg.setX(Integer.parseInt(split[2]));
                buildMsg.setY(Integer.parseInt(split[3]));
                buildMsg.setCurrency(PsCurrency.ALLIANCE_BUILDING_HONOR);
                srvDpd.getAllianceBuildingService().allianceBuildingSet(role, buildMsg);
                break;
            case "#sendAllianceMsg":
                long allianceId = Long.parseLong(split[1]);
                String content = split[2];
                var msgType = GameType.ALLIANCE_FEAST_START;
                if (!split[3].isEmpty()) {
                    msgType = GameType.ALLIANCE_NEW_FEAST_APPEAR;
                }
                var alliTmp = allianceDao.findById(allianceId);
                chatExport.onMsgNotice(msgType, alliTmp.getLeaderId(), allianceId, content);
                break;
            case "#setFeastHallInfo":  // 设置宴会厅信息
                var status = PsAllianceFeastStatus.findByValue(Integer.parseInt(split[1]));
                var stageEndTime = Long.parseLong(split[2]);
                var roundEndTime = Long.parseLong(split[3]);
                var subTime = Long.parseLong(split[4]);
                srvDpd.getAllianceFeastService().gmSetFeastHallInfo(role, status, stageEndTime, roundEndTime, subTime);
                break;
            case "#openFeastHallINow":  // 设置宴会厅信息
                var status1 = PsAllianceFeastStatus.READY;
                var stageEndTime1 = System.currentTimeMillis() + 5 * TimeUtils.SECONDS_MILLIS;
                var roundEndTime1 = TimeUtils.getNextWeekStartTime(stageEndTime1);
                var subTime1 = 0L;
                srvDpd.getAllianceFeastService().gmSetFeastHallInfo(role, status1, stageEndTime1, roundEndTime1, subTime1);
                break;
            case "#removeAllianceFeastInfo":
                srvDpd.getAllianceFeastService().gmRemoveFeastInfo(role);
                break;
            case "#addBuyFeastNode":  // 设置宴会厅信息
                var fGroupId = Integer.parseInt(split[1]);
                var fNum = Integer.parseInt(split[2]);
                srvDpd.getAllianceFeastService().addCanPlaceFeastFromBuy(role, fGroupId, fNum);
                break;
            case "#test111":  // doc: required int time;
                int time11 = NumberUtils.toInt(split[1]);
                WorkServiceImpl.immediateCostDiamond(time11);
                break;
            case "#KVKGoout":
                kvkGameService.enterKVK(role, 1, null);
                break;
            case "#KVKGoback":
                kvkGameService.goBackKVKActive(role);
                break;
            case "#rankhelp":
                kvkGameService.goBackKVKActive(role);
                break;
            case "#finishCurrentDifficulty":
                whispererActivityManager.finishCurrentDifficulty(role);
                break;
            case "#worldExploreAddCommonEvent":  // doc: required string eventGroupId;
                eventManager.gmAddCommonEvent(role, split[1]);
                break;
            case "#worldExploreAddExploreEvent":  // doc: required string eventGroupId;
                eventManager.gmAddExploreEvent(role, split[1]);
                break;
            case "#worldExploreAddSpecialEvent":  // doc: required string eventGroupId;
                eventManager.gmAddSpecialEvent(role, split[1]);
                break;
            case "#worldExploreClear":
                eventManager.gmClearEvents(role);
                break;
            case "#worldExploreComplete":
                eventManager.gmCompleteAllEvents(role);
                break;
            case "#worldExploreRefresh":
                eventManager.gmRefresh(role);
                break;
            case "#worldExploreAddExp":  // doc: required int eventExp;
                var eventExp = 1;
                if (split.length > 1) {
                    eventExp = Integer.parseInt(split[1]);
                }
                eventManager.gmAddExploreEventExp(role, eventExp);
                break;
            case "#worldExploreSetBossLevel":  // doc: required int level;
                eventManager.gmSetExploreEventBossLevel(role, Integer.parseInt(split[1]));
                break;
            case "#worldExploreSetLevel":  // doc: required int level;
                var eventLevel = 1;
                if (split.length < 2) {
                    return;
                }
                eventLevel = Math.max(1, Integer.parseInt(split[1]));
                eventManager.gmSetExploreEventLevel(role, eventLevel);
                break;
            case "#worldExploreUpgradeRewardLevel":
                eventManager.gmUpgradeExploreEventRewardLevel(role);
                break;
            case "#changeCanAttackLevel":  // doc: required int level;
                RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getId());
                if (roleExtra == null) {
                    return;
                }

                int attackLevel = Integer.parseInt(split[1]);
                roleExtra.setNpcCanAttackLevel(attackLevel);
                roleManager.saveRole(role);
                break;
            case "#setAttackNpcLevel":  // doc: required int level;
                int npcMaxLevel = Integer.parseInt(split[1]);
                Application.getBean(RoleExtraManager.class).setCanAttackNpcLevelForGm(role, npcMaxLevel);
                break;
            case "#testSeasonTaskPve":  // doc: required int npcLv;
                int npcLv = Integer.parseInt(split[1]);
                Application.getBean(SeasonTaskService.class).test(role, npcLv);
                break;
            case "#clearClanCD":
                RoleExtra roleExtra1 = roleExtraManager.getRoleExtra(role.getRoleId());
                roleExtra1.setExitAllianceTime(0);
                roleExtraManager.saveRoleExtra(roleExtra1);
                break;
            case "#BingoPosition": // 设置bingo状态  // doc: required string bingoPosition; required string bingoVal;
                String bingoPosition = split[1];
                String bingoVal = split[2];
                bingoActivityService.setCellVal4Gm(role, bingoPosition, bingoVal);
                break;
            case "#trainNumLimit":  // doc: required int soldierType;
                PsSoldierType soldierType = PsSoldierType.valueOf(split[1]);
                int limit = soldierService.trainNumLimit(role, soldierType);
                debugMsg(role, "训练上限", "你要训练的兵种是" + soldierType + "训练上限是" + limit);
                break;
            case "#gmAchimentFinish":  // doc: required string cmd;
                Application.getBean(AchievementServiceImpl.class).gmAchimentFinish(role, split[1]);
                break;
            case "#modifyInviteCode":
                String text = split[1];
                srvDpd.getRoleService().modifyInviteCode(role, text, true);
            case "#itemAll":  // doc: optional int amount;
                ItemConfig itemConfig = configService.getConfig(ItemConfig.class);
                int amount = 10;
                if (split.length >= 2) {
                    amount = Integer.parseInt(split[1]);
                }
                for (var item : itemConfig.getMetaMap().values()) {
                    itemService.give(role, item.getId(), (int) amount, ItemLogReason.GM_CMD_GIVE_ITEM);
                }
                break;
            case "#gather":
                cityPos = roleCityDao.findById(role.getRoleId()).getPosition();
                var resList = sceneService.getAoiNodesByType(role.getCurrentServerId(), cityPos, SceneNodeType.NEW_RES);
                if (!JavaUtils.bool(resList)) {
                    return;
                }
                soldiers = soldierManager.getAllSoldier(role);
                idCount = 2000;
                idsString = "11001,11002,11003,21004,21005,21006";
                ids = new HashSet<String>(Arrays.asList(idsString.split(",")));
                for (var id : ids) {
                    var s = soldiers.get(id);
                    if (s == null || s.getCount() < idCount) {
                        soldierService.addSoldiers(role, id, idCount, SoldierUpdateReasonType.GM_GIVE, false);
                    }
                }
                var resNode = RandomUtils.random(resList);
                armyMessage = new CgArmySetout();
                armyMessage.setSetoutType(PsArmyType.GATHER_NEW_RES);
                armyMessage.setNodeType(PsMapNodeType.NEW_RES);
                armyMessage.setX(resNode.getX());
                armyMessage.setY(resNode.getY());
                armyMessage.setBodyType(PsArmyBodyType.NORMAL);
                for (var id : ids) {
                    armyMessage.putToSoldiers(id, idCount);
                }
                param = new ArmySetoutParam(armyMessage);
                param.setSkipCheck(true);
                param.setServerId(role.getCurrentServerId());
                processer = armyProcessorFactory.getProcessorByArmyType(param.getArmyType());
                processer.excute(role, param);
                break;
            case "#itemsub":  // doc: required string itemId; required int count;
                itemMgr.removeItem(role, split[1], Integer.parseInt(split[2]), ItemLogReason.GM_CMD_COST_ITEM);
                break;
            case "#upgradeAllBuilding": // 先设置建筑建造需要的解锁关卡  // doc: required int level;
                Application.getBean(ExpeditionNumberGateService.class).updateLevel(role, srvDpd.getConfigService().getConfig(BtlMapConfig.class).getMaxLevel());
                roleCityService.gmUpgradeAllBuilding(role, Integer.parseInt(split[1]));
                break;
            case "#allianceUpgradeTech":  // doc: required int allianceTechGroupId; required int allianceTechLevel;
                int allianceTechGroupId = Integer.parseInt(split[1]);
                int allianceTechLevel = Integer.parseInt(split[2]);
                allianceTechService.gmUpgradeTech(role, allianceTechGroupId, allianceTechLevel);
                break;
            case "#allianceFinishAllTech":
                allianceTechService.gmFinishAllTech(role);
                break;
            case "#recon":
                recon(role);
                break;
            case "#rallyBoss":
                rallyBoss(role);
                break;
            case "#allianceGift":  // doc: required string allianceGiftId; required int allianceGiftCount;
                String allianceGiftId = split[1];
                int allianceGiftCount = Integer.parseInt(split[2]);
                allianceGiftService.gmGainGift(role, allianceGiftId, allianceGiftCount);
            case "#autoRally":  // doc: optional int autoRally;
                this.srvDpd.getAllianceWarService().modifyAutoRallyState(role, split.length > 1 ? Integer.parseInt(split[1]) == 1 : true, false, null);
                break;
            case "#seeRob":
                debugMsg(role, "保护上限", JSON.toJSONString(this.srvDpd.getScoutServiceImpl().canPlunderRes(role)));
                break;
            case "#recharge":  // doc: optional int diamond=100;
                var diamond = 100;
                if (split.length >= 2) {
                    diamond = Integer.parseInt(split[1]);
                }
                var conRechargeService = this.srvDpd.getContinuousRechargeService();
                conRechargeService.onRecharge(role, diamond);
                break;
            case "#regOpen":  // doc: required int regionMetaId;
                String regionMetaId = split[1];
                int openTime = split.length > 2 ? Integer.parseInt(split[2]) : 0;
                regionCapitalService.GmOpen(role, role.getCurrentServerId(), role.getAllianceId(), regionMetaId, openTime);
                break;
            case "#regPveCount":  // doc: required int bossNum; required int npcNum;
                int bossNum = Integer.parseInt(split[1]);
                int npcNum = Integer.parseInt(split[2]);
                regionCapitalService.GmPveCount(role.getCurrentServerId(), bossNum, npcNum);
                break;
            case "#regApplyLimit":  // doc: required string capitalMetaId; optional int applyLimit = 0;
                String capitalMetaId = split[1];
                int applyLimit = split.length > 2 ? Integer.parseInt(split[2]) : 0;
                regionCapitalService.GmApplyLimit(role, applyLimit, capitalMetaId);
                break;
            case "#regApply":  // doc: required string metaId; optional int pvpIndex = 0;
                String _metaId = split[1];
                int pvpIndex = split.length > 2 ? Integer.parseInt(split[2]) : 0;
                regionCapitalService.GmApply(role.getCurrentServerId(), _metaId, role.getAllianceId(), pvpIndex);
                break;
            case "#regBelong":  // doc: required string nodeMetaId;
                String nodeMetaId = null;
                if (split.length == 1) {
                    RoleCity roleCity = roleCityManager.getRoleCity(role.getId());
                    nodeMetaId = regionCapitalService.getRegionCapital(roleCity.getCurrentServerId(), roleCity.getRegionId()).getMetaId();
                } else {
                    nodeMetaId = split[1];
                }
                boolean isTemp = split.length > 2;
                regionCapitalService.GMBelong(role.getCurrentServerId(), role.getAllianceId(), nodeMetaId, isTemp);
                break;
            case "#regApplyCount":
                regionCapitalService.GmApplyCount(role.getRoleId(), Integer.parseInt(split[1]));
                break;
            case "#regChatOccupy":  // doc: required string occupyMetaId;
                String occupyMetaId = split[1];
                regionCapitalService.GMChatOccupy(role.getRoleId(), occupyMetaId);
                break;
            case "#caravanResetCount":  // doc: required int timeType;
                int timeType = Integer.parseInt(split[1]);
                srvDpd.getCaravanService().GMResetCount(role, timeType);
                break;
            case "#caravanFinish":  // doc: required int caravanQueueId;
                int caravanQueueId = Integer.parseInt(split[1]);
                srvDpd.getCaravanService().GMQueueFinish(role, caravanQueueId);
                break;
            case "#newPlayerMissionSetDay":  // doc: optional int day = 1;
                day = 1;
                if (split.length >= 2) {
                    day = Integer.parseInt(split[1]);
                }
                srvDpd.getMissionNewPlayerService().gmSetDay(role, day);
                break;
            case "#divide":
                Application.getBean(ArmyManager.class).divideArmy(role);
                break;
            case "#NewStrongestLordScore":  // doc: required int score;
                long addScore = Long.parseLong(split[1]);
                newStrongestLordsActivityManager.addScoreAndUpdateRank(role, null, addScore);
                break;
            case "#NewStrongestLordFillRank": {  // doc: required int min; required int max;
                long min = Long.parseLong(split[1]);
                long max = Long.parseLong(split[2]);
                for (Role role1 : srvDpd.getRoleDao().findAll()) {
                    if (role1 == role) {
                        continue;
                    }
                    newStrongestLordsActivityManager.addScoreAndUpdateRank(role1, null, (long) RandomUtils.random(min, max));
                }
                break;
            }
            case "#NewStrongestLordGmOpenDay":  // doc: required string activityMetaId; required int aimDay;
                String activityMetaId = split[1];
                int aimDay = Integer.parseInt(split[2]);
                newStrongestLordsActivityManager.gmOpenDay(activityMetaId, aimDay);
                break;
            case "#endSecondQueue":
                var sec = WorkQueueType.BUILD2;
                var workProgressDao = Application.getBean(WorkProgressDao.class);
                var workprogess = workProgressDao.findByRoleAndMetaId(role.getId(), String.valueOf(sec.getId()));
                if (workprogess != null && workprogess.getCanAddTaskTm() > 0) {
                    workprogess.setCanAddTaskTm(TimeUtil.getNow());
                    role.send(WorkOutput.wrapperWorkList(Application.getBean(WorkProgressManager.class).getWorkProgressByRoleId(role.getId())));
                }
                break;
            case "#isinBlack":  // doc: required int x; required int y;
                var checkpoint = Point.getInstance(Integer.parseInt(split[1]), Integer.parseInt(split[2]));
                var grid = srvDpd.getWorldService().getGrid(role.getCurrentServerId(), checkpoint);
                debugMsg(role, "坐标:" + checkpoint.getX() + "," + checkpoint.getY() + "是不是黑土地?" + (grid.isBlack()), "");
                break;
            case "#setKvkStage":  // doc: required int stage;
                Application.getBean(KvkSeasonServiceImpl.class).changeKvkStage(Integer.parseInt(split[1]));
                break;
            case "#addKvkHonor":  // doc: required int addValue;
                Application.getBean(KvkHonorService.class).addHonor("gm", role, Integer.parseInt(split[1]));
                break;
            case "#setAllianceSeasonRank":  // doc: required int rank;
                alliance = allianceDao.findById(role.getAllianceId());
                if (alliance == null) {
                    debugMsg(role, "设置赛季联盟排行失败", "你没有联盟");
                    return;
                }
                alliance.setRank(Application.getSeason(), Integer.parseInt(split[1]));
                allianceDao.save(alliance);
                Application.getBean(KvkSeasonServiceImpl.class).pushAllianceSeasonReward(role, alliance);
                break;
            case "#getAllMailReward":
                Application.getBean(MailSdkService.class).getAllMailReward(role.getId());
                break;
            case "#setStoryEvent":  // doc: required int eventIdType; required int eventId;
                if (split.length != 3) {
                    return;
                }
                int mainEventId = 0;
                int randomEventId = 0;
                int storyEventId = 0;
                if (Integer.parseInt(split[1]) == 1) {
                    mainEventId = Integer.parseInt(split[2]);
                } else if (Integer.parseInt(split[1]) == 3) {
                    storyEventId = Integer.parseInt(split[2]);
                } else {
                    randomEventId = Integer.parseInt(split[2]);
                }
                storyService.eventUpdate(role, mainEventId, randomEventId, storyEventId, false, true);
                break;
            case "#logout":
                if (role.isOnline()) {
                    role.getPlayer().tryClose(null, ClosePlayerReason.GMKick);
                }
                break;
            case "#kira":
                srvDpd.getAsyncOperService().execute(new AllianceBattleMatchOperation(rankDao, srvDpd, allianceBattleInfoDao));
                break;
            case "#shareFrom":
                long shareFromRoleId = Long.parseLong(split[1]);
                srvDpd.getMissionShareService().GmSetShareFromRoleId(role, shareFromRoleId);
                break;
            case "#shareFinish":
                String finishChapterId = split[1];
                srvDpd.getMissionShareService().GmShareMissionCallback(role, finishChapterId);
                break;
            case "#allianceDonate":  // doc: required int donateValue;
                int donateValue = Integer.parseInt(split[1]);
                allianceMember = srvDpd.getAllianceMemberManager().getMember(role.getId());
                if (allianceMember != null) {
                    allianceMember.addDonate(donateValue);
                }
                break;
            case "#allianceBoss":  // doc: required int x; required int y;
                alliance = allianceDao.findById(role.getAllianceId());
                if (alliance == null) {
                    return;
                }
                int x = Integer.parseInt(split[1]);
                int y = Integer.parseInt(split[2]);
                var node = bossNodeDao.findByAllianceId(role.getAllianceId());
                if (node != null) {
                    allianceBossService.removeAllianceBoss(alliance);
                }
                alliance.setBossPoint(x, y);
                allianceDao.save(alliance);
                allianceBossService.initAllianceBossNode(alliance, false);
                break;
            case "#removeAllianceBoss":
                alliance = allianceDao.findById(role.getAllianceId());
                if (alliance == null) {
                    return;
                }
                alliance.setBossX(-1);
                alliance.setBossY(-1);
                alliance.setCanBuild(1);
                allianceBossService.removeAllianceBoss(alliance);
                break;
            case "#operateAllianceBoss":  // doc: required int operateType;
                int operateType = Integer.parseInt(split[1]);
                allianceBossService.gmOperateAllianceBoss(role, operateType);
                break;
            case "#popularWillAdd":  // doc: required int addValue;
                if (split.length != 2) {
                    return;
                }
                srvDpd.getPopularWillService().addPopularWill(role, Integer.parseInt(split[1]), PopularWillEventType.GM_ADD);
                break;
            case "#popularTargetWillAdd":  // doc: required int addValue;
                if (split.length != 2) {
                    return;
                }
                srvDpd.getPopularWillService().addTargetWill(role, Integer.parseInt(split[1]));
                break;
            case "#popularWillDel":  // doc: required int subValue;
                if (split.length != 2) {
                    return;
                }
                srvDpd.getPopularWillService().addPopularWill(role, -Integer.parseInt(split[1]), PopularWillEventType.GM_ADD);
                break;
            case "#popularTargetWillDel":  // doc: required int subValue;
                if (split.length != 2) {
                    return;
                }
                srvDpd.getPopularWillService().addTargetWill(role, -Integer.parseInt(split[1]));
                break;
            case "#updateaffair": {  // doc: required int affairType; optional long time=now();
                long time = split.length > 2 ? Long.parseLong(split[2]) : TimeUtil.getNow();
                srvDpd.getAllianceAffairService().updateAllianceAffair(role, Integer.parseInt(split[1]), time);
                break;
            }
            case "#openAllianceBossGround":
                srvDpd.getAllianceBossService().gmOpenAllianceBossGround(role);
                break;
            case "#deleteaffair":  // doc: required int affairType; optional String metaId="";
                String metaId = split.length > 2 ? split[2] : "";
                srvDpd.getAllianceAffairService().cancelAffair(AllianceAffairType.findById(Integer.parseInt(split[1])), role.getAllianceId(), metaId);
                break;
            case "#triggerZeroReset":
                zeroScheduleService.resetZeroFuture();
                allianceService.resetZeroFuture();
                srvDpd.getWorldBossDongZhuoService().onZero();
                break;
            case "#activityNotice": {  // doc: required String ...noticeIds;
                var noticeIds = new ArrayList<String>();
                for (var i = 1; i < split.length; i++) {
                    noticeIds.add(split[i]);
                }
                srvDpd.getActivityNoticeService().sendNotice(role, noticeIds);
                break;
            }
            case "#gvg":
                String function = split[1];
                switch (function) {
                    case "in":

                        GvgMatchType gvgMatchType = split.length >= 3 && split[2].equals("2") ? GvgMatchType.FRIENDLY_TEAM2 : GvgMatchType.FRIENDLY;
                        GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgGameDataVoManager.findGvgBattleServerDispatchRecordVo(role.getAllianceId(), gvgMatchType);
                        var gvgServerId = gvgBattleServerDispatchRecordVo.getBattleServerId().intValue();
                        gvgGameService.migrateGvgTest(role, gvgServerId, false, gvgMatchType);
                        break;
                    case "setStatus":
                        gvgLocalTestService.setStatusTestGM(split.length >= 4 && "1".equals(split[3]), Integer.parseInt(split[2]));
                        break;
//                        case "result":
//                            gvgBattleService.sendBattleResultTest(role, "1".equals(split[2]));
//                            break;
                    case "testStartService":
                        gvgGameService.startService();
                        break;
                    case "mail":
                        mailCreator.createSystemMail(role.getRoleId(), Application.getServerId(), EmailConstants.GVG_PILICHE_ATTACK_GUANDU, null, null, null);
                        break;
                    case "battleReset":
                        gvgLocalTestService.battleReset();
                        break;
                    case "notice":

                        for (StrongHoldNode strongHoldNode : strongHoldNodeDao.findAll()) {

                            GvgBuildingConfig.GvgBuildingMeta gvgBuildingMeta = configService.getConfig(GvgBuildingConfig.class).get(strongHoldNode.getMetaId());
                            if (gvgBuildingMeta == null || !JavaUtils.bool(gvgBuildingMeta.getBuildingOpenShow())) {
                                continue;
                            }

                            GcGVGBuildingOpen gcGVGBuildingOpen = new GcGVGBuildingOpen();
                            gcGVGBuildingOpen.setMetaId(gvgBuildingMeta.getId());
                            playerService.broadcast(0, Application.getServerId(), gcGVGBuildingOpen);
                        }

                        break;
                    case "clearFieldOpenFlag":
                        Collection<GVGBattleFieldTimeLine> all = gvgBattleFieldTimeLineDao.findAll();
                        for (GVGBattleFieldTimeLine gvgBattleFieldTimeLine : all) {
                            gvgBattleFieldTimeLine.setNoticedOpenFlag(new ArrayList<>());
                        }
                        break;
                    case "testStop":
                        gvgLocalTestService.battleStopGMTest(role);
                        break;
                    case "battledatadrop":
                        gvgLocalTestService.clearGVGBattleDataBase(split.length >= 3 && "clear".equals(split[2]));
                        break;
                    case "servertype":
                        ServerType serverType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(Integer.parseInt(split[2]));
                        break;
                    case "chatserver":
                        gvgGameService.getSortAllianceList(Integer.parseInt(split[2]));
                        break;
                    case "wound":
                        GVGStrongHoldService.piliCheAttackGuandu();
                        break;
                    case "piliMail":
                        Point position = Point.getInstance(new Random().nextInt(100), new Random().nextInt(100));
                        List<String> mailParams = new ArrayList<>();
                        String pointParam = mailCreator.getPointParam(Application.getServerId(), position.getX(), position.getY());
                        mailParams.add(pointParam);
                        mailParams.add(String.valueOf(new Random().nextInt(1000000)));
                        SystemEmail mail = mailCreator.createSystemMail(role.getRoleId(), Application.getServerId(), EmailConstants.GVG_PILICHE_ATTACK_GUANDU,
                                null, 1, null, mailParams, false, SystemEmailType.PILICHE_ATTACK,
                                "", null);
                        List<AbstractEmail> mailList = new ArrayList<>();
                        mailList.add(mail);
                        if (JavaUtils.bool(mailList)) {
                            srvDpd.getMailSender().sendBatchMail(mailList);
                        }
                        break;
                    case "offLine":
                        long roleId2 = Long.parseLong(split[2]);
                        targetRole = roleDao.findById(roleId2);
                        if (targetRole == null) {
                            debugMsg(role, "role 不存在", split[2]);
                            return;
                        }
                        if (targetRole.isOnline()) {
                            targetRole.getPlayer().tryClose(null, ClosePlayerReason.GMKick);
                        }
                        int dayGap = Integer.parseInt(split[3]);
                        targetRole.setLastLogoutTime(TimeUtil.getNow() - dayGap * TimeUtil.DAY_MILLIS - TimeUtil.HOUR_MILLIS);
                        targetRole.setLastLoginTime(TimeUtil.getNow() - dayGap * TimeUtil.DAY_MILLIS - TimeUtil.HOUR_MILLIS);
                        roleDao.save(targetRole);
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTimeInMillis(targetRole.getLastLogoutTime());
                        debugMsg(role, "修改成", calendar.getTime().toString());
                        break;
                    case "start": {
                        var timeline = gvgBattleFieldTimeLineDao.find();
                        if (timeline != null) {
                            long addTime = 0;
                            try {
                                addTime = Long.parseLong(split[2]);
                            } catch (Exception ignored) {

                            }
                            timeline.setStartTime(TimeUtil.getNow() + addTime);
                        }
                        gvgBattleFieldTimeLineDao.save(timeline);
                    }
                    break;
                    case "end": {
                        var timeline = gvgBattleFieldTimeLineDao.find();
                        if (timeline != null) {
                            long addTime = 0;
                            try {
                                addTime = Long.parseLong(split[2]) * TimeUtil.MINUTE_MILLIS;
                            } catch (Exception ignored) {

                            }
                            timeline.setEndTime(TimeUtil.getNow() + addTime);
                        }
                        gvgBattleFieldTimeLineDao.save(timeline);
                    }
                    break;
                }

                // 将GM命令发给中控
                IGameRemoteGVGControlService gameRemoteGVGControlService = gameRPCToGVGControlProxyService.getGameRemoteGVGControlService();
                if (gameRemoteGVGControlService == null) {
                    ErrorLogUtil.errorLog("中控服rpc连接失败");
                    return;
                }

                var args = new String[split.length - 1];
                System.arraycopy(split, 1, args, 0, split.length - 1);
                gameRemoteGVGControlService.gm(args);
                break;
            case "#gvgSetting":
                if(split.length == 3) {
                    String key = split[1];
                    String value = split[2];
                    if (key != null && value != null) {
                        var config = configService.getConfig(GvgSettingConfig.class);
                        config.parse(key, value);
                    }
                }
                break;
            case "#rallyBoss1000": {
                for (var i = 0; i < 1000; i++) {
                    rallyBoss(role);
                }
                break;
            }
            case "#peopleBorn":  // doc: optional int peopleCount=1;
                var peopleCount = split.length >= 2 ? Integer.parseInt(split[1]) : 1;
                srvDpd.getPeopleServiceImpl().gmTriggerBorn(role, peopleCount);
                break;
            case "#peopleBornMeta":  // doc: required String metaId; optional int peopleCount=1;
                if (split.length < 2) {
                    return;
                }
                srvDpd.getPeopleServiceImpl().gmTriggerBorn(role, split[1], split.length > 2 ? Integer.parseInt(split[2]) : 1);
                break;
            case "#peopleOnekeyAdd":
                srvDpd.getPeopleServiceImpl().gmFillFriendPeople(role);
                break;
            case "#peopleChangeSex": // peopleChangeSex {peopleId} {newSex}
                if(split.length < 3) return;
                int peopleid = Integer.parseInt(split[1]);
                int newSex = Integer.parseInt(split[2]);
                srvDpd.getPeopleFriendServiceImpl().gmChangeSex(role, peopleid, newSex);
                break;
            case "#peopleRecovery":
                srvDpd.getPeopleServiceImpl().gmRecoverPeople(role);
                break;
            case "#peopleDie":  // doc: required int peopleId;
                if (split.length < 2) {
                    return;
                }
                String reason = split.length > 2 ? split[2] : "ill";
                srvDpd.getPeopleServiceImpl().gmDie(role, Integer.parseInt(split[1]), reason);
                break;
            case "#peopleFrdPoint":
                if (split.length < 2) {
                    return;
                }
                int peopleId = Integer.parseInt(split[1]);
                int frdPoint = 100;
                if (split.length >= 3) {
                    frdPoint = Integer.parseInt(split[2]);
                }
                srvDpd.getPeopleServiceImpl().gmAddPeopleFrdPoint(role, peopleId, frdPoint);
                break;
            case "#itemFrdPoint":
                if (split.length < 4) {
                    return;
                }
                metaId = split[1];
                int cnt = Integer.parseInt(split[2]);
                String peopleIdStr = split[3];
                itemMgr.addItem(role, metaId, cnt,  ItemLogReason.DEBUG_ADD_ITEM);
                srvDpd.getItemService().useItemByMetaId(role, metaId, cnt, peopleIdStr);
                break;
            case "#giveFriendPerson":
                long frdIdG = 0;
                int frdTypeIdG = PeopleBornConditionType.SHARER_REGISTER_CALLBACK.getId();
                if (split.length >= 2) {
                    frdIdG = Long.parseLong(split[1]);
                }
                if (split.length >= 3) {
                    frdTypeIdG = Integer.parseInt(split[2]);
                }
                srvDpd.getPeopleServiceImpl().gmGiveFriendPerson(role, frdIdG, frdTypeIdG);
                break;
            case "#peopleFrindAdd":
                long frdId = 0;
                String frdName = "";
                String head = "";
                long accId = 0;
                int frdTypeId = PeopleBornConditionType.SHARER_REGISTER_CALLBACK.getId();
                if (split.length >= 2) {
                    frdId = Long.parseLong(split[1]);
                }
                if (split.length >= 3) {
                    accId = Long.parseLong(split[2]);
                }
                if (split.length >= 4) {
                    frdTypeId = Integer.parseInt(split[3]);
                }
                if (split.length >= 5) {
                    frdName = split[4];
                }
                if (split.length >= 6) {
                    head = split[5];
                }
                srvDpd.getPeopleServiceImpl().gmFriendGivePerson(role, frdId, accId, frdTypeId, frdName, head);
                break;
            case "#setDongzhuoAttackCount":  // doc: optional int attackCount=5;
                var attackCount = split.length > 1 ? Integer.parseInt(split[1]) : 5;
                srvDpd.getWorldBossDongZhuoService().gmSetRemainChangeCount(role, attackCount);
                break;
            case "#refreshDongzhuo":  // doc: optional int lastTime=0;
                if (split.length == 2) {
                    int lastTime = Integer.parseInt(split[1]);
                    srvDpd.getWorldBossDongZhuoService().gmRefreshDongzhuo(lastTime);
                    break;
                }
                // 获取上次活动刷新时间
                var activityDongzhuo = activityDao.findActivityByActivityType(ActivityType.WORLD_BOSS_DONGZHUO);
                if (activityDongzhuo == null) {
                    logger.info("world boss dongzhuo activity not found.");
                    return;
                }
                DongzhuoBossActivityContext contextDongzhuo = activityDongzhuo.getActivityContext();
                contextDongzhuo.setLastRefreshTime(0);
                activityDao.save(activityDongzhuo);
                break;
            case "#dropDongzhuoBox":
                srvDpd.getWorldBossDongZhuoService().dropBoxByRatio(role, true);
                break;
            case "#involvedDongzhuoBox":
                long boxId = Long.parseLong(split[1]);
                srvDpd.getWorldBossDongZhuoService().involvedDongzhuoBox(role, boxId);
                break;
            case "#vassalReportRoyalStart":
                srvDpd.getVassalService().gmReportRoyalStart(Integer.parseInt(split[1]));
                break;
            case "#vassalReportRoyalFinish":
                srvDpd.getVassalService().gmReportRoyalFinish(Long.parseLong(split[1]));
                break;
            case "#vassalOverview":
                srvDpd.getVassalService().vassalAllianceOverview(role, Integer.parseInt(split[1]));
                break;
            case "#allianceBattleAddScore":  // doc: required long score;
                long roleScore = Long.parseLong(split[1]);
                allianceBattleService.addScoreAndUpdateRank(role, null, roleScore);
                break;
            case "#allianceBattleSettle":
                var allianceBattleActivityMetaId = "600013";
                allianceBattleService.onZero(allianceBattleActivityMetaId);
                break;
            case "#seasonWarmUpAddScore":  // doc: required long score;
                long seasonWarmUpScore = Long.parseLong(split[1]);
                seasonWarmUpActivityService.addScore(role, seasonWarmUpScore);
                break;
            case "#seasonWarmUpSettle":
                var subActivityMetaId = "1";
                seasonWarmUpActivityService.settle(subActivityMetaId);
                break;
            case "#seasonWarmUpResetSettle":
                seasonWarmUpActivityService.gmResetSettleRecords();
                break;
            case "#seasonWarmUpsetSafeRegion":
                int group = 1;
                int color = 2;
                if (split.length > 2) {
                    group = Integer.parseInt(split[1]);
                    color = Integer.parseInt(split[2]);
                }
                seasonWarmUpActivityService.setSafeRegion(role, group, color);
                break;
            case "#seasonWarmUpSetOrder":
                int order = 1;
                if (split.length > 1) {
                    order = Integer.parseInt(split[1]);
                }
                seasonWarmUpActivityService.setSelectOrder(role, order);
                break;
            case "#seasonWarmUpClean":
                seasonWarmUpActivityService.gmClean();
                break;
            case "#seasonWarmUpShow":
                seasonWarmUpActivityService.gmShow();
                break;
            case "#allianceBattleMatch":
                srvDpd.getAsyncOperService().execute(new AllianceBattleMatchOperation(rankDao, srvDpd, allianceBattleInfoDao));
                break;
            case "#allianceBossBattleChatPush":
                allianceBossService.gmChatPush(role);
                break;
            case "#actCalendar":
                //打印活动日历
                debugMsg(role, "活动日历", debugDisplayCalendar(srvDpd.getActivityService().buildCalendar(role,5)));
                break;
            case "#actCal": {  // doc: optional int days=5; optional String changes=null; optional date choose=now();
                var days = 5;
                String changes = null;
                var choose = TimeUtil.getNow();
                if (split.length > 1) {
                    days = Integer.parseInt(split[1]);
                }
                if (split.length > 2) {
                    changes = split[2];
                }
                if (split.length > 3) {
                    choose = DateUtil.parseDate(split[3] + " 00:00:00", DateUtil.STYLE1);
                }
                //打印活动日历
                debugMsg(role, "活动日历", debugDisplayCalendar(srvDpd.getActivityService().gmBuildCalendar(choose, days, changes)));
                break;
            }
            case "#lordTreasureForge":  // doc: required String metaId;
                srvDpd.getLordTreasureService().forge(role, split[1]);
                break;
            case "#lordAll":
                srvDpd.getLordTreasureService().lordAll(role);
                break;
            case "#zhujiuboss": {  // doc: required String bossId;
                srvDpd.getWhispererActivityService().gmSummonBoss(role, split[1]);
                break;
            }
            case "#killzhujiuboss": {  // doc: required long damage;
                srvDpd.getWhispererActivityService().gmAttackBoss(role, Long.parseLong(split[1]));
                break;
            }
            case "#zhujiupass": {
                srvDpd.getWhispererActivityService().gmAllKill(role);
                break;
            }
            case "#nanmanattack": {
                var level = 0;
                if (split.length > 1) {
                    level = Integer.parseInt(split[1]);
                }
                srvDpd.getBarbarianActivityManager().gmStartBattle(role,level);
                break;
            }
            case "#nanmannext": {
                srvDpd.getBarbarianActivityManager().gmNextRound(role);
                break;
            }
            case "#nanmanstop": {
                srvDpd.getBarbarianActivityManager().gmStopBattle(role);
                break;
            }
            case "#nanmanreward": {
                srvDpd.getBarbarianActivityManager().gmReward(role);
                break;
            }
            case "#nanmanreset": {
                srvDpd.getAllianceService().broadcast(role.getAllianceId(),r->true,r->{
                    srvDpd.getBarbarianActivityManager().gmReset(r);
                });
                break;
            }
            case "#fillalliance": {
                var myAlliance = srvDpd.getAllianceService().getAllianceById(role.getAllianceId());
                if(myAlliance==null){
                    break;
                }
                //升级所有科技
                srvDpd.getAllianceTechService().gmFinishAllTech(role);
                //升至满级
                srvDpd.getRoleCurrencyManager().add(role,Currency.ALLIANCE_EXP,100000000000L,MoneyLogReason.GM_CMD_GIVE_MONEY,"");

                var id = TimeUtil.getNow();
                srvDpd.getAsyncOperService().execute(()->{
                    var curMember = allianceManager.getCurMember(myAlliance);
                    for (var t=curMember;t<=myAlliance.getMaxMember();t++ ) {
                        var robot = createRobot(role,id + t);
                        srvDpd.getAllianceService().requestJoin(robot,myAlliance.getId(),"",true );
                        // 自动迁城到联盟聚集点周围
                        srvDpd.getAllianceGatheringPlaceService().moveRoleCityAroundGatheringPlace(myAlliance, robot);
                    }
                    return false;
                });
                break;
            }
            case "#alliancegm":{  // doc: required String ...commands;
                var myAlliance = srvDpd.getAllianceService().getAllianceById(role.getAllianceId());
                if(myAlliance==null){
                    break;
                }
                if(split.length <= 1){
                    break;
                }
                var sub = new String[split.length - 1];
                System.arraycopy(split, 1, sub, 0, split.length - 1);
                //对联盟中的所有人执行此命令
                srvDpd.getAllianceService().broadcast(role.getAllianceId(),r->true,r->processCommand(r,sub));
                break;
            }
            case "#nanmanscore": {   // doc: required int score;
                var value= Integer.parseInt(split[1]);
                srvDpd.getBarbarianActivityManager().gmAddScore(role,value);
                break;
            }
            case "#nanmanfillalliancerank": {
                //自动创建联盟，并填满排行榜
                var myAlliance = srvDpd.getAllianceService().getAllianceById(role.getAllianceId());
                if(myAlliance==null){
                    break;
                }
                var finalCount = 10;
                srvDpd.getAsyncOperService().execute(()->{
                    if(finalCount > 0){
                        var id = TimeUtil.getNow();
                        for (var t=0;t<finalCount;t++ ) {
                            var robot = createRobot(role,id + t);
                            if(srvDpd.getAllianceService().gmCreateAlliance(robot,myAlliance,robot.getName(),UUID.randomUUID().toString().substring(0,2))){
                                srvDpd.getBarbarianActivityManager().gmAddScore(robot,3000000);
                            }
                        }
                    }
                    return false;
                });

                break;
            }
            case "#nanmanfillplayerrank": {
                //自动创建联盟，并填满排行榜
                var myAlliance = srvDpd.getAllianceService().getAllianceById(role.getAllianceId());
                if(myAlliance==null){
                    break;
                }


                var needCount = 100;
                if(needCount > 0){
                    var id = TimeUtil.getNow();
                    for (var t=0;t<needCount;t++ ) {
                        var robot = createRobot(role,id + t);
                        if(srvDpd.getAllianceService().gmCreateAlliance(robot,myAlliance,robot.getName(),UUID.randomUUID().toString().substring(0,2))){
                            srvDpd.getBarbarianActivityManager().gmAddScore(robot,10000);
                        }
                    }
                }

                break;
            }
            case "#seasontime":{
                var toDate = split[1];
                long toTimestamp;
                if(toDate.equals("0")){
                    toTimestamp = 0;
                }else{
                    var toTime = "00:00:00";
                    if (split.length > 2) {
                        toTime = split[2];
                    }
                    toTimestamp = DateUtil.parseDate(toDate + " " + toTime, DateUtil.STYLE1);
                }
                srvDpd.getKvkSeasonService().setGmSeasonTime(toTimestamp);
                srvDpd.getActivityService().checkConflictWithSeasonTime();
                break;
            }
            case "#diamonddetail":{
                debugMsg(role, "钻石详情", "免费钻石:["+srvDpd.getRoleCurrencyManager().getMoney(role,Currency.PRESENT_DIAMOND)+"] 付费钻石:["+srvDpd.getRoleCurrencyManager().getMoney(role,Currency.DIAMOND)+"]");
                break;
            }
            case "#tokendetail":{
                debugMsg(role, "代币详情", "免费代币:["+srvDpd.getRoleCurrencyManager().getMoney(role,Currency.FREE_CHARGE_TOKEN)+"] 付费代币:["+srvDpd.getRoleCurrencyManager().getMoney(role,Currency.CHARGE_TOKEN)+"]");
                break;
            }
            case "#startact":{ // doc: required String actId; optional int days;
                var actId = split[1];
                var meta = srvDpd.getConfigService().getConfig(ActivityListConfig.class).getMetaById(actId);
                if(meta == null){
                    debugMsg(role, "测试活动开启", "活动不存在:["+actId+"]");
                    break;
                }
                var curAct = srvDpd.getActivityService().getActivityByType(meta.getActivityType());
                if(curAct != null){
                    debugMsg(role, "测试活动开启", "已有活动存在:["+curAct+"]");
                    break;
                }

                var now = TimeUtil.getBeginOfDay(TimeUtil.getNow());
                var time = split.length >2 ? Integer.parseInt(split[2]) * TimeUtil.DAY_MILLIS : meta.getRoleEndTime();
                srvDpd.getActivityService().gmStartAct(meta,now,now + time);
                break;
            }
            case "#time": {  // doc: required String date; optional String time;
                var toDate = split[1];
                var toTime = "00:00:00";
                if (split.length > 2) {
                    toTime = split[2];
                }
                long toTimestamp = DateUtil.parseDate(toDate + " " + toTime, DateUtil.STYLE1);
                if (toTimestamp <= 0) {
                    ErrorLogUtil.errorLog("Change time date error" ,"toDate",toDate , "toTime", toTime);
                    debugMsg(role, "改时间指令", "时间参数错误！必须为 [#time 2024-07-10] 或者  [#time 2024-07-10 05:10:47] 格式");
                    break;
                }
                var nowTimestamp = TimeUtil.getNow();
                if (toTimestamp < nowTimestamp) {
                    debugMsg(role, "改时间指令", "时间参数错误！小于当前时间[" + DateUtil.format(
                            new Date(nowTimestamp), DateUtil.STYLE1) + "] 修改时间只能往后调");
                    break;
                }
                //开启异步操作
                srvDpd.getAsyncOperService().execute(new AsyncOperation() {
                    @Override
                    public boolean run() {

                        var now = TimeUtil.getNow();
                        while (toTimestamp > now) {

                            //先改到当天0点
                            var todayZero = DateUtil.getZeroClock(now + TimeUtil.DAY_MILLIS);
                            if (todayZero <= toTimestamp) {
                                //先跨天
                                debugMsg(role, "改时间指令", "目标时间【" + DateUtil.format(
                                        new Date(toTimestamp), DateUtil.STYLE1) + "】,先修改至【" + DateUtil.format(
                                        new Date(todayZero), DateUtil.STYLE1) + "】");
                                var ret = changeSystemTime(todayZero);
                                if (!ret.equals("ok")) {
                                    debugMsg(role, "改时间指令", ret);
                                    return false;
                                }

                                var newTime = TimeUtil.getNow();
                                if (newTime > todayZero) {
                                    debugMsg(role, "改时间指令", "已修改至【" + DateUtil.format(
                                            new Date(newTime), DateUtil.STYLE1) + "】 开始执行0点任务 期间等待3秒");
                                } else {
                                    debugMsg(role, "改时间指令", "修改失败 当前时间【" + DateUtil.format(
                                            new Date(newTime), DateUtil.STYLE1) + "】");
                                    return false;
                                }

                                srvDpd.getZeroScheduleService().debugZero();
                                srvDpd.getAllianceService().debugZero();

                                try {
                                    Thread.sleep(3000);
                                } catch (Exception e) {
                                    if (!(e instanceof ExpectedException)) {
                                        ErrorLogUtil.exceptionLog(e);
                                    }
                                    e.printStackTrace();
                                    debugMsg(role, "改时间指令", "Sleep 异常");
                                }

                            } else {
                                var ret = changeSystemTime(toTimestamp);
                                if (!ret.equals("ok")) {
                                    debugMsg(role, "改时间指令", ret);
                                    return false;
                                }

                                var newTime = TimeUtil.getNow();
                                if (newTime <= toTimestamp) {
                                    debugMsg(role, "改时间指令", "修改失败 当前时间【" + DateUtil.format(
                                            new Date(newTime), DateUtil.STYLE1) + "】");
                                    return false;
                                }

                                srvDpd.getZeroScheduleService().debugZero();
                                srvDpd.getAllianceService().debugZero();
                            }

                            now = TimeUtil.getNow();
                        }

                        //重置玩家的keepalive，保证不掉线
                        player.getLimitContext().setLastKeepAliveTime(now, 0);

                        debugMsg(role, "改时间指令", "修改完毕已修改至【" + DateUtil.format(
                                new Date(now), DateUtil.STYLE1) + "】");
                        return false;
                    }
                });
                break;
            }
            case "#resetZeroFuture": {
                logger.info("resetZeroFuture 重置跨天完成");
                srvDpd.getZeroScheduleService().resetZeroFuture();
                break;
            }
            case "#gmSetOpenServerTime": {
                if (split.length == 4) {
                    Application.getBean(ServerInfoServiceImpl.class).setServerOpenTime(Integer.parseInt(split[1]), TimeUtil.parseStr2MillTime(split[2] + " "+ split[3], "yyyy-MM-dd HH:mm:ss"));
                } else {
                    Application.getBean(ServerInfoServiceImpl.class).setServerOpenTime(TimeUtil.parseStr2MillTime(split[1] + " " + split[2], "yyyy-MM-dd HH:mm:ss"));
                }
                break;
            }
            case "#online": {  // 获取所有在线用户列表
                List<JSONObject> onlineRoleIds = MessageRecorder.getOnlineRoleIds();
                debugMsg(role, "在线用户列表", JSON.toJSONString(onlineRoleIds));
                break;
            }
            case "#record": {  // 开始录制消息 // doc: optional long recordTargetRoleId;
                long recordTargetRoleId = 0L;
                if (split.length >= 2) {
                    recordTargetRoleId = Long.parseLong(split[1]);
                } else {
                    recordTargetRoleId = role.getRoleId();

                }
                MessageRecorder.record(recordTargetRoleId);
                break;
            }
            case "#recordNext": {  // 录制下一个将要登录的用户的消息  // doc: optional long recordTargetAccountId;
                MessageRecorder.recordNext();
                break;
            }
            case "#recordAcc": {  // 录制下一个将要登录的用户的消息 // 一定要注册到本服 // 如果还未注册，则等待其登录  // doc: required String recordTargetAccountName;
                String recordAccountName = split[1];
                MessageRecorder.recordAcc(recordAccountName);
                break;
            }
            case "#recordAccStop": {  // 停止录制account的消息 // doc: required String recordTargetAccountName;
                String recordAccountName = split[1];
                MessageRecorder.recordAccStop(recordAccountName);
                break;
            }
            case "#recordGetInfo": {  // 获取录制信息
                String recordInfo = MessageRecorder.getRecordInfo();
                debugMsg(role, "录制消息的详情", recordInfo);
                break;
            }
            case "#recordStop": {  // 停止录制消息 // doc: optional long recordTargetRoleId;
                long recordTargetRoleId = role.getRoleId();
                if (split.length >= 2) {
                    recordTargetRoleId = Long.parseLong(split[1]);
                }
                List<String> msgs = MessageRecorder.stop(recordTargetRoleId);
                debugMsg(role, "停止录制消息", JSON.toJSONString(msgs));
                break;
            }
            case "#recordClear": {  // 停止录制所有用户
                MessageRecorder.clear();
                break;
            }
            case "#recordGet": {  // 获取录制的消息，不删除  // doc: optional long recordTargetRoleId;
                long recordTargetRoleId = role.getRoleId();
                if (split.length >= 2) {
                    recordTargetRoleId = Long.parseLong(split[1]);
                }
                List<String> msgs = new LinkedList<>();
                MessageRecorder.getRecord(recordTargetRoleId, msgs);
                debugMsg(role, "获取录制消息", JSON.toJSONString(msgs));
                break;
            }
            case "#newPlayerAndMigrate": {
                int level = 6;
                serverId = split.length > 1 ? Integer.parseInt(split[1]) : 0;
                if (serverId > 0) {
                    role.setTargetServerId(serverId);
                }
                if (role.getLevel() < 6) {
                    roleCityService.gmUpgradeAllBuilding(role, level);
                } else {
                    migrateService.gmMigrateServer(role.getRoleId(), serverId);
                }
                break;
            }
            case "#gmMigrate": {
                serverId = Integer.parseInt(split[1]);
                migrateService.gmMigrateServer(role.getRoleId(), serverId);
                break;
            }
            case "#gmModifyKvkSeason": {
                Application.getBean(KVKControlService.class).gmModifyKvkSeason(Integer.parseInt(split[1]), Long.parseLong(split[2]));
                break;
            }
            case "#resetGameServer": {
                boolean suc = Application.getBean(ServerInfoServiceImpl.class).checkAndResetServer();
                logger.warn("重置开服时间 suc={}", suc);
                break;
            }
            case "#horseallmax" : { // 一键全坐骑
                srvDpd.getHorseService().gmAllHorseMax(role);
                break;
            }
            case "#horsegetall" : { //获得所有坐骑
                srvDpd.getHorseService().gmAddAllHorse(role);
                break;
            }
            case "#horsealllevel": { //所有坐骑升至指定等级  // doc: required int level;
                var level = Integer.parseInt(split[1]);
                srvDpd.getHorseService().gmSetAllHorseLevel(role,level);
            }
            case "#horseallstar" : { //所有坐骑升至指定星级  // doc: required int star;
                var star = Integer.parseInt(split[1]);
                srvDpd.getHorseService().gmSetAllHorseStar(role,star);
                break;
            }
            case "#horseallequip" : { //所有坐骑装备升至指定等级  // doc: required int level;
                var equipLevel = Integer.parseInt(split[1]);
                srvDpd.getHorseService().gmSetAllHorseEquipLevel(role,equipLevel);
                break;
            }
            case "#horseadd" : { //获得指定id的坐骑  // doc: required int horseId;
                var horseId = Integer.parseInt(split[1]);
                srvDpd.getHorseService().gmAddHorse(role,horseId);
                break;
            }
            case "#horselevel" : { //指定id的坐骑升至指定等级 // doc: required int horseId;required int level;
                var horseId = Integer.parseInt(split[1]);
                var level = Integer.parseInt(split[2]);
                srvDpd.getHorseService().gmSetHorseLevel(role,horseId,level);
                break;
            }
            case "#horsestar" : { //指定id的坐骑升至指定星级 // doc: required int horseId;required int star;
                var horseId = Integer.parseInt(split[1]);
                var level = Integer.parseInt(split[2]);
                srvDpd.getHorseService().gmSetHorseStar(role,horseId,level);
                break;
            }
            case "#horseequip" : { //指定id的坐骑装备升至指定等级 // doc: required int horseId;required int level;
                var horseId = Integer.parseInt(split[1]);
                var level = Integer.parseInt(split[2]);
                srvDpd.getHorseService().gmSetHorseEquipLevel(role,horseId,level);
                break;
            }
            case "#resetHorseMate" : { // 重置坐骑交配状态
                srvDpd.getHorseService().gmResetHorseMate(role);
                break;
            }
            case "#groovy" :{
                var groovy = groovyEngineService.eval(split[1]);
                debugMsg(role,"groovy结果", JsonUtils.toJson(groovy));
                break;
            }
            case "#siegeNpcCount":
                if (split.length < 2) {
                    break;
                }
                siegeEnginesServiceImpl.gmChangeMonsterNum(role, Integer.parseInt(split[1]));
                break;
            case "#mission":{ //添加指定任务类型的积分 // doc: required int type;required int num;
                int type = Integer.parseInt(split[1]);
                int value = Integer.parseInt(split[2]);
                srvDpd.getMissionService().onMissionFinish(role, MissionType.findById(type),value);
                break;
            }
            case "#resetHorseName":{
                var horseId = Integer.parseInt(split[1]);
                srvDpd.getHorseService().gmResetHorseName(role, horseId);
                break;
            }
            //GM命令 模拟别人点击了你分享的
            case "#onFissionClick":{
                var fissionHandler = Application.getBean(FissionActivityHandler.class);
                long clickerId = Long.parseLong(split[1]);
                boolean isNewPlayer = Boolean.parseBoolean(split[3]);
                fissionHandler.onShareClick(role, clickerId, clickerId, split[2], isNewPlayer);
                break;
            }
            case "#clickOtherSharedFission":{
                PsShareArg psShareArg = new PsShareArg();
                psShareArg.setType(ShareCallbackType.SHARE_ACTIVITY.getId());
                psShareArg.setShareId(Long.parseLong(split[1]));
                psShareArg.setActivityMetaId(split[2]);
                Application.getBean(ShareServiceImpl.class).clickShareNew(role, psShareArg);
                break;
            }
            case "#testFissionRate":{
                var meta = configService.getConfig(SpringFestivalFissionConfig.class).getFissionMeta(split[1]);
                count = Integer.parseInt(split[2]);
                Map<String, Integer> itemMap = new HashMap<>();
                for (int i = 1; i <= count; i++) {
                    String itemId = meta.getToCollectItemId(i);
                    itemMap.compute(itemId, (k, v) -> (v == null ? 1 : v + 1));
                }
                Application.getBean(NoticeServiceImpl.class).notice(role, JSON.toJSONString(itemMap), false);
//                debugMsg(role, JSON.toJSONString(itemMap), "");
                break;
            }
            case "#luckybag": { //翻牌子跑数 // doc: required int times;
                int times = Integer.parseInt(split[1]);
                srvDpd.getAsyncOperService().execute(()->{
                    var result = Application.getBean(LuckyBagActivityManager.class).gmRunTimes(role,times);
                    debugMsg(role,"翻牌子跑数",result);
                    return false;
                });
                break;
            }
            case "#redpack": {
                //抢红包
                srvDpd.getRedPackActivityManager().gmGetRedPack(role);
            }
            case "#fakelucky": {
                Application.getBean(LuckyBagActivityManager.class).gmRecord(role,Integer.parseInt(split[1]),Integer.parseInt(split[2]),Integer.parseInt(split[3]));
                break;
            }
            default:
                break;
        }
    }

    private static String debugDisplayCalendar(ActivityCalendar calendar) {
        var sb = new StringBuilder();
        ;
        sb.append("当前时间:【").append(DateUtil.format(
                new Date(), DateUtil.STYLE1)).append("】,");
        sb.append("日历时间:【").append(DateUtil.format(
                new Date(calendar.getChooseTime()), DateUtil.STYLE1)).append("】,");
        sb.append("开服时间:【").append(DateUtil.format(
                new Date(calendar.getOpenTime()), DateUtil.STYLE1)).append("】,");
        sb.append("展示天数:【").append(calendar.getShowDays()).append("】,活动列表:");
        for (var act :
                calendar.getActivities()) {
            var meta = Application.getBean(ConfigServiceImpl.class).getConfig(ActivityListConfig.class).getMetaById(act.getMetaId());
            sb.append("【第").append(act.getRound()).append("轮活动-").append(meta != null ? meta.getActivityType().name() : "测试活动").append("- id:").append(act.getMetaId()).append(",开始时间:").append(DateUtil.format(
                    new Date(act.getStartTime()), DateUtil.STYLE1)).append(",结束时间:").append(DateUtil.format(
                    new Date(act.getEndTime()), DateUtil.STYLE1)).append("】");
        }

        return sb.toString();
    }

    private Role createRobot(Role source,long id) {
        GameRPCToWebProxyService webRPCProxyService = Application.getBean(GameRPCToWebProxyService.class);
        ICommonRemoteWebService commonRemoteWebService = webRPCProxyService.getCommonRemoteWebService();
        var roleid = commonRemoteWebService.gmCreateRole(id,source.getoServerId());
        var simPlayer = new Player();
        simPlayer.setSimUser(true);
        simPlayer.setId(roleid);
        simPlayer.setAccountId(id);
        simPlayer.setUserId(id);
        simPlayer.setoServerId(source.getoServerId());
        simPlayer.setCurrentServerId(source.getCurrentServerId());
        simPlayer.setOnlineState(stateFactory.getState(AbstractOnlineState.Type.LOGINED));
        simPlayer.getLoginContext().setExtParam(new LoginExtParam());
        simPlayer.getLoginContext().getExtParam().setDeviceId("100001");
        simPlayer.getLoginContext().getExtParam().setIp("0.0.0.0");
        simPlayer.getLoginContext().getExtParam().setDeviceType(DeviceType.ANDROID);
        simPlayer.getLoginContext().getExtParam().setPushPlatformType(source.getPlayer().getLoginContext().getExtParam().getPushPlatformType());
        simPlayer.getLoginContext().getExtParam().setClientVersion(source.getPlayer().getLoginContext().getExtParam().getClientVersion());
        simPlayer.getLoginContext().getExtParam().setChannelF("robot");
        srvDpd.getPlayerService().add(simPlayer);
        var createRole = srvDpd.getRoleService().gmCreateRole(roleid,simPlayer);
        //手动设置一下
        createRole.setCurrentServerId(source.getCurrentServerId());
        srvDpd.getPlayerService().initPlayer(simPlayer);
        // 进入ENTERING状态，通知前端
        simPlayer.getOnlineState().loaded(simPlayer);
        //enter world
        srvDpd.getLoginService().login(simPlayer);
        srvDpd.getLoginService().enterWorld(simPlayer,null);
        //执行 #upgradeAllBuilding 10
        // 先设置建筑建造需要的解锁关卡
        Application.getBean(ExpeditionNumberGateService.class).updateLevel(createRole, srvDpd.getConfigService().getConfig(BtlMapConfig.class).getMaxLevel());
        roleCityService.gmUpgradeAllBuilding(createRole, 20);
        //给钻石 #
        return createRole;
    }

    private static String changeSystemTime(long timestamp) {
        String command = null;
        var date = DateUtil.format(new Date(timestamp), DateUtil.STYLE1).split(" ");

        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            // Windows
            // Windows 命令格式: date yyyy-MM-dd & time HH:mm:ss
            command = "cmd.exe /c date " + date[0] + " & time " + date[1];
        } else if (os.contains("mac") || os.contains("nix") || os.contains("nux")) {
            // macOS / Unix / Linux
            // Unix/Linux命令格式: date -s "yyyy-MM-dd HH:mm:ss"
            command = "date -s \"" + date[0] + " " + date[1] + "\"";
        }

        if (command != null) {
            try {
                logger.info("Executing command: " + command);
                Process process = Runtime.getRuntime().exec(command);
                BufferedReader reader =
                        new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info(line);
                }
                int code = process.waitFor();
                logger.info("System time set successfully.Exit code:" + code);
                return "ok";
            } catch (IOException | InterruptedException e) {
                ErrorLogUtil.exceptionLog("System time change error",e);
                return "发生异常:" + e.getMessage();
            }
        }

        return "未知的操作系统";
    }

    public static void debugMsg(Role role, String title, String msg) {
        GcNoticeInfo noticeInfo1 = new GcNoticeInfo();
        noticeInfo1.setType(PsNoticeType.DEBUG_LOG);
        noticeInfo1.setContent(msg);
        noticeInfo1.setTitle(title);
        role.send(noticeInfo1);
    }

    private void recon(Role role) {
        logger.info("清理已经领取邮件的附件...");
        Map<Long, String> map = new HashMap<>();
        map.put(500000000425231L, "10106003|6;19908002|2;19908001|2;10205002|4;10101004|8;10102004|8;10103003|20;10104003|10;10401002|20");
        map.put(100000240331L, "10304001|10");

        int roleSize = 0;
        var roleItemManager = Application.getBean(RoleItemManager.class);
        for (var entry : map.entrySet()) {
            var roleToRemoveItem = roleDao.findById(entry.getKey());
            var items = entry.getValue();
            var data = StringUtils.split(items, AbstractMeta.META_SEPARATOR_3);
            for (var temp : data) {
                var item = StringUtils.split(temp, AbstractMeta.META_SEPARATOR_2);
                if (item.length != 2) {
                    ErrorLogUtil.errorLog("format error", "roleToRemoveItem",entry.getKey(),"item", temp);
                    continue;
                }
                String itemMetaId = item[0];
                int itemCount = Integer.parseInt(item[1]);
                if (roleToRemoveItem == null) {
                    logger.info(" roleToRemoveItem is not in memory or roleToRemoveItem is in other server roleToRemoveItem={}, metaId={} num={}", entry.getKey(), itemMetaId, itemCount);
                    continue;
                }
                roleSize++;
                int haveCount = roleItemManager.getItemAmount(roleToRemoveItem, itemMetaId);
                try {
                    if (haveCount < itemCount) {
                        ErrorLogUtil.errorLog("roleToRemoveItem has not enough to sub item ", "roleToRemoveItem",entry.getKey(),"itemMetaId", itemMetaId, "count",itemCount, "remove count",haveCount);
                        roleItemManager.removeItem(roleToRemoveItem, itemMetaId, haveCount, LogReasons.ItemLogReason.GM_CMD_COST_ITEM);
                    } else {
                        logger.info("roleToRemoveItem has enough item roleToRemoveItem={} itemMetaId={} count={}", entry.getKey(), itemMetaId, itemCount);
                        roleItemManager.removeItem(roleToRemoveItem, itemMetaId, itemCount, LogReasons.ItemLogReason.GM_CMD_COST_ITEM);
                    }
                } catch (ExpectedException ignored) {

                } catch (Exception e) {
                    ErrorLogUtil.exceptionLog("remove item error",e,"roleToRemoveItem", entry.getKey(),
                            "itemMeta", itemMetaId, "num",itemCount, "haveNum",haveCount);
                }
            }
            // 1. 先获取所有存在民心数据的玩家信息
        }
        logger.info("刪除道具:全部完成 roleSize={}", roleSize);
    }

    public void rallyBoss(Role role) {
        var alliance = allianceDao.findById(role.getAllianceId());
        if (alliance == null) {
            debugMsg(role, "发起集结失败", "请先加入联盟");
            return;
        }
        var cityPos = roleCityDao.findById(role.getRoleId()).getPosition();
        var nodeList = sceneService.getAoiNodesByType(role.getCurrentServerId(), cityPos, SceneNodeType.WORLD_BOSS);
        if (!JavaUtils.bool(nodeList)) {
            debugMsg(role, "发起集结失败", "没有找到boss");
            return;
        }
        var soldiers = soldierManager.getAllSoldier(role);
        var idCount = 2000;
        String idsString = "11001,11002,11003,21004,21005,21006";
        Set<String> ids = new HashSet<String>(Arrays.asList(idsString.split(",")));
        for (var id : ids) {
            var s = soldiers.get(id);
            if (s == null || s.getCount() < idCount) {
                soldierService.addSoldiers(role, id, idCount, SoldierUpdateReasonType.GM_GIVE, false);
            }
        }
        var npc = RandomUtils.random(nodeList);
        CgArmySetout armyMessage = new CgArmySetout();
        armyMessage.setSetoutType(PsArmyType.WORLD_BOSS_ATTACK);
        armyMessage.setNodeType(PsMapNodeType.WORLD_BOSS);
        armyMessage.setX(npc.getX());
        armyMessage.setY(npc.getY());
        armyMessage.setBodyType(PsArmyBodyType.NORMAL);
        armyMessage.setTimeMetaId("301");
        for (var id : ids) {
            armyMessage.putToSoldiers(id, idCount);
        }
        ArmySetoutParam param = new ArmySetoutParam(armyMessage);
        param.setSkipCheck(true);
        param.setServerId(role.getCurrentServerId());
        Collection<Hero> heros = Application.getBean(HeroServiceImpl.class).selectHeroForFight(role.getRoleId(), List.of());
        param.setHeros(heros.stream().map(Hero::getMetaId).collect(Collectors.toList()));
        ArmyProcessor processer = armyProcessorFactory.getProcessorByArmyType(param.getArmyType());
        processer.excute(role, param);
    }
}
