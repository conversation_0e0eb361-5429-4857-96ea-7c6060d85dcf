package com.lc.billion.icefire.game.biz.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.biz.model.expedition.ExpeditionLevelType;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Config(name = "BtlMap", metaClass = BtlMapConfig.BtlMapMeta.class)
public class BtlMapConfig {

    //等级
    private Map<Integer, BtlMapMeta> levelsMap;

    //初始等级
    private int initLevel;

    //最大等级
    @Getter
    private int maxLevel;

    public void init(List<BtlMapMeta> list) {
        if (list != null && !list.isEmpty()) {
            levelsMap = new HashMap<>();
            initLevel = list.getFirst().level;
            for (BtlMapMeta meta : list) {
                levelsMap.put(meta.level, meta);
                if (meta.level < initLevel) {
                    initLevel = meta.level;
                }
                maxLevel = Math.max(maxLevel, meta.level);
            }
        }
    }

    public BtlMapMeta getMetaById(int id) {
        return levelsMap.get(id);
    }

    public int getNextLevel(int level) {
        if (level == 0) {
            return initLevel;
        }
        var meta = getMetaById(level);
        if (meta == null) {
            return -1;
        }
        var nextLevel = level + 1;
        if (levelsMap.containsKey(nextLevel)) {
            return nextLevel;
        }
        return -1;
    }

    public ExpeditionLevelType getLevelType(int level) {
        if (level == 0) {
            return ExpeditionLevelType.UNKNOW;
        }
        var meta = getMetaById(level);
        if (meta == null) {
            return ExpeditionLevelType.UNKNOW;
        }
        // 基于serverCheck字段判断关卡类型
        // serverCheck = 0 表示剧情关卡，其他为普通关卡
        return meta.getServerCheck() == 0 ? ExpeditionLevelType.STORY : ExpeditionLevelType.NORMAL;
    }

    public static class  BtlMapMeta extends AbstractMeta{

        //关卡id
        private int level;
        //关卡奖励
        @JsonProperty("Reward_id")
        private String rewardId;
        //是否后端校验
        @JsonProperty("ServerCheck")
        private int serverCheck;

        public String getRewardId() {
            return rewardId;
        }

        public void setRewardId(String rewardId) {
            this.rewardId = rewardId;
        }

        public int getServerCheck() {
            return serverCheck;
        }

        public void setServerCheck(int serverCheck) {
            this.serverCheck = serverCheck;
        }

        @Override
        public void init(JsonNode json) {
            level = Integer.parseInt(getId());
        }

    }

}
