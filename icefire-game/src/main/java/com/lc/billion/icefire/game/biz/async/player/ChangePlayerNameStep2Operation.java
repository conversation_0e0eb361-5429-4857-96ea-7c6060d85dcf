package com.lc.billion.icefire.game.biz.async.player;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.alliance.async.AsyncCheckWordOperation;
import com.lc.billion.icefire.game.biz.service.impl.player.PlayerServiceImpl;
import com.lc.billion.icefire.protocol.GcPlayerInvalidChar;

public class ChangePlayerNameStep2Operation extends AsyncCheckWordOperation {
	private PlayerServiceImpl playerService;
	private String name;
	private String metaId;

	public ChangePlayerNameStep2Operation(Role role, PlayerServiceImpl playerService, String name, String itemMetaId) {
		this.role = role;
		this.playerService = playerService;
		this.name = name;
		this.metaId = itemMetaId;
		setWord(name);
	}

	@Override
	public void finish() {
		if (isSpam()) {
			role.send(new GcPlayerInvalidChar(name));
		} else {
			playerService.changeNameStep3(role, name, metaId);
		}
	}
}
