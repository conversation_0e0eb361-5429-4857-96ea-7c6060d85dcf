package com.lc.billion.icefire.game;

/**
 * 一些与业务无关的常量定义
 *
 * <AUTHOR>
 */
public class GameConstants {

	public static final String ZOOKEEPER_CONFIG_NAME = "zookeeper.json";

	public static final String MONGO_CONFIG_NAME = "mongo.json";

	public static final String GAME_CONFIG_NAME = "game.json";

	// public static final String DC_CONFIG_NAME = "dc.json";

	// public static final String RPC_CONFIG_NAME = "rpc.json";

	public static final String MAP_CONFIG_NAME = "servertype.json";

	public static final String BI_CONFIG_NAME = "bi.json";

	// public static final String MAP_CONFIG_NAME = "bi.json";

	/**
	 * { "scienceOld": true, "scienceNew": true }
	 */
	// public static final String NEW_DB_CONFIG_NAME = "new-db.json";

	public static final String GVG_BATTLE_CREATE_CONFIG_NAME = "gvgbattlecreate.json";

	public static final String ALERT_CONFIG_NAME = "alert.json";

	public static final String TVT_BATTLE_CREATE_CONFIG_NAME = "tvtbattlecreate.json";

	public static final String ALIPAY_REPORT_CONFIG_NAME = "alipayReportConfig.json";

	public static final String APPLICATION_CONFIG_NAME = "application.json";

}
