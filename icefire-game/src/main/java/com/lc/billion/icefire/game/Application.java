package com.lc.billion.icefire.game;

import com.lc.billion.icefire.core.Constants;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.csacontrol.biz.service.rpc.CSAControlRPCToGameProxyService;
import com.lc.billion.icefire.game.biz.exec.MainWorker;
import com.lc.billion.icefire.game.biz.exec.WorldScheduler;
import com.lc.billion.icefire.game.biz.flyway.FlywayHolder;
import com.lc.billion.icefire.game.biz.flyway.FlywayLocationType;
import com.lc.billion.icefire.game.biz.flyway.FlywayMultiHolder;
import com.lc.billion.icefire.game.biz.service.ServiceStarter;
import com.lc.billion.icefire.game.biz.service.ServiceStarter.Future;
import com.lc.billion.icefire.game.biz.service.impl.DaoService;
import com.lc.billion.icefire.game.biz.service.impl.account.GroovyEngineServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionSwitchService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.gameMsg.GameMsgLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.biz.service.rpc.*;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.jmx.GameServerStatus;
import com.lc.billion.icefire.game.msg.AsyncMessageSenderManager;
import com.lc.billion.icefire.game.net.WebSocketServerInitializer;
import com.lc.billion.icefire.game.tool.config.impl.ConfigCenterForCompileCheck;
import com.lc.billion.icefire.gvgbattle.biz.service.rpc.GVGBattleRPCToGVGControlProxyService;
import com.lc.billion.icefire.gvgbattle.biz.service.rpc.GVGBattleRPCToGameProxyService;
import com.lc.billion.icefire.gvgbattle.biz.service.rpc.TVTBattleRPCToTVTControlProxyService;
import com.lc.billion.icefire.gvgcontrol.biz.service.rpc.GVGControlRPCToGVGBattleProxyService;
import com.lc.billion.icefire.gvgcontrol.biz.service.rpc.GVGControlRPCToGameProxyService;
import com.lc.billion.icefire.tvtcontrol.biz.service.rpc.TVTControlRPCToTVTBattleProxyService;
import com.lc.billion.icefire.tvtcontrol.biz.service.rpc.TvtControlRpcToGameProxyService;
import com.longtech.cod.socket.netty.NettyTraffic;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.config.WorldMapConfig;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.simfun.sgf.net.EncodedMsg;
import com.simfun.sgf.net.SocketServer;
import com.simfun.sgf.utils.JavaUtils;
import lombok.Getter;
import org.apache.thrift.TBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;

import javax.annotation.Nonnull;
import javax.management.InstanceAlreadyExistsException;
import javax.management.MBeanRegistrationException;
import javax.management.MalformedObjectNameException;
import javax.management.NotCompliantMBeanException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class Application {
    private static Logger logger = LoggerFactory.getLogger(Application.class);

    @Getter
    private static ApplicationContext applicationContext;

    @Getter
    private static AsyncMessageSenderManager asyncMessageSenderManager;

    @Getter
    private static WorldScheduler worldScheduler;
    @Getter
    private static ConfigCenter configCenter;

    //encoder for BroadcastService
    private static WebSocketServerInitializer serverInitializer;

    // 临时修复数据时使用
    @Getter
    private static final Map<String, Object> fixData = new ConcurrentHashMap<>();

    public static void setApplicationContext(ApplicationContext applicationContext) {
        Application.applicationContext = applicationContext;
        try {
            configCenter = getBean(ConfigCenter.class);

            getBean(GroovyEngineServiceImpl.class).init();
        } catch (NoSuchBeanDefinitionException e) {
            // 应该只在 CompileCheck 时才会出现，可以忽略之。
            logger.warn("应该只在 CompileCheck 时才会出现，可以忽略之。", e);
        }
    }

    @Nonnull
    public static <T> T getBean(Class<T> requiredType) {
        if (applicationContext == null) {
            return null;
        }
        return applicationContext.getBean(requiredType);
    }

    public static <T> T getBean(String name, Class<T> requiredType) {
        if (applicationContext == null) {
            return null;
        }
        return applicationContext.getBean(name, requiredType);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getBeanByName(String name) {
        return (T) applicationContext.getBean(name);
    }

    public static MainWorker getMainWorker() {
        return getBean(MainWorker.class);
    }

    public static void initBizConfig() {
        ConfigServiceImpl configureService = getBean(ConfigServiceImpl.class);
        configureService.start();
        logger.info("bizConfig init");
    }

    public static void flywayUpdateBeforeDao() {
        FlywayMultiHolder holder = getBean(FlywayMultiHolder.class);
        // 升级游戏服或者赛季服的所有数据库
        Map<Integer, String> dbVersionMap = holder.update(FlywayLocationType.LOCATION_BEFORE_DAO);
        // 更新zk里每个服对应的数据库版本号
        dbVersionMap.forEach((k, v) -> {
            Application.getConfigCenter().getLsConfig().getGameServers().get(k).setDbVersion(v);
        });
        // 找到个最大dbVersion，然后等待所有db升到同一版本号
        String dbVersion = "";
        for (Map.Entry<Integer, String> next : dbVersionMap.entrySet()) {
            if (next.getValue() != null && next.getValue().compareTo(dbVersion) > 0) {
                dbVersion = next.getValue();
            }
        }
        if (dbVersion.isEmpty()) {
            throw new AlertException("flyway升级异常");
        }
        waitAllGameServerDbVersion(dbVersion);
    }

    private static void waitAllGameServerDbVersion(String dbVersion) {
        // 只有 GAME 和 KVK_SEASON 才检查其他服的数据库版本。
        if (Application.getConfigCenter().currentServerTypeIsGAME_or_KVKSEASON()) {
            logger.info("本服务器启动类型是：{}，需要等待其他GameServer数据库到相同版本：{}。", Application.getServerType(), dbVersion);
            while (!Application.getConfigCenter().checkGameServersDbVersion()) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.info("等待其他GameServer数据库升级到相同版本（" + dbVersion + "）时，线程被中断！", e);
                }
            }
            logger.info("所有GameServer的flyway版本相同：{}。", dbVersion);
            // 为避免所有服同时捞取数据对mongo造成压力，随机等待
            WorldServiceImpl.randomSleep(60);
        }
    }

    public static void flywayUpdateAfterService() {
        // afterService 不需要多服执行，和beforeDao不同。因为赛季服多个服的数据都在同一内存
        FlywayHolder flywayHolder = getBean(FlywayHolder.class);
        flywayHolder.update(FlywayLocationType.LOCATION_AFTER_SERVICE);
    }

    public static void afterDataReady() {
        ServiceStarter srvStarter = getBean(ServiceStarter.class);
        // 在主业务worker中处理，需要等待
        Future<?> future = srvStarter.afterDataReady();
        future.awaitUninterruptibly();
        if (!future.isSuccess()) {
            throw JavaUtils.sneakyThrow(future.getCause());
        }
    }

    // public static void flywayUpdateAfterService1() {
    // FlywayMultiHolder holder = getBean(FlywayMultiHolder.class);
    // holder.update(FlywayLocationType.LOCATION_AFTER_SERVICE);
    // }

    public static void initRuntime() throws InstanceAlreadyExistsException, MBeanRegistrationException, NotCompliantMBeanException, MalformedObjectNameException {
        GameServerStatus.init();

        MainWorker mainWorker = getBean(MainWorker.class);
        mainWorker.start();

        WorldServiceImpl worldService = getBean(WorldServiceImpl.class);
        worldService.init();

        int poolThreadNum = ServerConfigManager.getInstance().getGameConfig().getAsyncThreadSize();
        asyncMessageSenderManager = new AsyncMessageSenderManager(poolThreadNum);
        asyncMessageSenderManager.start();
    }

    public static void initWorldScheduler() {
        worldScheduler = new WorldScheduler();
        worldScheduler.start();
    }

    public static void startServices() {
        ServiceStarter srvStarter = getBean(ServiceStarter.class);
        // 在主业务worker中处理，需要等待
        Future<?> future = srvStarter.startService();
        future.awaitUninterruptibly();
        if (!future.isSuccess()) {
            throw JavaUtils.sneakyThrow(future.getCause());
        }
    }

    public static void startServicesAfterNetSocketCreate() {
        ServiceStarter srvStarter = getBean(ServiceStarter.class);
        // 在主业务worker中处理，需要等待
        Future<?> future = srvStarter.startServiceAfterNetSocketCreate();
        future.awaitUninterruptibly();
        if (!future.isSuccess()) {
            throw JavaUtils.sneakyThrow(future.getCause());
        }
    }

    public static void beforeLoadDao() {
        DaoService loadDaoService = getBean(DaoService.class);
        // 在主业务worker中处理，需要等待
        loadDaoService.beforeLoadAll();
    }

    public static void loadDao() {
        DaoService loadDaoService = getBean(DaoService.class);
        // 在主业务worker中处理，需要等待
        loadDaoService.loadAll();
    }

    public static void initNet() {
        WorldMapConfig worldMapConfig = ServerConfigManager.getInstance().getWorldMapConfig();
        ServerType serverType = worldMapConfig.getServerType();
        if (serverType == ServerType.GVG_CONTROL || serverType == ServerType.CSA_CONTROL || serverType == ServerType.TVT_CONTROL) {
            // 中控服不开20009端口
            return;
        }
        GameConfig gameConfig = ServerConfigManager.getInstance().getGameConfig();
        ConfigCenter cc = configCenter;
        GameServerConfig gameServerConfig = cc.getCurrentGameServerConfig();
        serverInitializer = new WebSocketServerInitializer(null);
        SocketServer gcServer = new SocketServer("gc-server", gameServerConfig.getGameBindIp(), gameServerConfig.getGameBindPort(), gameConfig.getClientIoWorkerCount(),
                serverInitializer);
        gcServer.start();

        AppCleaner.ins().addSocketServer(gcServer);
    }

    public static EncodedMsg encodeMsg(TBase<?,?> message){
        var encoder = serverInitializer == null ? null : serverInitializer.getMessageEncoder();
        if(encoder == null){
            return null;
        }
        var msg = encoder.encodeMsg(message);
        if(msg == null){
            return null;
        }
        msg.setBodyStr(GameMsgLogUtil.getBodyStr(message));
        return msg;
    }

    public static void initNode() {
        // CommonSerializer.init();// 初始化序列化
        int serverId = getServerId();

        String logFileName = Constants.NODE_WORLD_PREFIX + serverId;
        System.setProperty("logFileName", logFileName);

        // final String nodeId = Constants.NODE_WORLD_PREFIX + serverId;
        // String nodeAddr = Distr.getNodeAddr(nodeId);
        // final Node node = new Node(nodeId, nodeAddr);

        // /* 1 设置远程Node */
        //
        // // 1.3 游戏服务器
        // for (String nid : Distr.WORLD_NODES) {
        // // 不用连接自己
        // if (nodeId.equals(nid))
        // continue;
        // // 连接远程
        // node.addRemoteNode(nid, Distr.getNodeAddr(nid));
        // }
        //
        // // 1.5 平台服务器
        // node.addRemoteNode(Constants.NODE_PF, Distr.getNodeAddr(Constants.NODE_PF));

        // /* 2 加载系统数据 */
        // // 2.1 创建个临时Port
        // mainPort = new GamePort(Constants.PORT_DEFAULT);
        // mainPort.startup(node);

        // mainPort = getBean(MainWorker.class);
        // mainPort.startup(node);

        // TransferPlayerDataService transferDataServer = new
        // TransferPlayerDataService(mainPort);
        // transferDataServer.startup();
        // mainPort.addService(transferDataServer);

        // extPort = new GamePort(Constants.PORT_EXT);
        // extPort.startup(node);

        // // 定时打印服务器状态
        // ServerStatusService serverStatusService = new ServerStatusService(mainPort);
        // serverStatusService.startup();
        // mainPort.addService(serverStatusService);

        // node.startup();
        //
        // // 标记此Node加载完成
        // node.startupComplete();
        //
        // // 启动日志信息
        // logger.info("====================");
        // logger.info("Node " + nodeId + " started.");
        // logger.info("Listen:" + nodeAddr);
        // logger.info("====================");

        // 系统关闭时进行清理
        Runtime.getRuntime().addShutdownHook(new Thread() {
            public void run() {
                logger.info("GameServer shutdown. [{}]", LocalDateTime.now());
            }
        });
    }

    public static void initRPC() throws InterruptedException {
        GameServerConfig gc = configCenter.getCurrentGameServerConfig();
        int gameThreads = 1;
        GameRpcServer rpcService = new GameRpcServer(gc.getRpcBindIp(), gc.getRpcBindPort(), gameThreads);
        rpcService.init();
    }

    public static void initRPCServices() {
        long startTime = System.currentTimeMillis();
        logger.info("initRPCServices start");

        // gvgbattle to game
        GVGBattleRPCToGameProxyService gvgBattleRPCToGameProxyService = getBean(GVGBattleRPCToGameProxyService.class);
        gvgBattleRPCToGameProxyService.init();
        // gvgbattle to control
        GVGBattleRPCToGVGControlProxyService gvgBattleRPCToGVGControlProxyService = getBean(GVGBattleRPCToGVGControlProxyService.class);
        gvgBattleRPCToGVGControlProxyService.init();

        // game to game
        GameRPCToGameProxyService gameRPCToGameProxyService = getBean(GameRPCToGameProxyService.class);
        gameRPCToGameProxyService.init();
        // game to gvgbattle
        GameRPCToGVGBattleProxyService gameRPCToGVGBattleProxyService = getBean(GameRPCToGVGBattleProxyService.class);
        gameRPCToGVGBattleProxyService.init();
        // game to gvgcontrol
        GameRPCToGVGControlProxyService gameRPCToGVGControlProxyService = getBean(GameRPCToGVGControlProxyService.class);
        gameRPCToGVGControlProxyService.init();

        // gvgcontrol to game
        GVGControlRPCToGameProxyService gvgControlRPCToGameProxyService = getBean(GVGControlRPCToGameProxyService.class);
        gvgControlRPCToGameProxyService.init();

        // gvgcontrol to battle
        GVGControlRPCToGVGBattleProxyService gvgControlRPCToGVGBattleProxyService = getBean(GVGControlRPCToGVGBattleProxyService.class);
        gvgControlRPCToGVGBattleProxyService.init();

        // tvtcontrol to game
        TvtControlRpcToGameProxyService tvtControlRpcToGameProxyService = getBean(TvtControlRpcToGameProxyService.class);
        tvtControlRpcToGameProxyService.init();

        TVTControlRPCToTVTBattleProxyService tvtControlRPCToTVTBattleProxyService = getBean(TVTControlRPCToTVTBattleProxyService.class);
        tvtControlRPCToTVTBattleProxyService.init();

        TVTBattleRPCToTVTControlProxyService tvtBattleRPCToTVTControlProxyService = getBean(TVTBattleRPCToTVTControlProxyService.class);
        tvtBattleRPCToTVTControlProxyService.init();

        GameRPCToTvtControlProxyService gameRPCToTvtControlProxyService = getBean(GameRPCToTvtControlProxyService.class);
        gameRPCToTvtControlProxyService.init();

        FunctionSwitchService switchService = getBean(FunctionSwitchService.class);
        if (switchService.isOpen(FunctionType.CROSS_SERVER_ATTACK)) {
            // game to csa control
            GameRPCToCSAControlProxyService gameRPCToCSAControlProxyService = getBean(GameRPCToCSAControlProxyService.class);
            gameRPCToCSAControlProxyService.init();

            // csa control to game
            CSAControlRPCToGameProxyService csaControlRPCToGameProxyService = getBean(CSAControlRPCToGameProxyService.class);
            csaControlRPCToGameProxyService.init();
        }

        logger.info("initRPCServices end costTime:{}ms", System.currentTimeMillis()-startTime);
    }

    public static void close() {
        AppCleaner.ins().close();

        try {
            NettyTraffic.stopAndWait();
        } catch (InterruptedException e) {
            ErrorLogUtil.exceptionLog(e);
        }
    }

    public static <T> Map<String, T> getBeansOfType(Class<T> t) {
        return applicationContext.getBeansOfType(t, false, true);
    }

    public static long getServerOpenTime() {
        if (configCenter instanceof ConfigCenterForCompileCheck) {
            return 0;
        }
        if (configCenter == null) {
            ErrorLogUtil.errorLog("服务器还没加载完zookeeper的配置,configCenter == null", new RuntimeException());
            return 0;
        }
        GameServerConfig currentGameServerConfig = configCenter.getCurrentGameServerConfig();
        if (currentGameServerConfig == null) {
            ErrorLogUtil.errorLog("服务器还没加载完zookeeper的配置", new RuntimeException());
            return 0;
        }
        return currentGameServerConfig.getOpenTimeMs();
    }
    /**
     * 获取产品名称
     */
    public static String getName() {
        ApplicationConfig applicationConfig = ServerConfigManager.getInstance().getApplicationConfig();
        return applicationConfig != null ? applicationConfig.getName() : "warz";
    }

    /**
     * 获取集群ID
     */
    public static String getClusterId() {
        ApplicationConfig applicationConfig = ServerConfigManager.getInstance().getApplicationConfig();
        return applicationConfig != null ? applicationConfig.getClusterId() : "20638-warz-local";
    }

    public static int getServerId() {
        //这里的两处报错不使用错误日志打点,错误日志内部获取serverId会造成循环报错造成栈溢出
        if (configCenter instanceof ConfigCenterForCompileCheck) {
            return 0;
        }
        if (configCenter == null) {
            logger.error("服务器还没加载完zookeeper的配置configCenter == null ", new RuntimeException());
            return 0;
        }
        GameServerConfig currentGameServerConfig = configCenter.getCurrentGameServerConfig();
        if (currentGameServerConfig == null) {
            logger.error("服务器还没加载完zookeeper的配置", new RuntimeException());
            return 0;
        }
        return currentGameServerConfig.getGameServerId();
    }

    /**
     * 获取服务器Id,如果获取不到返回0,不报错版本
     * todo 后续优化合getServerId统一重构
     * @return
     */
    public static int getServerIdWithOutError() {
        //这里的两处报错不使用错误日志打点,错误日志内部获取serverId会造成循环报错造成栈溢出
        if (configCenter instanceof ConfigCenterForCompileCheck) {
            return 0;
        }
        if (configCenter == null) {
            return 0;
        }
        GameServerConfig currentGameServerConfig = configCenter.getCurrentGameServerConfig();
        if (currentGameServerConfig == null) {
            return 0;
        }
        return currentGameServerConfig.getGameServerId();
    }

    public static ServerType getServerType() {
        return configCenter.getCurrentGameServerType();
    }

    public static boolean isBattleServer() {
        return configCenter.getCurrentGameServerType() == ServerType.GVG_BATTLE || configCenter.getCurrentGameServerType() == ServerType.TVT_BATTLE;
    }

    public static boolean isGVGBattleServer() {
        return configCenter.getCurrentGameServerType() == ServerType.GVG_BATTLE;
    }

    public static boolean isGVGServer() {
        return configCenter.getCurrentGameServerType() == ServerType.GVG_BATTLE || configCenter.getCurrentGameServerType() == ServerType.GVG_CONTROL;
    }

    public static ServerType getServerType(int serverId) {
        return configCenter.getServerType(serverId);
    }

    public static int getSeason() {
        return configCenter.getSeason();
    }


    public static void launch() {
        configCenter.launch();
        // FlywayMultiHolder 初始化要在
        // configCenter初始化之后，因为currentKvkSeasonServerGroupConfig需要在ConfigCenter初始化
        getBean(FlywayMultiHolder.class).init(configCenter.getCurrentKvkSeasonServerGroupConfig(), configCenter.getCurrentGameServerConfig());
    }

    /**
     * 获得KVK服分组所有的游戏服id
     */
    public static List<Integer> getKVKGroupServerIds() {
        List<Integer> gameServerIds = new ArrayList<>();
        ServerType serverType = getServerType();
        if (serverType == ServerType.KVK_SEASON) {
            KvkSeasonServerGroupConfig serverGroupByKServerId = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(getServerId());
            gameServerIds.addAll(serverGroupByKServerId.getOServerIds());
        }
        return gameServerIds;
    }

    /**
     * 获得进程内所有serverId，包含K服及原服的id
     */
    public static List<Integer> getAllServerIds() {
        List<Integer> ret = new ArrayList<Integer>();
        ret.add(getServerId());
        ret.addAll(getKVKGroupServerIds());
        return ret;
    }

    private static String kvkGroupServerIdsStr;

    /**
     * season_serverId1_serverId2_...
     */
    public static String getKVKGroupServerIdsStr() {
        if (kvkGroupServerIdsStr == null) {
            List<Integer> kvkGroupServerIds = getKVKGroupServerIds();
            if (JavaUtils.bool(kvkGroupServerIds)) {
                KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getCurrentKvkSeasonServerGroupConfig();
                kvkGroupServerIds.sort((s1, s2) -> s1 - s2);
                StringJoiner stringJoiner = new StringJoiner("_");
                stringJoiner.add(String.valueOf(kvkSeasonServerGroupConfig.getSeason()));
                for (Integer serverId : kvkGroupServerIds) {
                    stringJoiner.add(String.valueOf(serverId));
                }
                kvkGroupServerIdsStr = stringJoiner.toString();
            } else {
                kvkGroupServerIdsStr = String.valueOf(Application.getServerId());
            }
        }
        return kvkGroupServerIdsStr;
    }

    /**
     * 抛出一个事件给注册了@EventListener的方法使用
     *
     * @param event
     */
    public static void publish(Object event) {
        applicationContext.publishEvent(event);
    }

    public static void initServerMessageService() {
//        TsRemoteGameServiceImpl tsMsgService = Application.getBean(TsRemoteGameServiceImpl.class);
//        tsMsgService.initServer();
//        GameServiceRemoteTsImpl gameToTs = Application.getBean(GameServiceRemoteTsImpl.class);
//        gameToTs.initClient();
    }

    public static boolean isDev(){
        return ServerConfigManager.isDev();
    }

    public static boolean isProd(){
        return ServerConfigManager.isProd();
    }
}
