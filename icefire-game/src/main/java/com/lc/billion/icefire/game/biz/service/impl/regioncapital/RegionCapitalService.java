package com.lc.billion.icefire.game.biz.service.impl.regioncapital;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.firebase.database.utilities.Pair;
import com.lc.billion.icefire.core.common.MathUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.model.MetaServerType;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.BizConstants;
import com.lc.billion.icefire.game.biz.async.regioncapital.RegionCapitalHotConflictOperation;
import com.lc.billion.icefire.game.biz.async.regioncapital.RegionCapitalWarPlanCheckWordOperation;
import com.lc.billion.icefire.game.biz.battle.*;
import com.lc.billion.icefire.game.biz.battle.calculator.FightUnitPropCalculate;
import com.lc.billion.icefire.game.biz.battle.result.FightLostInfo;
import com.lc.billion.icefire.game.biz.battle.result.FightLostType;
import com.lc.billion.icefire.game.biz.config.*;
import com.lc.billion.icefire.game.biz.dao.AbstractMemoryDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleRegionCapitalDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RegionCapitalNodeDao;
import com.lc.billion.icefire.game.biz.manager.*;
import com.lc.billion.icefire.game.biz.manager.activity.RoyalActivityManager;
import com.lc.billion.icefire.game.biz.manager.alliance.AllianceCalcPropManager;
import com.lc.billion.icefire.game.biz.manager.alliance.AllianceCurrencyManager;
import com.lc.billion.icefire.game.biz.manager.gvg.GVGGameDataVoManager;
import com.lc.billion.icefire.game.biz.manager.prop.base.ICalcProp;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceLeader;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.alliance.affair.AllianceAffairType;
import com.lc.billion.icefire.game.biz.model.alliance.log.AllianceLogType;
import com.lc.billion.icefire.game.biz.model.alliance.rank.Auth;
import com.lc.billion.icefire.game.biz.model.army.*;
import com.lc.billion.icefire.game.biz.model.buff.BuffSourceType;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.email.AbstractEmail;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.email.MailUtils;
import com.lc.billion.icefire.game.biz.model.email.RoleSimpleInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconArmyInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconRoleInfo;
import com.lc.billion.icefire.game.biz.model.hero.Hero;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.milestone.MilestoneType;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.regioncapital.RegionCapitalLog;
import com.lc.billion.icefire.game.biz.model.regioncapital.RegionCapitalRecord;
import com.lc.billion.icefire.game.biz.model.regioncapital.RegionCapitalRecordType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.role.RoleInfoBitMapEnum;
import com.lc.billion.icefire.game.biz.model.role.info.DefaultRoleInfo;
import com.lc.billion.icefire.game.biz.model.scene.MapData;
import com.lc.billion.icefire.game.biz.model.scene.MapRegion;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.game.biz.model.scene.node.SiegeEnginesNode;
import com.lc.billion.icefire.game.biz.service.impl.DaoService;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.activity.handler.regionCapital.RegionCapitalHandler;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceLogServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.affair.AllianceAffairServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.alliancebuilding.AllianceBuildingServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.war.AllianceWarService;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.email.MailCreator;
import com.lc.billion.icefire.game.biz.service.impl.email.MailSender;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionSwitchService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroOutput;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.milestone.MilestoneService;
import com.lc.billion.icefire.game.biz.service.impl.officials.OfficialsService;
import com.lc.billion.icefire.game.biz.service.impl.player.PlayerServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.siegeengines.SiegeEnginesServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.soldier.SoldierUpdateReasonType;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.kvkseason.biz.service.impl.honor.HonorType;
import com.lc.billion.icefire.kvkseason.biz.service.impl.honor.KvkHonorService;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kvk.KvkSeasonServiceImpl;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.*;
import com.lc.billion.icefire.protocol.structure.*;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

@Service
public class RegionCapitalService {
    private static final Logger logger = LoggerFactory.getLogger(RegionCapitalService.class);

    @Autowired
    private RegionCapitalNodeDao regionCapitalNodeDao;
    @Autowired
    private ServiceDependency srvDep;
    @Autowired
    private ArmyManager armyManager;
    @Autowired
    private AllianceManager allianceManager;
    @Autowired
    private AllianceDao allianceDao;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private ArmyDao armyDao;
    @Autowired
    private AllianceMemberManager allianceMemberManager;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private AllianceCalcPropManager allianceCalcPropManager;
    @Autowired
    private RoleCalcPropManager roleCalcPropManager;
    @Autowired
    private SceneServiceImpl sceneService;
    @Autowired
    private DropServiceImpl dropService;
    @Autowired
    private MailSender mailSender;
    @Autowired
    private MailCreator mailCreator;
    @Autowired
    private MilestoneService milestoneService;
    @Autowired
    private AllianceServiceImpl allianceService;
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private BiLogUtil biLogUtil;
    @Autowired
    private FunctionSwitchService functionSwitchService;
    @Autowired
    private AllianceAffairServiceImpl allianceAffairService;
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private KvkHonorService kvkHonorService;
    @Autowired
    private RoleRegionCapitalDao roleRegionCapitalDao;
    @Autowired
    private RegionCapitalHandler regionCapitalHandler;
    @Autowired
    private RegionCapitalChatExport regionCapitalChatExport;
    @Autowired
    private PlayerServiceImpl playerService;
    @Autowired
    private DaoService daoService;
    @Autowired
    private RoleCityManager roleCityManager;
    @Autowired
    private WorldServiceImpl worldService;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private OfficialsService officialsService;
    @Autowired
    private HeroServiceImpl heroService;
    @Autowired
    private AllianceWarService allianceWarService;
    @Autowired
    private AllianceLogServiceImpl allianceLogService;
    @Autowired
    private ArmyServiceImpl armyService;
    @Autowired
    private RegionCapitalRecordServiceImpl regionCapitalRecordService;
    @Autowired
    private BuffManager buffManager;
    @Autowired
    private GVGGameDataVoManager gvgGameDataVoManager;
    @Autowired
    private RoyalActivityManager royalActivityManager;
    @Autowired
    private AllianceCurrencyManager allianceCurrencyManager;
    @Autowired
    private AllianceBuildingServiceImpl allianceBuildingService;
    @Autowired
    private KvkSeasonServiceImpl kvkSeasonService;
    @Autowired
    private SiegeEnginesServiceImpl siegeEnginesServiceImpl;
    @Autowired
    private SeasonWarmUpActivityService seasonWarmUpActivityServiceImpl;

    // 王城映射 serverId - node
    private final Map<Integer, RegionCapitalNode> royalNodes = new ConcurrentHashMap<>();
    // 炮台映射 serverId - long - node
    private final Map<Integer, Map<Long, RegionCapitalNode>> artilleryNodes = new ConcurrentHashMap<>();
    // 区域映射州府 不包括炮台 serverId - regionId - node
    private final Map<Integer, Map<Integer, RegionCapitalNode>> regionToNode = new ConcurrentHashMap<>();

    // 各个阶段开启时间
    private final List<PsRegionCapitalStage> stageOpenTime = new ArrayList<>();
    // tick时记录全服广播节点
    private final Map<Long, RegionCapitalNode> tickSimpleNodes = new HashMap<>();

    // 热点上次刷新时间
    private long lastHotConflictTime = 0;
    // 热点信息
    private final List<PsRegionCapitalHotConflict> hotConflictList = new CopyOnWriteArrayList<>();

    // 获得王城
    public RegionCapitalNode getRoyalNode(int serverId) {
        return royalNodes.get(serverId);
    }

    // 获得炮台
    public Map<Long, RegionCapitalNode> getArtilleryNodes(int serverId) {
        Map<Long, RegionCapitalNode> ret = artilleryNodes.get(serverId);
        return null == ret ? Collections.emptyMap() : ret;
    }

    public void startService() {
        if (!configCenter.currentServerTypeIsGAME_or_KVKSEASON()) {
            return;
        }
        var nodes = regionCapitalNodeDao.findAll();
        for (var node : nodes) {
            if (node.getBelongAllianceId() <= 0) {
                continue;
            }

            // 看下联盟是否被解散了
            var alliance = allianceDao.findById(node.getBelongAllianceId());
            if (null != alliance) {
                continue;
            }

            node.setBelongAllianceId(0);
            regionCapitalNodeDao.save(node);
        }

        // 计算各个阶段开启时间
        calcStageOpenTime();

        // 检测时间变化
        checkOpenTime();

        // 检测黑土地玩家
        checkBlackCity();
    }

    // 检测黑土地玩家
    public void checkBlackCity() {
        var rcp = configService.getConfig(RegionCapitalPositionConfig.class);
        var nodes = regionCapitalNodeDao.findAll();
        for (var node : nodes) {
            var meta = rcp.get(node.getMetaId());
            if (null == meta) {
                continue;
            }

            var points = meta.getSizePoints();
            for (var point : points) {
                var city = sceneService.getSceneNode(node.getCurrentServerId(), point);
                if (!(city instanceof RoleCity roleCity)) {
                    continue;
                }

                // 玩家坐标重复，重新随机一个点
                roleCity.setPosition(null);
                roleCityManager.save(roleCity);
                ErrorLogUtil.errorLog("玩家坐标重复，重新分配", "player", roleCity.getPersistKey());
            }
        }
    }

    // 计算各个阶段倒计时
    public void calcStageOpenTime() {
        int serverId = Application.getServerId();
        if (ServerType.KVK_CONTROL == Application.getServerType()) {
            return;
        }

        var nodes = regionCapitalNodeDao.findByCurrentServerId(serverId);
        Map<Integer, Pair<RegionCapitalNode, Long>> stageEndTime = new TreeMap<>();
        TreeSet<Integer> stageIds = new TreeSet<>();
        for (var node : nodes) {
            if (node.getType() == PsRegionCapitalType.ARTILLERY) {
                continue;
            }

            // 没有进入赛季按原服判断/进入赛季按赛季服编年史判断
            long chapterEndTime = milestoneService.getOpenCityToChapterEndTime(node.getLevel());
            var oldStartTime = stageEndTime.get(node.getLevel());
            if (null == oldStartTime) {
                // 没有则直接添加
                stageEndTime.put(node.getLevel(), new Pair<>(node, chapterEndTime));
            } else {
                // 发现了更小的开始时间
                if (oldStartTime.getSecond() > chapterEndTime) {
                    stageEndTime.put(node.getLevel(), new Pair<>(node, chapterEndTime));
                }
            }
            // 共有多少阶段
            stageIds.add(node.getLevel());
        }

        var rpc = configService.getConfig(RegionCapitalPositionConfig.class);
        for (var stageId : stageIds) {
            var item = stageEndTime.get(stageId);
            var meta = rpc.get(item.getFirst().getMetaId());
            if (null == meta) {
                continue;
            }
            // 1开放倒计时
            long endTime = item.getSecond() + meta.getMilestoneUnlock();
            stageOpenTime.add(new PsRegionCapitalStage(stageId, 1, endTime));
            // 2争夺倒计时
            var nextItem = stageEndTime.get(stageId + 1);
            if (null != nextItem) {
                endTime = nextItem.getSecond() - TimeUtil.DAY_MILLIS;
                stageOpenTime.add(new PsRegionCapitalStage(stageId, 2, endTime));
            }
        }

        // 里程碑结束时间
        long milestoneEndTime = milestoneService.getEndTime();
        // 最后一阶段的争夺倒计时
        if (!stageIds.isEmpty()) {
            stageOpenTime.add(new PsRegionCapitalStage(stageIds.last(), 2, milestoneEndTime));
        }
    }

    // 玩家登陆
    public void onEnterWorld(Role role) {
        // 下发配置信息
        sendConfig(role);

        // 下发州府各阶段信息
        sendRegionCapitalStage(role);
        // 下发所有州府数据
        sendRegioncapitalList(role);
        // 下发报名信息
        role.send(wrapApplyList(role.getAllianceId()));

        var roleRegCap = roleRegionCapitalDao.findById(role.getRoleId());
        if (null == roleRegCap) {
            roleRegionCapitalDao.create(role);
        }
        tipsReward(role, null);
        sendHotConflict(role);
    }

    // 开发环境下发配置信息
    public void sendConfig(Role role) {
        if (null == role) {
            return;
        }

        if (!ServerConfigManager.isDev()) {
            return;
        }

        GcRegionConfig msg = new GcRegionConfig();
        RegionCapitalConfig rcc = configService.getConfig(RegionCapitalConfig.class);
        msg.setPvpStartTime(Arrays.stream(rcc.getPvpStartTime()).boxed().toList());
        msg.setPvpStartTimeOversea(Arrays.stream(rcc.getPvpStartTimeOversea()).boxed().toList());
        rcc.getDeclarePVPTimes().forEach(o -> msg.addToDeclarePVPTimes(Arrays.stream(o).boxed().toList()));
        rcc.getDeclarePVPTimeOverseas().forEach(o -> msg.addToDeclarePVPTimeOverseas(Arrays.stream(o).boxed().toList()));
        msg.setKingCityOpen(Arrays.stream(rcc.getKingCityOpen()).boxed().toList());
        msg.setKingCityOpenOversea(Arrays.stream(rcc.getKingCityOpenOversea()).boxed().toList());
        var config = configService.getConfig(RegionCapitalPositionConfig.class);
        for (var entry : config.getMetaMap().entrySet()) {
            msg.putToAllianceLimit(entry.getKey(), entry.getValue().getAllianceLimit());
        }
        role.send(msg);
    }

    // 初始化州府战节点
    public void initRegionCapital(int serverId, boolean isOriginal) {
        var rcpConfig = configService.getConfig(RegionCapitalPositionConfig.class);
        // 删除表里没有的id
        var nodes = regionCapitalNodeDao.findByCurrentServerId(serverId);
        for (var node : nodes) {
            if (null == rcpConfig.get(node.getMetaId())) {
                regionCapitalNodeDao.delete(node);
                ErrorLogUtil.errorLog("initRegionCapital server node meta is not find", "serverId", serverId, "node", node.getPersistKey(), "meta", node.getMetaId());
            }
        }

        // 所有等级的开始时间
        var metas = rcpConfig.getMetaByServer(isOriginal ? MetaServerType.BASE : MetaServerType.KVK);
        if (metas == null) {
            logger.warn("州府战信息没有配置 serverId={}", serverId);
            return;
        }

        long now = TimeUtil.getNow();
        for (var meta : metas) {
            var node = regionCapitalNodeDao.getNodeByMetaId(serverId, meta.getId());
            if (node != null) {
                if (!(node.getPosition().equals(meta.getPoint()))) {
                    node.setPosition(meta.getPoint());
                    regionCapitalNodeDao.save(node);
                    logger.info("sId={} modify position RegionCapitalNode {}-{}~", serverId, node.getPersistKey(), node.getMetaId());
                }
            } else {
                node = regionCapitalNodeDao.create(serverId, meta.getId(), meta.getPoint());
                logger.info("sId={} first init RegionCapitalNode {}-{}~", serverId, node.getPersistKey(), node.getMetaId());
            }

            node.setCitySize(meta.getCitySize());
            node.setLevel(meta.getLevel());
            node.setType(meta.getType());

            // 添加王城映射
            if (node.getType() == PsRegionCapitalType.ROYAL) {
                royalNodes.put(node.getoServerId(), node);
            }

            // 添加炮台映射
            if (node.getType() == PsRegionCapitalType.ARTILLERY) {
                artilleryNodes.computeIfAbsent(node.getoServerId(), k -> new ConcurrentHashMap<>()).put(node.getPersistKey(), node);
            }

            // 未开启过,可以重置时间
            if (node.getOccupyCount() <= 0) {
                // 根据编年史判断
                long startTime = milestoneService.getOpenCityToChapterEndTime(meta.getLevel());
                // 没找到,则永不开启
                if (startTime <= 0) startTime = now + TimeUtil.DAY_MILLIS * 3650;
                if (node.getStartTime() != startTime) {
                    if (PsRegionCapitalState.CLOSE == node.getStatus() || PsRegionCapitalState.NORMAL == node.getStatus()) {
                        // 设置结束时间
                        node.setStartTime(startTime);
                        node.setEndTime(startTime + meta.getMilestoneUnlock());
                        // 王城设置为保护
                        if (PsRegionCapitalType.ROYAL == meta.getType()) {
                            node.setStatus(PsRegionCapitalState.PROTECTED);
                        } else {
                            node.setStatus(PsRegionCapitalState.NORMAL);
                        }
                    }
                }
            }

            // 设置障碍点
            worldService.setBlock(serverId, meta.getSizePoints());
            // 中心点
            Point cp = meta.getCenterPoint();
            // 左下
            Point ldp = Point.getInstance(cp.getX() - meta.getCastleRange(), cp.getY() - meta.getCastleRange());
            // 右上
            Point rup = Point.getInstance(cp.getX() + meta.getCastleRange(), cp.getY() + meta.getCastleRange());
            List<Point> handleRegionIdList = new ArrayList<>();
            for (int x = ldp.getX(); x <= rup.getX(); x++) {
                for (int y = ldp.getY(); y <= rup.getY(); y++) {
                    handleRegionIdList.add(Point.getInstance(x, y));
                }
            }
            handleRegionIdList.addAll(meta.getSizePoints());
            handleBlock(serverId, handleRegionIdList);
            worldService.setBlack(serverId, ldp, rup, meta.getSizePoints(), meta.getRefreshLimit());
            // 城池中心点在阻挡区域

            node.initWatcher();
            if (this.sceneService.getSceneNode(serverId, node.getPosition()) == null) {
                this.sceneService.add(node, true);
            }

            // 添加节点区域映射
            if (node.getType() != PsRegionCapitalType.ARTILLERY) {
                regionToNode.computeIfAbsent(node.getoServerId(), k -> new ConcurrentHashMap<>()).put(node.getRegionId(), node);
            }
        }
    }

    // 检测城池开放时间
    public void checkOpenTime() {
        long now = TimeUtil.getNow();
        var rccConfig = configService.getConfig(RegionCapitalConfig.class);
        List<Integer> allServerIds = Application.getAllServerIds();
        for (var serverId : allServerIds) {
            ServerType serverType = configCenter.getServerType(serverId);
            if (ServerType.GAME != serverType) {
                continue;
            }

            var nodes = regionCapitalNodeDao.findByCurrentServerId(serverId);
            for (var node : nodes) {
                // 已经开始PVP的不判断
                if (PsRegionCapitalState.NORMAL != node.getStatus()) {
                    continue;
                }

                // 王城计算
                if (PsRegionCapitalType.ROYAL == node.getType()) {
                    int[] openTime = rccConfig.getKingCityOpenTime();
                    long endTime = TimeUtil.getCurWeekDay(now, openTime[0]);
                    if (now < node.getStartTime()) {
                        // 未开启
                        endTime = TimeUtil.getCurWeekDay(node.getStartTime(), openTime[0]);
                    }
                    endTime += openTime[1] * TimeUtil.HOUR_MILLIS;
                    node.setEndTime(endTime);
                    node.setStatus(PsRegionCapitalState.PROTECTED);
                } else {
                    var endStatus = rccConfig.findEndTime(now);
                    if (now < node.getStartTime()) {
                        // 未开启
                        endStatus = rccConfig.findEndTime(node.getStartTime());
                    }

                    // 结束时间没有变化
                    if (endStatus.getSecond() == node.getEndTime()) {
                        continue;
                    }

                    // 设置下一状态结束时间
                    node.setEndTime(endStatus.getSecond());
                    // 设置下一状态
                    if (endStatus.getFirst() == 0) {
                        node.setStatus(PsRegionCapitalState.NORMAL);
                    } else {
                        node.setStatus(PsRegionCapitalState.PVP);
                    }
                }

                regionCapitalNodeDao.save(node);
                logger.info("checkOpenTime serverId:{} node:{} endTime:{} status:{}", serverId, node.getMetaId(), node.getEndTime(), node.getStatus());
            }
        }
    }

    // 修复州府战状态
    public void fixRegionCapitalNodeStage(int serverId) {
//        var rcpConfig = configService.getConfig(RegionCapitalPositionConfig.class);
//        // 所有等级的开始时间
//        var allStartTime = milestoneService.getAllCityOpenTime(serverId);
//        var nodes = regionCapitalNodeDao.findByCurrentServerId(serverId);
//        for (var node : nodes) {
////            if (node.getBelongAllianceId() > 0) {
////                // 分配奖励重新赋值
////                Alliance alliance = allianceDao.findById(node.getBelongAllianceId());
////                if (null != alliance && null != node.getAllocationReward()) {
////                    alliance.getRegionCapitalReward().put(node.getMetaId(), new HashMap<>(node.getAllocationReward()));
////                    logger.info("fixRegionCapitalNodeStage allocation reward node:{} alliance:{} reward:{}",
////                            node.getMetaId(), alliance.getId(), node.getAllocationReward());
////                    allianceDao.save(alliance);
////                }
////                node.setAllocationReward(null);
////            }
//            var meta = rcpConfig.get(node.getMetaId());
//            // 根据编年史判断
//            Long startTime = allStartTime.get(meta.getLevel());
//            // 没找到,则永不开启
//            if (null == startTime) startTime = Long.MAX_VALUE;
//            if (node.getStartTime() != startTime) {
//                // 设置结束时间
//                node.setStartTime(startTime);
//                node.setEndTime(startTime + meta.getMilestoneUnlock());
//                node.setStatus(PsRegionCapitalState.NORMAL);
//                logger.info("fixRegionCapitalNodeStage serverId:{} node:{} status:{} startTime:{} endTime:{}",
//                        serverId, node.getMetaId(), node.getStatus(), node.getStartTime(), node.getEndTime());
//            }
//
//            node.getPveScore().clear();
//            // 重置怪物信息
//            node.setBossDeadCount(0);
//            node.setMonsterDeadCount(0);
//            node.setMonsterResetTime(0);
//            regionCapitalNodeDao.save(node);
//        }
    }

    // 修复重复结点
    public void fixRepeatedNode(int serverId) {
        var rcpConfig = configService.getConfig(RegionCapitalPositionConfig.class);
        var nodes = regionCapitalNodeDao.findByCurrentServerId(serverId);
        for (var node : nodes) {
            var meta = rcpConfig.get(node.getMetaId());
            // 玩家是否在州府城池上
            for (var point : meta.getSizePoints()) {
                var sceneNode = sceneService.getSceneNode(serverId, point);
                if (null == sceneNode) {
                    continue;
                }

                SceneNodeType sceneNodeType = sceneNode.getNodeType();
                if (SceneNodeType.REGION_CAPITAL == sceneNodeType) {
                    continue;
                }

                String collectionName = sceneNode.getCollectionName();
                AbstractMemoryDao<AbstractEntity> entityDao = daoService.getEntityDao(collectionName);
                switch (sceneNodeType.getSceneNodeRepeatType()) {
                    case COMMON:
                        // 野怪资源田等，删除自己
                        //有些coverNode是内存里创建的，所以entityDao可能确实会不存在，判断一下 不存在就不用删除db 只需要从场景里移除就好
                        if (entityDao != null) {
                            entityDao.delete(sceneNode);
                        }
                        sceneService.remove(sceneNode, false);
                        ErrorLogUtil.errorLog("fixRepeatedNode sceneNode is repeated", "sceneNode", sceneNode);
                        break;
                    case NONE:
                        // 不做处理的，比如城市等
                        throw new AlertException("fixRepeatedNode sceneNode is repeated,but repeatType is none", "sceneNode", sceneNode);
                    case SPECIAL_PLAYER:
                        // 玩家坐标重复，重新随机一个点
                        RoleCity playerCity = (RoleCity) sceneNode;
                        playerCity.setPosition(null);
                        roleCityManager.save(playerCity);
                        ErrorLogUtil.errorLog("fixRepeatedNode sceneNode is repeated", "sceneNode", sceneNode);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    // 重置城市的时间
    public void resetTime(int serverId) {
        // 重置州府战状态
        var nodes = regionCapitalNodeDao.findByCurrentServerId(serverId);
        if (JavaUtils.bool(nodes)) {
            var rcpConfig = configService.getConfig(RegionCapitalPositionConfig.class);
            for (var node : nodes) {
                var meta = rcpConfig.get(node.getMetaId());
                if (meta == null) {
                    ErrorLogUtil.errorLog("resetTime meta is null", "metaId", node.getMetaId());
                    continue;
                }

                // 部队返回
                removeDefenceArmies(node);
                // 放弃该州府
                regionCapitalNodeDao.changeIndex(node, 0L);
                // 清除报名信息
                regionCapitalNodeDao.applyClear(node.getPersistKey());
                // 重新初始化
                node.init();

                regionCapitalNodeDao.save(node);
            }
        }
    }

    // 作战计划
    public void warPlan(Role role, String warPlan) {
        if (StringUtils.isEmpty(warPlan)) {
            return;
        }

        if (warPlan.length() > 1000) {
            role.send(new GcPlayerInvalidChar(warPlan));
            return;
        }

        if (!functionSwitchService.isOpen(FunctionType.REGION_CAPITAL.getId(), role)) {
            logger.info("warPlan switch close roleId {} ", role.getRoleId());
            return;
        }

        var allianceId = role.getAllianceId();
        // 联盟是否存在
        var alliance = allianceManager.getAllianceById(allianceId);
        if (null == alliance) {
            ErrorLogUtil.errorLog("warPlan role not alliance", "roleId", role.getId());
            return;
        }

        // 官职是否正确
        if (!allianceManager.checkPermission(Auth.REGION_CAPITAL_APPLY, role.getId())) {
            ErrorLogUtil.errorLog("warPlan role alliance position limit", "roleId", role.getId());
            return;
        }

        // 检查敏感词
        RegionCapitalWarPlanCheckWordOperation checkWordOperation = new RegionCapitalWarPlanCheckWordOperation(role, srvDep, warPlan);
        srvDep.getAsyncOperService().execute(checkWordOperation);
    }

    public void apply(Role role, long capitalId, int pvpIndex) {
        if (!functionSwitchService.isOpen(FunctionType.REGION_CAPITAL.getId(), role)) {
            logger.info("apply switch close roleId {} ", role.getRoleId());
            return;
        }

        long now = TimeUtil.getNow();
        // 是否在报名期间
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        if (!rcc.isDeclareTime(now)) {
            logger.info("apply time is error");
            return;
        }

        var allianceId = role.getAllianceId();
        // 联盟是否存在
        var alliance = allianceManager.getAllianceById(allianceId);
        if (null == alliance) {
            ErrorLogUtil.errorLog("apply role not alliance", "roleId", role.getId());
            return;
        }

        // 官职是否正确
        if (!allianceManager.checkPermission(Auth.REGION_CAPITAL_APPLY, role.getId())) {
            ErrorLogUtil.errorLog("apply role alliance position limit", "roleId", role.getId());
            return;
        }

        // 宣战次数是否足够
        if (alliance.getRegionCapitalApplyCount() <= 0) {
            ErrorLogUtil.errorLog("apply role alliance level isn't enough", "roleId", role.getId(), "allianceLevel", alliance.getLevel());
            return;
        }

        // 州府是否存在
        var targetCapital = regionCapitalNodeDao.findById(capitalId);
        if (null == targetCapital) {
            ErrorLogUtil.errorLog("apply role capital isn't find", "roleId", role.getId(), "capitalId", capitalId);
            return;
        }

        // 王城没有报名限制
        if (targetCapital.getType() == PsRegionCapitalType.ROYAL || targetCapital.getType() == PsRegionCapitalType.ARTILLERY) {
            ErrorLogUtil.errorLog("apply royal or artillery not need apply", "roleId",
                    role.getId(), "capitalId", capitalId, "metaId", targetCapital.getMetaId());
            return;
        }

        // 宣战时需要判断城池占领数量
        AllianceLevelConfig.AllianceLevelMeta levelConfig = configService.getConfig(AllianceLevelConfig.class).getByLevel(alliance.getLevel());
        var belongNodes = regionCapitalNodeDao.findByAllianceId(allianceId);
        List<RegionCapitalNode> belongList = new ArrayList<>();
        for (var entry : belongNodes) {
            if (entry.getoServerId() == targetCapital.getoServerId()) {
                belongList.add(entry);
            }
        }
        if (belongList.size() >= levelConfig.getBelongCount()) {
            ErrorLogUtil.errorLog("apply role alliance level belong isn't enough", "roleId", role.getId(), "allianceLevel", alliance.getLevel(), "belongSize", belongList.size());
            return;
        }

        var capitalConfig = configService.getConfig(RegionCapitalPositionConfig.class);
        var targetMeta = capitalConfig.get(targetCapital.getMetaId());
        if (null == targetMeta) {
            ErrorLogUtil.errorLog("apply role capital meta isn't find", "roleId", role.getId(), "capitalId", capitalId, "meta", targetCapital.getMetaId());
            return;
        }

        // 根据编年史判断/没有进入赛季按原服判断/进入赛季按赛季服编年史判断
        long targetStarTime = milestoneService.getOpenCityToChapterEndTime(targetMeta.getLevel());
        if (now < targetStarTime) {
            ErrorLogUtil.errorLog("apply role capital meta milestone unlock", "role", role.getId(), "capital", capitalId,
                    "meta", targetMeta.getId(), "level", targetMeta.getLevel(), "startTime", targetStarTime);
            return;
        }

        // 人数是否达标
        var memberCount = allianceManager.getCurMember(alliance);
        if (targetMeta.getAllianceLimit() > memberCount) {
            ErrorLogUtil.errorLog("apply role alliance poeple limit", "roleId", role.getId(), "allianceId", allianceId, "memberCount", memberCount);
            return;
        }

        // 状态判断
//        if (targetCapital.getStatus() != PsRegionCapitalState.PVP) {
//            ErrorLogUtil.errorLog("apply isn't match", "roleId",role.getId(), "capitalId",capitalId,
//                    "metaId", targetCapital.getMetaId(), "status",targetCapital.getStatus());
//            return;
//        }

        // pvp区间是否正确 1,2,3
        if (pvpIndex < 1 || null == rcc.getDeclarePVPTime(pvpIndex)) {
            ErrorLogUtil.errorLog("apply role capital meta pvpIndex is error", "roleId", role.getId(), "capitalId", capitalId, "meta", targetMeta.getId(), "pvpIndex", pvpIndex);
            return;
        }

        // 有官渡之战活动,则进行下面的判断
        if (null != gvgGameDataVoManager.findGvgActivityVo()) {
            // 是否在屏蔽区间
            int weekOfDay = TimeUtil.getDayOfWeek(now);
            if (rcc.getBlockSetting() == weekOfDay) {
                logger.error("apply role:{} capital:{} meta:{} weekOfDay:{} is block", role.getId(), capitalId, targetMeta.getId(), weekOfDay);
                return;
            }
        }

        // 已报名列表
        var applyList = regionCapitalNodeDao.applyList(allianceId);
        if (null != applyList.get(capitalId)) {
            ErrorLogUtil.errorLog("apply role capital meta is applyed", "roleId", role.getId(), "capitalId", capitalId, "meta", targetMeta.getId());
            return;
        }

        // 同一时间段只能宣战一座城池
//        if (applyList.containsValue(pvpIndex)) {
//            ErrorLogUtil.errorLog("apply is ready", "roleId",role.getId(), "capitalId",capitalId,
//                    "metaId",targetMeta.getId(), "pvpIndex",pvpIndex, "applyed",applyList);
//            return;
//        }

        // 已占领列表
        if (!JavaUtils.bool(belongList)) {
            // 没有占领对象,先报一级的
            if (targetMeta.getLevel() > 1) {
                ErrorLogUtil.errorLog("apply role capital meta belong list is empty but type is error",
                        "role", role.getId(), "capital", capitalId, "meta", targetMeta.getId(),
                        "type", targetMeta.getType());
                return;
            }

            // 若同盟未占领任意城池，则只能宣战一座城池
            if (JavaUtils.bool(applyList)) {
                ErrorLogUtil.errorLog("apply role capital meta apply list isn't empty", "roleId", role.getId(), "capitalId", capitalId, "meta", targetMeta.getId());
                return;
            }
        } else {
            if (!isAdjacent(alliance.getId(), targetCapital)) {
                ErrorLogUtil.errorLog("apply check failed", "roleId", role.getId(), "capitalId", capitalId, "metaId", targetMeta.getId());
                return;
            }
        }

        // 累计报名次数
        reduceApplyCount(alliance);

        // 报名新的州府
        regionCapitalNodeDao.applyAdd(allianceId, capitalId, pvpIndex);
        // 联盟事务
        updateAffair(targetCapital, allianceId, pvpIndex);
        // 向本盟广播
        allianceManager.broadcast(allianceId, wrapApplyList(allianceId));

        // 发送报名成功邮件
        String pvpIndexText = rcc.declarePVPIndexMailTexts(pvpIndex);
        enrollEmail(alliance, targetMeta, targetCapital, pvpIndexText);
        // 聊天
        regionCapitalChatExport.onApply(alliance, targetCapital);
        regionCapitalChatExport.onBeApply(alliance, targetCapital);
        // 日志
        allianceLogService.updateAllianceLog(alliance, AllianceLogType.REGION_CAPITAL_DECLARE_WAR,
                List.of(role.getName(), targetMeta.getName(), String.valueOf(targetCapital.getX()), String.valueOf(targetCapital.getY()), pvpIndexText));

        // 同步信息
        noticeChange(targetCapital);

        biLogUtil.regioncapitalApply(role, targetMeta.getId(), allianceId, pvpIndex);
        logger.info("apply role:{} alliance:{} node:{} metaId:{} pvpIndex:{} is success.", role.getRoleId(),
                allianceId, targetCapital.getPersistKey(), targetCapital.getMetaId(), pvpIndex);

        // 处理联盟宣战标志
        updateAllianceInWarFlag(allianceId, targetCapital, true);

        srvDep.getAllianceMissionService().onMissionFinish(alliance, MissionType.REGION_CAPITAL_APPLY, 1);
        // 返回成功
        var msg = new GcRegionCapitalApply();
        msg.setId(capitalId);
        role.send(msg);
    }

    // 是否可以对该城池宣战/进攻
    public boolean isAdjacent(Long allianceId, RegionCapitalNode node) {
        var belongNodes = regionCapitalNodeDao.findByAllianceId(allianceId);
        List<RegionCapitalNode> belongList = new ArrayList<>();
        for (var entry : belongNodes) {
            if (entry.getoServerId() == node.getoServerId()) {
                belongList.add(entry);
            }
        }

        var capitalConfig = configService.getConfig(RegionCapitalPositionConfig.class);
        // 已占领列表
        var mapData = worldService.getWorld().getMapData(node.getCurrentServerId());
        int regionId1 = mapData.getGrid(node.getPosition()).getRegionId();
        boolean isRight = false;
        // 郡县报名
        for (var belongNode : belongList) {
            int regionId2 = mapData.getGrid(belongNode.getPosition()).getRegionId();
            // 1.占领了本区域中的城池
            if (regionId1 == regionId2) {
                isRight = true;
                break;
            }

            // 相邻
            if (mapData.isConnected(regionId1, regionId2)) {
                isRight = true;
                break;
            }
        }

        // 区域判断不正确
        if (!isRight) {
            ErrorLogUtil.errorLog("isDeclareWar region is error", "allianceId", allianceId,
                    "capitalId", node.getPersistKey(), "metaId", node.getMetaId(), "type", node.getType());
            return false;
        }

        // 占领城池最大等级
        int levelMax = 0;
        for (var belongNode : belongList) {
            var belongMeta = capitalConfig.get(belongNode.getMetaId());
            if (belongMeta.getLevel() > levelMax) {
                levelMax = belongMeta.getLevel();
            }
        }

        // 宣战等级太大
        if (PsRegionCapitalType.ARTILLERY != node.getType()) {
            if (node.getLevel() > levelMax + 1) {
                ErrorLogUtil.errorLog("isDeclareWar level is error", "allianceId", allianceId,
                        "capitalId", node.getPersistKey(), "metaId", node.getMetaId(), "type", node.getType(), "maxLevel", levelMax);
                return false;
            }
        }

        return true;
    }

    // 判断哪些州府和王城相邻
    public List<RegionCapitalNode> royalNeighbour(int serverId) {
        List<RegionCapitalNode> ret = new ArrayList<>();
        var royal = getRoyalNode(serverId);
        if (null == royal) {
            return ret;
        }

        var nodes = regionToNode.get(serverId);
        if (!JavaUtils.bool(nodes)) {
            return ret;
        }

        var mapData = worldService.getWorld().getMapData(royal.getCurrentServerId());
        MapRegion region = mapData.getMapRegion(royal.getRegionId());
        for (var regionId : region.getNeighbour()) {
            var node = nodes.get(regionId);
            if (null == node) {
                continue;
            }

            ret.add(node);
        }

        return ret;
    }

    // 宣战次数扣减
    private void reduceApplyCount(Alliance alliance) {
        int oldApplyCount = alliance.getRegionCapitalApplyCount();
        // 保证次数被减
        alliance.setRegionCapitalApplyCount(alliance.getRegionCapitalApplyCount() - 1);

        AllianceLevelConfig levelConfig = configService.getConfig(AllianceLevelConfig.class);
        AllianceLevelConfig.AllianceLevelMeta levelMeta = levelConfig.getByLevel(alliance.getLevel());
        if (null != levelMeta) {
            if (oldApplyCount >= levelMeta.getApplyCount()) {
                alliance.setRegionCapitalApplyTime(TimeUtil.getNow());
            }
        }
        allianceDao.save(alliance);
    }

    public void cancel(Role role, Long capitalId) {
        if (!functionSwitchService.isOpen(FunctionType.REGION_CAPITAL.getId(), role)) {
            logger.info("cancel switch close roleId {} ", role.getRoleId());
            return;
        }

        Long allianceId = role.getAllianceId();
        var alliance = allianceManager.getAllianceById(allianceId);
        if (null == alliance) {
            return;
        }

        // 官职是否正确
        if (!allianceManager.checkPermission(Auth.REGION_CAPITAL_APPLY, role.getId())) {
            ErrorLogUtil.errorLog("cancel Role alliance position limit", "roleId", role.getId());
            return;
        }

        // 州府是否存在
        var capital = regionCapitalNodeDao.findById(capitalId);
        if (null == capital) {
            ErrorLogUtil.errorLog("cancel Role capital isn't find", "roleId", role.getId(), "capitalId", capitalId);
            return;
        }

        // 查表
        var capitalConfig = configService.getConfig(RegionCapitalPositionConfig.class);
        var targetMeta = capitalConfig.get(capital.getMetaId());
        if (null == targetMeta) {
            ErrorLogUtil.errorLog("cancel role capital meta isn't find", "roleId", role.getId(), "capitalId", capitalId);
            return;
        }

        // 是否已经报名
        Integer pvpIndex = capital.getDeclareWarList().get(allianceId);
        if (null == pvpIndex) {
            ErrorLogUtil.errorLog("cancel Role capital war list isn't find", "roleId", role.getId(), "capitalId", capitalId, "allianceId", allianceId);
            return;
        }

        // pvp阶段不能取消
        if (capital.getStatus() == PsRegionCapitalState.PVP) {
            ErrorLogUtil.errorLog("cancel capital status is pvp", "roleId", role.getId(), "capitalId", capitalId, "allianceId", allianceId);
            return;
        }

        regionCapitalNodeDao.applyRemove(allianceId, capitalId);
        // 取消联盟事务
        cancelAffair(capital, allianceId, pvpIndex);
        // 删除已有的积分排名
        capital.removePveScore(allianceId);
        // 同步信息
        noticeChange(capital);
        // 向本盟广播
        allianceManager.broadcast(allianceId, wrapApplyList(allianceId));
        // 日志
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        String pvpIndexText = rcc.declarePVPIndexMailTexts(pvpIndex);
        allianceLogService.updateAllianceLog(alliance, AllianceLogType.REGION_CAPITAL_DECLARE_WAR_CANCEL,
                List.of(role.getName(), targetMeta.getName(), String.valueOf(capital.getX()), String.valueOf(capital.getY()), pvpIndexText));

        biLogUtil.regioncapitalApplyCancel(role, capital.getMetaId(), allianceId);
        logger.info("cancel role:{} alliance:{} node:{} metaId:{} is success.", role.getRoleId(), allianceId, capital.getPersistKey(), capital.getMetaId());

        updateAllianceInWarFlag(allianceId, capital, false);
        // 返回成功
        var msg = new GcRegionCapitalCancel();
        msg.setId(capitalId);
        role.send(msg);

        siegeEnginesServiceImpl.changeCapitalBelong(allianceId, capitalId, capital.getBelongAllianceId());
    }

    // 全服广播simple信息
    public void broadcastGovernor(String metaId, int oserverId) {
        // 先在k服查找
        RegionCapitalNode capital = regionCapitalNodeDao.getNodeByMetaId(Application.getServerId(), metaId);
        if (null == capital) {
            // 再到原服查找
            capital = regionCapitalNodeDao.getNodeByMetaId(oserverId, metaId);
            if (null == capital) {
                return;
            }
        }

        // AOI
        sceneService.update(capital, null);

        // simple
        broadcast(capital);
    }

    public void sendRegionCapitalStage(Role role) {
        GcRegionCapitalStageInfo msg = new GcRegionCapitalStageInfo();
        // 1赛季取的是原服Id,其他赛季取的是k服Id
        msg.setStages(stageOpenTime);
        role.send(msg);
    }

    public GcRegionCapitalApplyList wrapApplyList(Long allianceId) {
        GcRegionCapitalApplyList msg = new GcRegionCapitalApplyList();
        var alliance = allianceManager.getAllianceById(allianceId);
        if (null == alliance) {
            return msg;
        }

        msg.setApplyCount(alliance.getRegionCapitalApplyCount());
        msg.setApplyTime(0);
        AllianceLevelConfig levelConfig = configService.getConfig(AllianceLevelConfig.class);
        AllianceLevelConfig.AllianceLevelMeta levelMeta = levelConfig.getByLevel(alliance.getLevel());
        if (null != levelMeta) {
            int applyCount = alliance.getRegionCapitalApplyCount();
            if (applyCount < levelMeta.getApplyCount()) {
                var declareCd = configService.getConfig(RegionCapitalConfig.class).getDeclareCDs();
                msg.setApplyTime(alliance.getRegionCapitalApplyTime() + declareCd[0] * TimeUtil.HOUR_MILLIS);
            }
        }
        do {
            var applyList = regionCapitalNodeDao.applyList(allianceId);
            if (null == applyList || applyList.isEmpty()) {
                break;
            }

            for (var entry : applyList.entrySet()) {
                var capitalNode = regionCapitalNodeDao.findById(entry.getKey());
                if (null == capitalNode) {
                    continue;
                }

                PsRegionCapitalApply applyMsg = new PsRegionCapitalApply();
                applyMsg.setAllianceId(allianceId);
                applyMsg.setId(entry.getKey());
                applyMsg.setPvpIndex(entry.getValue());
                msg.addToApplyInfo(applyMsg);
            }
        } while (false);
        return msg;
    }

    public void sendRegionCapital(Role role, long capitalId) {
        // 州府是否存在
        var capital = regionCapitalNodeDao.findById(capitalId);
        if (null == capital) {
            ErrorLogUtil.errorLog("sendRegionCapital Role capital isn't find", "roleId", role.getId(), "capitalId", capitalId);
            return;
        }
        GcRegionCapitalDetailInfo msg = new GcRegionCapitalDetailInfo();
        msg.setInfo(toPsRegionCapitalInfo(capital));
        role.send(msg);
    }

    // 州府战所有城池信息
    public void sendRegioncapitalList(Role role) {
        if (Application.isBattleServer()) {
            return;
        }
        Collection<RegionCapitalNode> sNodes = regionCapitalNodeDao.findByCurrentServerId(role.getoServerId());
        Collection<RegionCapitalNode> kNodes = Application.getSeason() > 1 ? regionCapitalNodeDao.findByCurrentServerId(Application.getServerId()) : List.of();
        GcUpdateRegionCapitalSimpleInfo msg = new GcUpdateRegionCapitalSimpleInfo();
        for (var node : sNodes) {
            msg.addToSimpleInfo(toSimpleInfo(node));
        }
        for (var node : kNodes) {
            msg.addToSimpleInfo(toSimpleInfo(node));
        }
        role.send(msg);
    }

    public void giveup(Role role, Long capitalId, boolean isCancel) {
        if (!functionSwitchService.isOpen(FunctionType.REGION_CAPITAL.getId(), role)) {
            logger.info("giveup switch close roleId {} ", role.getRoleId());
            return;
        }

        var allianceId = role.getAllianceId();
        // 联盟是否存在
        var alliance = allianceManager.getAllianceById(allianceId);
        if (null == alliance) {
            ErrorLogUtil.errorLog("giveup role not alliance", "roleId", role.getId());
            return;
        }

        // 官职是否正确
        if (!allianceManager.checkPermission(Auth.REGION_CAPITAL_APPLY, role.getId())) {
            ErrorLogUtil.errorLog("giveup role alliance position limit", "roleId", role.getId());
            return;
        }

        // 州府是否存在
        var capital = regionCapitalNodeDao.findById(capitalId);
        if (null == capital) {
            ErrorLogUtil.errorLog("giveup role capital isn't find", "roleId", role.getId(), "capitalId", capitalId);
            return;
        }

        var rcpc = configService.getConfig(RegionCapitalPositionConfig.class);
        var meta = rcpc.get(capital.getMetaId());
        if (null == meta) {
            ErrorLogUtil.errorLog("giveup role capital meta isn't find", "roleId", role.getId(), "capitalId", capitalId, "meta", capital.getMetaId());
            return;
        }

        // 王城不能放弃
        if (PsRegionCapitalType.ROYAL == capital.getType()) {
            ErrorLogUtil.errorLog("giveup isn't giveup royal", "roleId", role.getId(), "capitalId", capitalId, "metaId", capital.getMetaId());
            return;
        }

        // 是否属于自己的联盟
        if (!Objects.equals(capital.getBelongAllianceId(), allianceId)) {
            ErrorLogUtil.errorLog("giveup role capital meta alliance isn't own", "role", role.getId(),
                    "capital", capitalId, "meta", capital.getMetaId(), "belongAllianceId", capital.getBelongAllianceId(),
                    "allianceId", allianceId);
            return;
        }

        // 是否取消放弃
        if (isCancel) {
            if (capital.getGiveupEndTime() <= 0) {
                ErrorLogUtil.errorLog("giveup role capital meta isn't giveup", "roleId", role.getId(), "capitalId", capitalId, "meta", capital.getMetaId());
                return;
            }

            // 取消放弃
            capital.setGiveupEndTime(0);
            allianceLogService.updateAllianceLog(alliance, AllianceLogType.REGION_CAPITAL_GIVEUP_CANCEL,
                    List.of(role.getName(), meta.getName(), String.valueOf(capital.getX()), String.valueOf(capital.getY())));
        } else {
            if (capital.getGiveupEndTime() > 0) {
                ErrorLogUtil.errorLog("giveup role capital meta is giveup", "roleId", role.getId(), "capitalId", capitalId, "meta", capital.getMetaId());
                return;
            }

            // 放弃倒计时
            var rcc = configService.getConfig(RegionCapitalConfig.class);
            capital.setGiveupEndTime(TimeUtil.getNow() + rcc.getGiveupDuration());
            allianceLogService.updateAllianceLog(alliance, AllianceLogType.REGION_CAPITAL_GIVEUP,
                    List.of(role.getName(), meta.getName(), String.valueOf(capital.getX()), String.valueOf(capital.getY())));
        }

        // aio
        sceneService.update(capital, null);
        noticeAlliance(capital, allianceId);

        regionCapitalNodeDao.save(capital);
        biLogUtil.regioncapitalGiveup(role, capital.getMetaId(), allianceId, isCancel);
        logger.info("giveup role:{} alliance:{} node:{} metaId:{} isCancel:{} is success.",
                role.getRoleId(), allianceId, capital.getPersistKey(), capital.getMetaId(), isCancel);
    }

    // 每个城池每人只能发起一次集结
    public boolean rallyCheck(Role role, RegionCapitalNode node) {
        var armys = sceneService.getNodeArmies(node);
        for (var entry : armys) {
            if (!role.getRoleId().equals(entry.getRoleId())) {
                continue;
            }

            var army = armyDao.findById(entry.getPersistKey());
            if (null == army) {
                continue;
            }

            // 是否是集结进攻
            var armyType = army.getArmyType();
            if (ArmyType.RALLY_REGION_CAPITAL == armyType || ArmyType.JOIN == armyType) {
                return true;
            }
        }

        return false;
    }

    public List<PsRegionCapitalArmyInfo> stationArmys(RegionCapitalNode capitalNode) {
        List<PsRegionCapitalArmyInfo> armyList = new ArrayList<>();
        if (capitalNode.getBelongAllianceId() <= 0) {
            return armyList;
        }
        var armys = sceneService.getNodeArriveArmies(capitalNode);
        if (!JavaUtils.bool(armys)) {
            return armyList;
        }

        // 先组装leader
        PsRegionCapitalArmyInfo leaderInfo = null;
        for (var army : armys) {
            PsRegionCapitalArmyInfo psArmy = new PsRegionCapitalArmyInfo();
            PsStationArmyUnit armyUnit = new PsStationArmyUnit();

            long roleId = army.getRoleId();
            Role role = roleManager.getRole(roleId);
            RoleSimpleInfo roleSimpleInfo = new RoleSimpleInfo(role, null);
            armyUnit.setRoleInfo(roleSimpleInfo.toPsSimpleInfo());

            psArmy.setStartTime(army.getStartWorkTime());
            psArmy.setTotalTime(army.getTotalWorkTime());
            armyUnit.setArmyInfo(new PsArmySimpleInfo());
            armyUnit.getArmyInfo().setSoldiers(army.getSoldierMap());
            armyUnit.getArmyInfo().setHeroInfo(HeroOutput.toHeroSimpleList(role.getRoleId(), army.getHeros()));
            psArmy.setArrived(true);
//            psArmy.setArriveTime(army.getJoinTime());
            psArmy.setArmyId(army.getPersistKey());
            psArmy.setDetailInfo(armyUnit);
            armyList.add(psArmy);
            if (army.getPersistKey().equals(capitalNode.getLeaderId())) {
                leaderInfo = psArmy;
            }
        }

        if (null != leaderInfo) {
            armyList.remove(leaderInfo);
            armyList.addFirst(leaderInfo);
        }

        return armyList;
    }

    public void battleRecord(ArmyInfo armyInfo, RegionCapitalNode node) {
        Role role = armyInfo.getOwner();
        Alliance alliance = allianceDao.findById(role.getAllianceId());
        if (alliance == null) {
            ErrorLogUtil.errorLog("battleRecord is null", "roleId", role.getId(), "nodeId", node.getPersistKey(), "metaId", node.getMetaId());
            return;
        }

        // 统计战斗记录数据
        FightLostInfo fightLostInfo = armyInfo.getFightContext().getFightResult().getAttackerLostInfo();
        Long allianceId = alliance.getId();
        if (armyInfo.getRallyContext() == null) {
            Long roleId = role.getRoleId();
            regionCapitalRecordService.addRecordScore(
                    allianceId, roleId, node.getoServerId(), node.getMetaId(), RegionCapitalRecordType.KILL,
                    node.getEndTime(), fightLostInfo.getValue(roleId.toString(), FightLostType.KILL));
            regionCapitalRecordService.addRecordScore(
                    allianceId, roleId, node.getoServerId(), node.getMetaId(), RegionCapitalRecordType.BADLY,
                    node.getEndTime(), fightLostInfo.getValue(roleId.toString(), FightLostType.BADLY));
        } else {
            Set<Long> joinArmyIds = new HashSet<>(armyInfo.getRallyContext().getJoinArmyIdList());
            joinArmyIds.add(armyInfo.getPersistKey());
            for (Long joinArmyId : joinArmyIds) {
                ArmyInfo joinArmy = armyManager.findById(joinArmyId);
                if (joinArmy == null) {
                    continue;
                }
                Long roleId = joinArmy.getRoleId();
                if (roleId == null) {
                    continue;
                }
                regionCapitalRecordService.addRecordScore(
                        allianceId, roleId, node.getoServerId(), node.getMetaId(), RegionCapitalRecordType.KILL,
                        node.getEndTime(), fightLostInfo.getValue(roleId.toString(), FightLostType.KILL));
                regionCapitalRecordService.addRecordScore(
                        allianceId, roleId, node.getoServerId(), node.getMetaId(), RegionCapitalRecordType.BADLY,
                        node.getEndTime(), fightLostInfo.getValue(roleId.toString(), FightLostType.BADLY));
            }
        }

        // 防守方
        FightLostInfo defenderLostInfo = armyInfo.getFightContext().getFightResult().getDefenderLostInfo();
        var armyDefender = armyInfo.getFightContext().getDefender();
        if (defenderLostInfo != null && armyDefender.getTargetId() != null) {
            Long mainDefendRoleId = armyDefender.getTargetId();
            Role mainDefendRole = roleDao.findById(mainDefendRoleId);
            if (mainDefendRole != null) {
                Long armyAllianceId = mainDefendRole.getAllianceId();
                regionCapitalRecordService.addRecordScore(
                        armyAllianceId, mainDefendRoleId, node.getoServerId(), node.getMetaId(), RegionCapitalRecordType.KILL,
                        node.getEndTime(), defenderLostInfo.getValue(mainDefendRoleId.toString(), FightLostType.KILL));
                regionCapitalRecordService.addRecordScore(
                        armyAllianceId, mainDefendRoleId, node.getoServerId(), node.getMetaId(), RegionCapitalRecordType.BADLY,
                        node.getEndTime(), defenderLostInfo.getValue(mainDefendRoleId.toString(), FightLostType.BADLY));
                var nodeType = armyInfo.getTargetNodeType();
                if (nodeType == SceneNodeType.CITY || nodeType == SceneNodeType.STRONGHOLD || nodeType == SceneNodeType.REGION_CAPITAL) {
                    var fightTroops = armyDefender.getFightTroops();
                    for (FightTroop fightTroop : fightTroops) {
                        Long troopRoleId = fightTroop.getRoleId();
                        if (troopRoleId.equals(mainDefendRoleId)) {
                            continue;
                        }
                        regionCapitalRecordService.addRecordScore(
                                armyAllianceId, troopRoleId, node.getoServerId(), node.getMetaId(), RegionCapitalRecordType.KILL,
                                node.getEndTime(), defenderLostInfo.getValue(troopRoleId.toString(), FightLostType.KILL));
                        regionCapitalRecordService.addRecordScore(
                                armyAllianceId, troopRoleId, node.getoServerId(), node.getMetaId(), RegionCapitalRecordType.BADLY,
                                node.getEndTime(), defenderLostInfo.getValue(troopRoleId.toString(), FightLostType.BADLY));
                    }
                }
            }
        }

    }

    // PVE：攻击怪物胜利 返回怪物是否全部被消灭
    public void pveLogic(ArmyInfo armyInfo, RegionCapitalNode node) {
        // 功勋结算
        pveHonorTrigger(armyInfo);

        boolean isWin = armyInfo.getFightContext().isWin();
        if (!isWin) {
            if (node.getOccupyCount() <= 0) {
                armyManager.returnStamina(armyInfo, EmailConstants.RETURN_STAMINA_NONE);
            }
            return;
        }

        Role role = armyInfo.getOwner();
        Alliance alliance = allianceDao.findById(role.getAllianceId());
        if (alliance == null) {
            ErrorLogUtil.errorLog("pveLogic role alliance is null", "role", role.getId(), "node", node.getPersistKey(), "meta", node.getMetaId());
            return;
        }

        var rcpConfig = configService.getConfig(RegionCapitalPositionConfig.class);
        var meta = rcpConfig.get(node.getMetaId());
        if (null == meta) {
            ErrorLogUtil.errorLog("pveLogic role meta is null", "role", role.getId(), "node", node.getPersistKey(), "meta", node.getMetaId());
            return;
        }

        logger.info("pveLogic armyType:{} armyId:{} role:{} alliance:{} node:{} metaId:{} monster:{} boss:{} pveCount:{}.",
                armyInfo.getArmyType(), armyInfo.getPersistKey(), role.getRoleId(), alliance.getId(), node.getPersistKey(),
                node.getMetaId(), node.getMonsterDeadCount(), node.getBossDeadCount(), node.getContinuePveCount());
        // 此次消灭多少只怪物
        int killMonsterCount = node.getContinuePveCount();
        if (killMonsterCount <= 0) {
            killMonsterCount = 1;
        }
        // boss是否死了
        if (!node.isBossDeadAll(meta)) {
            // boss被打死
            node.setBossDeadCount(node.getBossDeadCount() + killMonsterCount);

            sendPveRewardMail(armyInfo, node, killMonsterCount);

            // 记录分数
            node.addPveScore(armyInfo, (long) meta.getNpcScore()[0] * killMonsterCount);
            // 弹幕
            if (node.isBossDeadAll(meta)) {
                // 本次战斗全部死亡
                regionCapitalChatExport.onSystemDanmu(node, RegionCapitalChatExport.BOSS_DEAD_ALL_KEY);
                logger.info("Region capital boss dead all, node={}, meta={}", node.getPersistKey(), node.getMetaId());
            }
        } else {
            if (!node.isMonsterDeadAll(meta)) {
                // 减少怪物被击杀次数
                int oldKillMonsterCount = node.getMonsterDeadCount();
                node.setMonsterDeadCount(node.getMonsterDeadCount() + killMonsterCount);

                sendPveRewardMail(armyInfo, node, killMonsterCount);

                // 记录分数
                node.addPveScore(armyInfo, (long) meta.getNpcScore()[1] * killMonsterCount);
                var regionSetting = configService.getConfig(RegionCapitalConfig.class);
                // 达到弹幕预警值
                int oldRemainCount = meta.getNpcNum(node.getBelongAllianceId() > 0) - oldKillMonsterCount;
                int newRemainCount = meta.getNpcNum(node.getBelongAllianceId() > 0) - node.getMonsterDeadCount();
                if (oldRemainCount > regionSetting.getAlertNpcNum() && newRemainCount <= regionSetting.getAlertNpcNum()) {
                    regionCapitalChatExport.onSystemDanmu(node, RegionCapitalChatExport.MONSTER_ALERT_KEY);
                    logger.info("Region capital monster alert, node={}, meta={}, alert={}, old={}, new={}",
                            node.getPersistKey(), node.getMetaId(), regionSetting.getAlertNpcNum(), oldRemainCount, newRemainCount);
                }
            }
        }

        // 怪物全部死亡,继续处理
        if (node.isDeadAll()) {
            // 占领
            Long newAllianceId = 0L;
            // 判断谁的积分高
            var scoreRank = node.getPveScore();
            if (JavaUtils.bool(scoreRank)) {
                newAllianceId = scoreRank.getFirst().getFirst();
            }
            if (PsRegionCapitalType.COUNTY == node.getType()) {
                Alliance newAlliance = allianceDao.findById(newAllianceId);
                normalFinish(node, newAlliance);
                // 全服广播
                broadcast(node);
            } else if (PsRegionCapitalType.ROYAL == node.getType()) {
                if (node.getOccupyCount() <= 0) {
                    sendBelongNotice(node.getMetaId(), newAllianceId, null, true);
                }
                // 争夺次数增加
                node.setOccupyCount(node.getOccupyCount() + 1);
                // 发放奖励
                sendBelongRewardMail(node, newAllianceId);
            }
        }

        regionCapitalNodeDao.save(node);
        // aoi通知 具体信息发生了变化
        sceneService.update(node, null);
    }

    // PVP胜利
    public void pvpLogic(ArmyInfo armyInfo, RegionCapitalNode node) {
        // 功勋结算
        pvpHonorTrigger(armyInfo);

        boolean isWin = armyInfo.getFightContext().isWin();
        if (!isWin) {
            // 向防守方同步驻防信息
            broadcastGarrisonInfo(node);
            return;
        }

        removeDefenceArmies(node);

        // 结算决战王城功勋
        royalHonorAdd(node, false, true);
    }

    // 王城/炮台战斗胜利后可能直接驻防
    public boolean ableGarrison(ArmyInfo armyInfo, RegionCapitalNode node) {
        // 炮台记录
        addArtilleryRecord(node, armyInfo, 2);

        // 战斗是否胜利
        boolean isWin = armyInfo.getFightContext().isWin();
        if (!isWin) {
            return false;
        }

        // 判断怪物是否全部死亡
        if (!node.isDeadAll()) {
            return false;
        }

        Role role = armyInfo.getOwner();
        if (null == role) {
            return false;
        }

        Alliance alliance = allianceDao.findById(role.getAllianceId());
        if (null == alliance) {
            return false;
        }

        // 直接进驻
        garrison(armyInfo, node);

        return true;
    }

    // 炮台驻军被返回
    public void checkArtilleryStartByArmyReturn(RegionCapitalNode node) {
        if (PsRegionCapitalType.ARTILLERY != node.getType()) {
            return;
        }

        // 本来就未开始
        if (node.getOccupyStartTime() <= 0) {
            return;
        }

        // 驻军
        var armies = sceneService.getNodeArriveArmies(node);
        // 驻军不为空,返回
        // 即此时依据驻防时的状态来处理
        if (JavaUtils.bool(armies)) {
            return;
        }

        // 炮台直接设置
        node.setBelongLastAllianceId(node.getBelongAllianceId());
        node.setBelongAllianceId(0);
        node.setOccupyStartTime(0);
        node.setArtilleryCount(0);
        regionCapitalNodeDao.save(node);

        // 数据下发,在外面已经做了,这里不重复发送
//        sceneService.update(node, null);

        logger.info("checkArtilleryStartByArmyReturn node:{} metaId:{} belongId:{} isStart:{}", node.getPersistKey(),
                node.getMetaId(), node.getBelongAllianceId(), node.getOccupyStartTime() > 0);
    }

    // 怪物奖励
    public void sendPveRewardMail(ArmyInfo armyInfo, RegionCapitalNode node, int count) {
        if (node.getOccupyCount() > 0) {
            return;
        }

        var meta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
        if (meta == null) {
            return;
        }

        // 参与玩家
        List<ArmyInfo> joinArmyList = new ArrayList<>();
        joinArmyList.add(armyInfo);
        RallyContext rallyCtx = armyInfo.getRallyContext();
        if (rallyCtx != null) {
            for (Long joinArmyId : rallyCtx.getJoinArmyIdList()) {
                joinArmyList.add(armyManager.findById(joinArmyId));
            }
        }

        var items = dropService.drop(meta.getParticipationReward());
        for (var item : items) {
            item.setCount(item.getCount() * count);
        }
        for (var entry : joinArmyList) {
            // 奖励和战斗邮件一起发放
            entry.addDropItem(items);
        }
    }

    // 推送州府占领信息
    public void sendBelongNotice(String metaId, Long allianceId, String roleName, boolean isFirst) {
        // 联盟广播
        GcRegionCapitalBelongNotice belongNotice = new GcRegionCapitalBelongNotice();
        belongNotice.setCapitalMetaId(metaId);
        belongNotice.setAllianceId(allianceId);
        belongNotice.setRoleName(roleName);
        belongNotice.setFirst(isFirst);
        allianceManager.broadcast(allianceId, belongNotice);
    }

    // 控制奖励 isRoyal表示王城终占
    public void sendBelongRewardMail(RegionCapitalNode node, Long allianceId) {
        // 炮台不发
        if (node.getType() == PsRegionCapitalType.ARTILLERY) {
            return;
        }

        Alliance alliance = allianceDao.findById(allianceId);
        if (null == alliance) {
            return;
        }

        // 州府第一次被占领会有奖励
        if (node.getOccupyCount() > 1) {
            return;
        }

        // 设置联盟奖励
        // 每个建筑只限一次，发放给达成首次终占的同盟成员
        List<Long> _memberList = allianceMemberManager.getMembers(alliance.getId()).stream().map(AllianceMember::getPersistKey).toList();
        List<Long> memberList = new ArrayList<>();
        for (var memberId : _memberList) {
            Role member = roleDao.findById(memberId);
            if (null == member) {
                continue;
            }
            // 进盟不足28小时的,不发奖励
            if (!canOpt(member)) {
                continue;
            }
            memberList.add(memberId);
        }
        alliance.addRegionCapitalReward(node.getMetaId(), memberList);

        var meta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
        if (meta == null) {
            return;
        }

        LogReasons.ItemLogReason reason = LogReasons.ItemLogReason.REGION_CAPITAL_OCCUPY_REWARD;
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        String name = "@" + meta.getName() + "@";
        int serverId = node.getCurrentServerId();

        var items = dropService.drop(meta.getOccupyReward());
        var members = allianceMemberManager.getMembers(alliance.getId());
        List<AbstractEmail> sendMails = new ArrayList<>();
        for (var entry : members) {
            var systemMail = mailCreator.createRegionCapitalMail(entry.getPersistKey(), serverId, rcc.getOccupyRewardMailId(), items, name);
            sendMails.add(systemMail);
        }

        if (JavaUtils.bool(sendMails)) {
            mailSender.sendBatchMail(sendMails);
        }
    }

    // 是否可以攻击洛阳/长安
    public boolean checkRoyalAttack(Role role, int serverId) {
        Long allianceId = role.getAllianceId();
        // 没有联盟
        if (null == allianceId) {
            return false;
        }

        // 王城
        RegionCapitalNode royalNode = getRoyalNode(serverId);
        if (null == royalNode) {
            return false;
        }

        // 周六发
        long now = TimeUtil.getNow();
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        int dayOfWeek = TimeUtil.getDayOfWeek(now);
        if (rcc.getKingCityOpen()[0] != dayOfWeek) {
            return false;
        }

        // 王城在当天开启
        if (!TimeUtil.isSameDay(now, royalNode.getEndTime())) {
            return false;
        }

        // 进入k服后，原服洛阳提醒要k服王城解锁才发放邮件
        if (Application.getServerType() == ServerType.KVK_SEASON && Application.getServerType(serverId) == ServerType.GAME) {
            var kroyal = getRoyalNode(Application.getServerId());
            if (!TimeUtil.isSameDay(now, kroyal.getEndTime())) {
                return false;
            }
        }

        // 城池筛选
        var belongNodes = regionCapitalNodeDao.findByAllianceId(allianceId);
        List<RegionCapitalNode> belongList = new ArrayList<>();
        for (var entry : belongNodes) {
            if (entry.getoServerId() == serverId) {
                belongList.add(entry);
            }
        }

        // 已占领列表
        MapData mapData = worldService.getWorld().getMapData(serverId);
        int royalRegion = mapData.getGrid(royalNode.getPosition()).getRegionId();
        boolean isRight = false;
        for (var belongNode : belongList) {
            int regionId = mapData.getGrid(belongNode.getPosition()).getRegionId();
            // 相邻
            if (mapData.isConnected(royalRegion, regionId)) {
                isRight = true;
                break;
            }
        }

        // 没有相邻
        if (!isRight) {
            return false;
        }

        return true;
    }

    // 王城开放提醒
    public void tickRoyalOpenMail(RegionCapitalNode node) {
        if (node.getType() != PsRegionCapitalType.ROYAL) {
            return;
        }

        // 每天查一次
        long now = TimeUtil.getNow();
        if (TimeUtil.isSameDay(node.getLastRoyalOpenTime(), now)) {
            return;
        }

        node.setLastRoyalOpenTime(now);
        regionCapitalNodeDao.save(node);

        // 周六发
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        int dayOfWeek = TimeUtil.getDayOfWeek(now);
        if (rcc.getKingCityOpen()[0] != dayOfWeek) {
            return;
        }

        var meta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
        if (meta == null) {
            return;
        }

        try {
            // 王城在当天开启
            var royal = getRoyalNode(node.getoServerId());
            if (!TimeUtil.isSameDay(now, royal.getEndTime())) {
                return;
            }

            // 也就是进入k服后，原服洛阳提醒要k服王城解锁才发放邮件
            if (Application.getServerType() == ServerType.KVK_SEASON && meta.getMetaServerType() == MetaServerType.BASE) {
                var kroyal = getRoyalNode(Application.getServerId());
                if (!TimeUtil.isSameDay(now, kroyal.getEndTime())) {
                    return;
                }
            }

            String name = "@" + meta.getName() + "@";
            int serverId = node.getCurrentServerId();
            String mailId = meta.getMetaServerType() == MetaServerType.BASE ? rcc.getBaseKingCityNoticeMailID() : rcc.getKvkKingCityNoticeMailID();
            var neighbours = royalNeighbour(node.getoServerId());
            logger.info("tickRoyalOpenMail node:{} metaId:{} neighbours:{}", node.getPersistKey(), node.getMetaId(), neighbours.size());
            Set<Long> allianceIds = new HashSet<>();
            for (var entry : neighbours) {
                if (allianceIds.contains(entry.getBelongAllianceId())) {
                    continue;
                }

                allianceIds.add(entry.getBelongAllianceId());
                logger.info("tickRoyalOpenMail neighbour node:{} metaId:{} belongAllianceId:{}", entry.getPersistKey(), entry.getMetaId(), entry.getBelongAllianceId());
                var members = allianceMemberManager.getMembers(entry.getBelongAllianceId());
                List<AbstractEmail> sendMails = new ArrayList<>();
                for (var member : members) {
                    var systemMail = mailCreator.createRegionCapitalRoyalOpenMail(member.getPersistKey(), serverId, mailId, name);
                    sendMails.add(systemMail);
                }

                if (JavaUtils.bool(sendMails)) {
                    mailSender.sendBatchMail(sendMails);
                }
            }

            // 有人能占领洛阳了
            if (!neighbours.isEmpty()) {
                srvDep.getVassalService().reportRoyalStart(serverId);
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("tickRoyalOpenMail error", e);
        }
    }

    // 占领成功邮件
    public void sendAttackSuccessMail(RegionCapitalNode node, long allianceId) {
        Alliance alliance = allianceDao.findById(allianceId);
        if (null == alliance) {
            return;
        }

        var meta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
        if (meta == null) {
            return;
        }

        var rcc = configService.getConfig(RegionCapitalConfig.class);
        String name = "@" + meta.getName() + "@";
        int serverId = node.getCurrentServerId();
        var members = allianceMemberManager.getMembers(alliance.getId());
        List<AbstractEmail> sendMails = new ArrayList<>();
        for (var entry : members) {
            var systemMail = mailCreator.createRegionCapitalAtkSuccessMail(entry.getPersistKey(), serverId, rcc.getAttackSuccessMailID(), name);
            sendMails.add(systemMail);
        }

        if (JavaUtils.bool(sendMails)) {
            mailSender.sendBatchMail(sendMails);
        }
    }

    // 被攻陷邮件
    public void sendDefenceFailedMail(RegionCapitalNode node, long oldAllianceId, String newAllianceName) {
        Alliance oldAlliance = allianceDao.findById(oldAllianceId);
        if (null == oldAlliance) {
            return;
        }

        var meta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
        if (meta == null) {
            return;
        }

        var rcc = configService.getConfig(RegionCapitalConfig.class);
        String name = "@" + meta.getName() + "@";
        int serverId = node.getCurrentServerId();
        var members = allianceMemberManager.getMembers(oldAlliance.getId());
        List<AbstractEmail> sendMails = new ArrayList<>();
        for (var entry : members) {
            var systemMail = mailCreator.createRegionCapitalDefFailedMail(entry.getPersistKey(), serverId, rcc.getDefenceFailedMailID(), newAllianceName, name);
            sendMails.add(systemMail);
        }

        if (JavaUtils.bool(sendMails)) {
            mailSender.sendBatchMail(sendMails);
        }
    }

    // 广播驻防信息给联盟成员
    public void broadcastGarrisonInfo(RegionCapitalNode capitalNode) {
        long allianceId = capitalNode.getBelongAllianceId();
        if (allianceId <= 0) {
            return;
        }

        Alliance alliance = allianceDao.findById(allianceId);
        if (alliance == null) {
            ErrorLogUtil.errorLog("broadcastDetailInfoUpdate alliance not exists", "allianceId", allianceId);
            return;
        }

        GcRegionCapitalDetailInfo msg = new GcRegionCapitalDetailInfo();
        msg.setInfo(toPsRegionCapitalInfo(capitalNode));
        allianceManager.broadcast(alliance, msg, null);
    }

    // 全服广播simple信息
    public void broadcast(RegionCapitalNode capital) {
        GcUpdateRegionCapitalSimpleInfo msg = new GcUpdateRegionCapitalSimpleInfo();
        msg.addToSimpleInfo(toSimpleInfo(capital));
        if (capital.getCurrentServerId() == Application.getServerId()) {
            // k服的城,推送所有人
            playerService.broadcast(msg);
        } else {
            // 原服的城,推送原服和k服的人
            playerService.broadcast(capital.getCurrentServerId(), Application.getServerId(), msg);
        }
    }

    // 全服广播simple信息
    public void broadcast(Collection<RegionCapitalNode> nodes) {
        if (!JavaUtils.bool(nodes)) {
            return;
        }

        // 按serverId分组
        Map<Integer, List<RegionCapitalNode>> msgMap = new HashMap<>();
        for (var node : nodes) {
            msgMap.computeIfAbsent(node.getCurrentServerId(), o -> new ArrayList<>()).add(node);
        }

        // 按分组发送
        for (var entry : msgMap.entrySet()) {
            GcUpdateRegionCapitalSimpleInfo msg = new GcUpdateRegionCapitalSimpleInfo();
            for (var node : entry.getValue()) {
                msg.addToSimpleInfo(toSimpleInfo(node));
            }
            if (entry.getKey() == Application.getServerId()) {
                // k服的城,推送所有人
                playerService.broadcast(msg);
            } else {
                // 原服的城,推送原服和k服的人
                playerService.broadcast(entry.getKey(), Application.getServerId(), msg);
            }
        }
    }

    // 推送节点变化
    public void noticeChange(RegionCapitalNode node) {
        // 全服广播
        broadcast(node);
        // aio
        sceneService.update(node, null);
    }

    // 通知相关联盟
    public void noticeAlliance(RegionCapitalNode node, Long giveUpId) {
        Set<Long> allIds = new HashSet<>();
        if (JavaUtils.bool(giveUpId)) allIds.add(giveUpId);
        if (JavaUtils.bool(node.getBelongAllianceId())) allIds.add(node.getBelongAllianceId());
        allIds.addAll(node.getDeclareWarList().keySet());
        for (var id : allIds) {
            GcUpdateRegionCapitalSimpleInfo msg = new GcUpdateRegionCapitalSimpleInfo();
            msg.addToSimpleInfo(toSimpleInfo(node));
            // 广播联盟成员
            allianceManager.broadcast(id, msg);
        }
    }

    // 归属发生变化
    public void recalcProsperity(RegionCapitalNode node, List<Long> allianceIds) {
        ServerType serverType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(node.getoServerId());
        long now = TimeUtil.getNow();
        for (var allianceId : allianceIds) {
            Alliance alliance = allianceDao.findById(allianceId);
            if (alliance != null) {
                srvDep.getAllianceProsperityService().reCalcAllianceProsperity(alliance, false);
                if (ServerType.GAME == serverType) {
                    alliance.setProsperityLastTime(now);
                } else if (ServerType.KVK_SEASON == serverType) {
                    alliance.setProsperityS2LastTime(now);
                }
            }
        }
    }

    // 队伍上限
    public int getStationTroopMaxNum(RegionCapitalNode node) {
        long realLeaderId = ableLeader(node);
        var army = armyDao.findById(realLeaderId);
        if (null == army) {
            // 没有部队
            return 1;
        }

        int ret = 1;
        // 找第一个已经达到的玩家
        Role role = roleManager.getRole(army.getRoleId());
        ret += role.getNumberProps().getInt(Prop.RALLY_CAPACITY_14000);
        return ret;
    }

    // 士兵上限
    public int getStationSoldierMaxNum(RegionCapitalNode node) {
        long realLeaderId = ableLeader(node);
        var army = armyDao.findById(realLeaderId);
        if (null == army) {
            // 没有部队
            return 0;
        }

        return armyManager.getRallySoldierMax(army);
    }

    // 当前士兵
    public int getStationSoldierNum(RegionCapitalNode node) {
        var armys = sceneService.getNodeArriveArmies(node);
        if (!JavaUtils.bool(armys)) {
            // 没有部队
            return 0;
        }

        int ret = 0;
        for (var army : armys) {
            ret += army.getSoldierCount();
        }

        return ret;
    }

    // 城市建筑驻扎到达
    public void armyStationArrive(ArmyInfo army, RegionCapitalNode node) {
        army.setWorkType(ArmyWorkType.DEFENDING);
        armyManager.saveArmy(army);

        sceneService.remove(army);
        // 更新信息给前端
        armyService.updateArmyProgress(army);

        // 炮台记录
        addArtilleryRecord(node, army, 1);

        // 开始计算功勋
        royalHonorArmyAdd(node, army, false);

        logger.info("armyStationArrive {},{},{}", army.getRoleId(), node.getMetaId(), army.getPersistKey());
    }

    // 炮台记录
    public void addArtilleryRecord(RegionCapitalNode node, ArmyInfo army, int type) {
        if (node.getType() != PsRegionCapitalType.ARTILLERY) {
            return;
        }

        Role role = army.getOwner();
        var record = new ArtilleryRecord();
        record.setType(type);
        record.setAtkName(role.getName());
        var atk = allianceDao.findById(role.getAllianceId());
        if (null != atk) {
            record.setAtkAllianceName(atk.getAliasName());
        }
        // 是否发生了战斗
        if (null != army.getFightContext()) {
            var fightContext = army.getFightContext();
            record.setWin(fightContext.isWin());
            var defFight = fightContext.getDefender();
            if (null != defFight) {
                var defRole = roleDao.findById(defFight.getMainRoleId());
                if (null != defRole) {
                    record.setDefName(defRole.getName());
                    Alliance def = allianceDao.findById(defRole.getAllianceId());
                    if (null != def) {
                        record.setDefAllianceName(def.getAliasName());
                    }
                }
            }
        }
        addArtilleryRecord(node, record);
        regionCapitalNodeDao.save(node);

        logger.info("addArtilleryRecord step1 node:{} metaId:{} record:{}", node.getPersistKey(), node.getMetaId(), record);
    }

    public void addArtilleryRecord(RegionCapitalNode node, long atkId) {
        var royalNode = getRoyalNode(node.getoServerId());
        if (null == royalNode) {
            return;
        }

        long defId = royalNode.getBelongAllianceId();
        Alliance def = allianceDao.findById(defId);
        if (null == def) {
            return;
        }

        Alliance atk = allianceDao.findById(atkId);
        if (null == atk) {
            return;
        }

        var record = new ArtilleryRecord();
        record.setType(3);
        record.setAtkAllianceName(atk.getAliasName());
        record.setDefAllianceName(def.getAliasName());
        addArtilleryRecord(node, record);
        regionCapitalNodeDao.save(node);

        logger.info("addArtilleryRecord step2 node:{} metaId:{} record:{}", node.getPersistKey(), node.getMetaId(), record);
    }

    public void addArtilleryRecord(RegionCapitalNode node, ArtilleryRecord record) {
        record.setOptTime(TimeUtil.getNow());
        var records = node.getArtilleryRecord();
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        while (records.size() > rcc.getConnonRecordMax()) {
            records.removeFirst();
        }
        node.getArtilleryRecord().add(record);
    }

    // 驻防
    public void garrison(ArmyInfo army, RegionCapitalNode node) {
        List<ArmyInfo> readyArmyList = trySplitArmy(node, army);
        // 已经到达的队伍
        List<ArmyInfo> arriveArmys = sceneService.getNodeArriveArmies(node);
        int stationSolderNum = 0;
        for (var entry : arriveArmys) {
            stationSolderNum += entry.getSoldierCount();
        }
        // 队伍上限检测
        int armyNumMax = arriveArmys.isEmpty() ? readyArmyList.size() : getStationTroopMaxNum(node);
        // 剩余队伍数量
        int remainArmySeat = armyNumMax - arriveArmys.size();
        // 士兵上限
        int soldierMaxNum = arriveArmys.isEmpty() ? 999999999 : getStationSoldierMaxNum(node);
        // 剩余士兵数量
        int remainSoldierSeat = soldierMaxNum - stationSolderNum;
        if (remainSoldierSeat <= 0) {
            logger.info("garrison capital:{} meta:{} role:{} army:{} remainSoldier < 0", node.getPersistKey(),
                    node.getMetaId(), army.getRoleId(), army.getPersistKey());
            armyManager.returnArmy(army);
            return;
        }

        List<Long> armyIds = readyArmyList.stream().map(ArmyInfo::getPersistKey).toList();
        logger.info("garrison capital:{} meta:{} armyList:{} armyNumMax:{} soldierMaxNum:{}", node.getPersistKey(),
                node.getMetaId(), armyIds, armyNumMax, soldierMaxNum);

        boolean isAllianceWar = false;
        for (var entry : readyArmyList) {
            ArmyInfo targetArmy = null;
            for (var campArmy : arriveArmys) {
                // 是否有我的驻防
                if (campArmy.getRoleId().equals(entry.getRoleId())) {
                    targetArmy = campArmy;
                    break;
                }
            }

            // 没有找到
            if (null == targetArmy) {
                // 部队数量是否足够
                if (remainArmySeat <= 0 || remainSoldierSeat <= 0) {
                    logger.info("garrison capital:{} meta:{} role:{} army:{} garrison seat or remain is zero", node.getPersistKey(),
                            node.getMetaId(), entry.getRoleId(), entry.getPersistKey());
                    armyManager.returnArmy(entry);
                    continue;
                }
                int armySoldierNum = entry.getSoldierCount();
                if (remainSoldierSeat >= armySoldierNum) {
                    // 士兵可以全部放下,全部走驻扎逻辑
                    logger.info("garrison capital:{} meta:{} role:{} army:{} garrison all", node.getPersistKey(),
                            node.getMetaId(), entry.getRoleId(), entry.getPersistKey());
                    armyStationArrive(entry, node);
                    remainSoldierSeat -= armySoldierNum;
                    remainArmySeat--;
                    isAllianceWar = true;
                    continue;
                }

                // 分兵后,剩余士兵走驻扎逻辑
                armyManager.divideArmy(entry, armySoldierNum - remainSoldierSeat, node.getMetaId() + ":" + node.getPersistKey());
                logger.info("garrison capital:{} meta:{} role:{} army:{} garrison divide:{}:{}", node.getPersistKey(),
                        node.getMetaId(), entry.getRoleId(), entry.getPersistKey(), remainSoldierSeat, armySoldierNum);
                armyStationArrive(entry, node);
                remainSoldierSeat = 0;
                remainArmySeat--;
                isAllianceWar = true;
                continue;
            }

            // 补英雄
            if (!JavaUtils.bool(targetArmy.getHeros()) && JavaUtils.bool(entry.getHeros())) {
                targetArmy.setHeros(new ArrayList<>(entry.getHeros()));
                entry.getHeros().clear();
                armyManager.saveArmy(entry);
            }

            // 补兵
            int soldierAmount = 0;
            Map<String, ArmySoldier> campSoldiers = targetArmy.getArmySoldiers();
            for (var unit : campSoldiers.entrySet()) {
                soldierAmount += unit.getValue().getCount();
            }

            // 士兵足够
            int maxSoldierAmount = armyManager.getSetOutSoldierMax(targetArmy);
            if (maxSoldierAmount <= soldierAmount) {
                // 已经有部队,且数量已经最大,没有需要补的兵了
                logger.info("garrison capital:{} meta:{} role:{} targetArmy:{} soldier:{}:{} is enough", node.getPersistKey(),
                        node.getMetaId(), entry.getRoleId(), targetArmy.getPersistKey(), maxSoldierAmount, soldierAmount);
                armyManager.returnArmy(entry);
                continue;
            }

            // 先计算之前的功勋
            royalHonorArmyAdd(node, army, false);

            // 可以补兵的数量
            int remainSoldier = remainSoldierSeat;
            if (remainSoldier > maxSoldierAmount - soldierAmount) {
                remainSoldier = maxSoldierAmount - soldierAmount;
            }
            logger.info("garrison capital:{} meta:{} role:{} army:{} soldier num:{}:{}", node.getPersistKey(),
                    node.getMetaId(), entry.getRoleId(), entry.getPersistKey(), remainSoldier, soldierAmount);
            // 等级从高到低/兵种:步兵->骑兵->弓兵
            var arriveSoldier = entry.getArmySoldiers();
            var arriveSoldierSort = arriveSoldier.values().stream().sorted((e1, e2) -> {
                var meta1 = e1.getMeta();
                var meta2 = e2.getMeta();
                if (meta1.getEra() > meta2.getEra()) {
                    return -1;
                } else if (meta1.getEra() < meta2.getEra()) {
                    return 1;
                } else {
                    return Integer.compare(meta1.getType().getValue(), meta2.getType().getValue());
                }
            }).toList();

            for (var soldier : arriveSoldierSort) {
                var solder = campSoldiers.computeIfAbsent(soldier.getSoldierMetaId(), o -> new ArmySoldier(soldier.getSoldierMetaId(), 0));
                if (remainSoldier > soldier.getCount()) {
                    solder.setCount(solder.getCount() + soldier.getCount());
                    arriveSoldier.remove(soldier.getSoldierMetaId());
                    remainSoldier -= soldier.getCount();
                } else {
                    solder.setCount(solder.getCount() + remainSoldier);
                    soldier.setCount(soldier.getCount() - remainSoldier);
                    break;
                }
            }
            remainSoldierSeat -= remainSoldier;
            isAllianceWar = true;
            armyManager.saveArmy(targetArmy);

            // 炮台记录
            addArtilleryRecord(node, entry, 1);

            // 补兵后达到队伍是否还有剩余
            if (arriveSoldier.isEmpty()) {
                // 直接删除army
                armyManager.takeBackArmy(entry);
            } else {
                armyManager.returnArmy(entry);
            }
        }

        // 切换占领者
        boolean isChange = occupyChange(army.getOwner().getAllianceId(), node);
        // 重新构筑驻防队列顺序
        resetStationOrder(node, node.getBelongAllianceId());

        regionCapitalNodeDao.save(node);

        // aoi通知
        sceneService.update(node, null);
        // 全服广播
        if (isChange) {
            broadcast(node);
        }

        // 驻防信息发生变化
        if (isAllianceWar) {
            allianceWarService.broadcastUpdate(node, PsAllianceWarInfoUpdateReason.STATION_ADD);
        }

        // 向本盟成员广播数据
        broadcastGarrisonInfo(node);
        // 同步攻击他的投石车信息
        siegeEnginesServiceImpl.regionCapitalGarrisonUpdate(node);
    }

    // 王城/炮台切换占领者
    public boolean occupyChange(Long allianceId, RegionCapitalNode node) {
        if (PsRegionCapitalType.ROYAL != node.getType() && PsRegionCapitalType.ARTILLERY != node.getType()) {
            return false;
        }

        long newAllianceId = null == allianceId ? 0 : allianceId;
        long oldAllianceId = node.getBelongAllianceId();
        // 只设置临时归属,不切换Dao里的映射关系
        node.setBelongAllianceId(newAllianceId);
        if (oldAllianceId > 0 && oldAllianceId == newAllianceId) {
            // 状态不发生变化
            return false;
        }

        // 争夺次数增加
        node.setOccupyCount(node.getOccupyCount() + 1);
        // 自动设置队长
        node.setAutoAppointAllianceId(newAllianceId);

        // 王城归属
        long royalBelongId = 0;
        // 需要检测的炮台
        List<RegionCapitalNode> artilleryNodes = new ArrayList<>();
        long now = TimeUtil.getNow();
        var rcconfig = configService.getConfig(RegionCapitalConfig.class);
        if (PsRegionCapitalType.ROYAL == node.getType()) {
            // 添加默认值
            node.getOccupyAllianceRecord().putIfAbsent(newAllianceId, 0L);
            // 切换占领者
            if (node.getOccupyStartTime() > 0) {
                node.getOccupyAllianceRecord().merge(oldAllianceId, now - node.getOccupyStartTime(), Long::sum);
            }
            node.setOccupyStartTime(now);
            // 王城设置持续时间
            node.setOccupyDuration(rcconfig.getKingCityOccupyTime());
            // 检测所有炮台状态
            artilleryNodes.addAll(getArtilleryNodes(node.getoServerId()).values());
            royalBelongId = node.getBelongAllianceId();
        } else {
            // 检测单一炮台
            artilleryNodes.add(node);
            // 王城
            RegionCapitalNode royalNode = getRoyalNode(node.getoServerId());
            if (null == royalNode) {
                return false;
            }
            royalBelongId = royalNode.getBelongAllianceId();
        }

        logger.info("occupyChange node:{} metaId:{} oldAllianceId:{} newAllianceId:{}", node.getPersistKey(),
                node.getMetaId(), oldAllianceId, newAllianceId);

        // 王城归属,一般来讲,开始争夺后王城不会没有归属
        // 所以当无归属时一定是前面的PVE阶段,也不做无归属时炮台的重置工作
//        if (royalBelongId <= 0) {
//            return;
//        }

        // 炮台归属
        for (var artillery : artilleryNodes) {
            long artilleryAllianceId = artillery.getBelongAllianceId();
            if (artilleryAllianceId <= 0) {
                continue;
            }

            if (artilleryAllianceId == royalBelongId) {
                // 炮台归属自己,停止开炮
                if (artillery.getOccupyStartTime() > 0) {
                    artillery.setOccupyStartTime(0);
                    sceneService.update(artillery, null);
                    regionCapitalNodeDao.save(artillery);
                }
            } else {
                // 归属别人,且没有切换归属
                if (oldAllianceId == artilleryAllianceId && artillery.getOccupyStartTime() > 0) {
                    continue;
                }

                // 炮台归属切换,开始开炮
                artillery.setOccupyStartTime(now);
                // 炮台次数清零
                artillery.setArtilleryCount(0);
                // 炮台设置持续时间
                artillery.setOccupyDuration(rcconfig.getCannonCDs()[0]);
                sceneService.update(artillery, null);
                regionCapitalNodeDao.save(artillery);
            }

            logger.info("occupyChange node:{} metaId:{} belongId:{} royalBelongId:{} isStart:{}", artillery.getPersistKey(),
                    artillery.getMetaId(), artillery.getBelongAllianceId(), royalBelongId, artillery.getOccupyStartTime() > 0);
        }

        // 联盟战争可能发生变化
        if (node.getType() == PsRegionCapitalType.ARTILLERY) {
            oldAllianceId = node.getBelongLastAllianceId();
        }
        allianceWarService.onDefenceChange(node, oldAllianceId, node.getBelongAllianceId());
        logger.info("occupyChange allianceWar defenceChange node:{} metaId:{} belongId:{}-{} royalBelongId:{}", node.getPersistKey(),
                node.getMetaId(), oldAllianceId, node.getBelongAllianceId(), royalBelongId);

        return true;
    }

    private List<ArmyInfo> trySplitArmy(RegionCapitalNode node, ArmyInfo originalArmy) {
        List<ArmyInfo> armyList = new ArrayList<>();
        armyList.add(originalArmy);
        var rallyContext = originalArmy.getRallyContext();
        if (rallyContext != null) {
            for (var joinArmyId : rallyContext.getJoinArmyIdList()) {
                ArmyInfo joinArmy = armyManager.findById(joinArmyId);
                if (joinArmy == null) {
                    continue;
                }
                armyList.add(joinArmy);
            }
        }
        originalArmy.setRallyContext(null);

        List<Long> armyIds = new ArrayList<>();
        // 如果之前是集结部队，则修改终点
        for (var army : armyList) {
            army.setArmyType(ArmyType.STATION_REGION_CAPITAL);
            var endPos = node.getPosition();
            Point oldPos = Point.getInstance(army.getEndX(), army.getEndY());
            if (!oldPos.equals(endPos)) {
                army.setEndX(endPos.getX());
                army.setEndY(endPos.getY());
                army.setEndSize(node.getSize());
                if (army.getMapRoute() != null) {
                    army.getMapRoute().setEnd(endPos);
                }
                army.setPos(endPos.toPosition());
                armyDao.updateArmyTargetIndex(army.getCurrentServerId(), oldPos, army);

                army.setTargetNode(node);
                army.setTargetNodeId(node.getPersistKey());
                army.setTargetNodeType(node.getNodeType());
            }
            armyIds.add(army.getPersistKey());
        }

        logger.info("trySplitArmy armyList:{}", armyIds);
        return armyList;
    }

    // 轮询
    public void lowTick() {
        long now = TimeUtil.getNow();
        try {
            hotConflictTick(now);
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("hotConflictTick error", e);
        }

        var nodes = regionCapitalNodeDao.findAll();
        tickSimpleNodes.clear();
        for (var node : nodes) {
            try {
                tick(node, now);
            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("tick error", e);
            }
        }

        // 整体推送
        broadcast(tickSimpleNodes.values());
    }

    // 热点冲突 多线程处理
    public void hotConflictTick(long now) {
        if (now - lastHotConflictTime < BizConstants.HOT_CONFLICT_TICK_INTERVAL) {
            return;
        }

        RegionCapitalHotConflictOperation operation = new RegionCapitalHotConflictOperation();
        srvDep.getAsyncOperService().execute(operation);
        // 为了保证PVP开始前10分钟，今早的刷新该数据，需要对lastHotConflictTime进行初始化
        int serverId = Application.getServerId();
        if (lastHotConflictTime <= 0) {
            Calendar cal = Calendar.getInstance();
            cal.setTimeInMillis(now);
            int minute = cal.get(Calendar.MINUTE) / 10 * 10;
            minute = minute + serverId % 5;
            cal.set(Calendar.MINUTE, minute);
            int second = serverId % 60;
            cal.set(Calendar.SECOND, second);
            lastHotConflictTime = cal.getTimeInMillis();
        } else {
            lastHotConflictTime = now;
        }
    }

    public void hotConflict() {
        int serverId = Application.getServerId();
        // 只查找k服的
        var nodes = regionCapitalNodeDao.findByCurrentServerId(serverId);
        if (!JavaUtils.bool(nodes)) {
            return;
        }

        // 清空
        hotConflictList.clear();

        // 当前时间段
        long now = TimeUtil.getNow();
        var rccConfig = configService.getConfig(RegionCapitalConfig.class);
        Integer pvpIndex = rccConfig.getPvpIndex(now + 5 * TimeUtil.MINUTE_MILLIS);
        // 还没到开战时间
        if (null == pvpIndex) {
            return;
        }

        int nodeRange = rccConfig.getHotPointRadius();
        // 州府周围的玩家数量 metaId - allianceId - roleIds
        Map<String, Map<Long, Set<Long>>> hotConflictMap = new HashMap<>();
        for (var node : nodes) {
            // 炮台不做处理
            if (node.getType() == PsRegionCapitalType.ARTILLERY) {
                continue;
            }

            Map<Long, Integer> decalareList = new HashMap<>();
            // 王城
            if (node.getType() == PsRegionCapitalType.ROYAL) {
                // 相邻州府是哪些联盟
                var mapData = worldService.getWorld().getMapData(node.getCurrentServerId());
                var region = mapData.getMapRegion(node.getRegionId());
                for (var neighbore : region.getNeighbour()) {
                    // 这个区域的节点
                    var neighboreCapital = getRegionCapital(node.getCurrentServerId(), neighbore);
                    if (null == neighboreCapital) {
                        continue;
                    }

                    // 这些联盟可以参加决战王城
                    decalareList.put(neighboreCapital.getBelongAllianceId(), pvpIndex);
                }
            } else {
                // 没有宣战不做检测 allianceId - pvpIndex
                decalareList = node.getDeclareWarList();
                if (decalareList.isEmpty()) {
                    continue;
                }

                // 阶段是否正确
                if (!decalareList.containsValue(pvpIndex)) {
                    continue;
                }
            }

            Map<Long, Set<Long>> nodeinvolvedCount = new HashMap<>();
            Point point = node.getPosition();
            for (int x = point.getX() - nodeRange; x <= point.getX() + nodeRange; x++) {
                for (int y = point.getY() - nodeRange; y <= point.getY() + nodeRange; y++) {
                    // 是否有节点
                    var sceneNode = sceneService.getSceneNode(serverId, Point.getInstance(x, y));
                    if (!(sceneNode instanceof RoleCity roleCity)) {
                        continue;
                    }

                    // 玩家是否存在
                    Role role = roleDao.findById(roleCity.getRoleId());
                    if (null == role || null == role.getAllianceId()) {
                        continue;
                    }

                    // 该盟没有宣战/且不是无主城池
                    if (!decalareList.containsKey(role.getAllianceId()) && !Objects.equals(node.getBelongAllianceId(), role.getAllianceId())) {
                        continue;
                    }

                    // 累加
                    var members = nodeinvolvedCount.computeIfAbsent(role.getAllianceId(), o -> new HashSet<>());
                    members.add(role.getRoleId());
                }
            }

            // 是否有两个盟达到筛选条件
            int allianceCount = 0;
            for (var entry : nodeinvolvedCount.entrySet()) {
                if (entry.getValue().size() >= rccConfig.getHotPointRequirementPlayer()) {
                    allianceCount++;
                }
            }
            // 太少的就算了,不做热点筛选
            if (allianceCount > 1) {
                hotConflictMap.put(node.getMetaId(), nodeinvolvedCount);
            }
        }

        // 按数量排序,取前5个
        var rankList = hotConflictMap.entrySet().stream()
                .sorted((o1, o2) -> o2.getValue().values().stream().mapToInt(Set::size).sum() - o1.getValue().values().stream().mapToInt(Set::size).sum())
                .limit(rccConfig.getMaxHotPoint()).toList();
        for (var entry1 : rankList) {
            var ps = new PsRegionCapitalHotConflict(entry1.getKey());
            for (var entry2 : entry1.getValue().entrySet()) {
                ps.putToInvolvedCount(entry2.getKey().toString(), entry2.getValue().size());
            }
            hotConflictList.add(ps);
        }

        if (!hotConflictList.isEmpty()) {
            String str1 = JSONObject.toJSONString(hotConflictMap);
            String str2 = JSONObject.toJSONString(hotConflictList);
            logger.info("hotConflict map:{} list:{}", str1, str2);
        }

        // 全服广播
        broadcastHotConflict();
    }

    // 发送冲突热点信息
    public void broadcastHotConflict() {
        var msg = new GcRegionCapitalHotConflict();
        msg.setHotConflicts(hotConflictList);
        playerService.broadcast(msg);
    }

    // 发送冲突热点信息
    public void sendHotConflict(Role role) {
        var msg = new GcRegionCapitalHotConflict();
        msg.setHotConflicts(hotConflictList);
        role.send(msg);
    }

    /**
     * 州府产出联盟石料
     *
     * @param node
     */
    private void outputAllianceHonour(RegionCapitalNode node) {
        if (node.getBelongAllianceId() <= 0) {
            return;
        }
        if (node.getType() == PsRegionCapitalType.ARTILLERY) {
            return;
        }
        if (node.getType() == PsRegionCapitalType.ROYAL && node.getStatus() == PsRegionCapitalState.PVP) {
            return;
        }

        try {
            long now = TimeUtil.getNow();
            if (now > node.getLastAllianceBuildingHonourOutPutTime() + TimeUtil.HOUR_MILLIS) {
                var rcpConfig = configService.getConfig(RegionCapitalPositionConfig.class);
                var nodeMeta = rcpConfig.get(node.getMetaId());
                Alliance alliance = allianceDao.findById(node.getBelongAllianceId());
                if (alliance != null && nodeMeta.getAllocatableyield() > 0) {
                    logger.info("allocatableyield node:{} allianceId:{} count:{}", node.getMetaId(), alliance.getId(), nodeMeta.getAllocatableyield());
                    allianceCurrencyManager.add(alliance, Currency.ALLIANCE_BUILDING_HONOR, nodeMeta.getAllocatableyield(), LogReasons.MoneyLogReason.ALLIANCE_BUILDING, "allocatableyield");
                    node.setLastAllianceBuildingHonourOutPutTime(TimeUtil.getNow());
                }
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("allianceBuildingHonourOutPut error", e);
        }
    }

    // 轮询
    public void tick(RegionCapitalNode node, long now) {
        // 州府产出联盟石料
        outputAllianceHonour(node);
        // 炮台
        if (PsRegionCapitalType.ARTILLERY == node.getType()) {
            artilleryTicker(node, now);
            return;
        } else if (PsRegionCapitalType.ROYAL == node.getType()) {
            royalTicker(node, now);
            return;
        }

        // 是否在取消中
        giveupFinish(node, now);

        // 时间未到
        if (node.getEndTime() <= 0 || now < node.getEndTime()) {
            return;
        }

        var rccConfig = configService.getConfig(RegionCapitalConfig.class);
        long oldEndTime = node.getEndTime();
        Integer pvpIndex = rccConfig.getPvpIndex(oldEndTime);
        // 怪物是否全部死亡
        var oldStatus = node.getStatus();
        // 是否切换到pvp状态
        boolean isChangePvp = false;
        // 王城特殊处理
        if (PsRegionCapitalType.ROYAL != node.getType()) {
            var endStatus = rccConfig.findEndTime(now);
            // 设置下一状态结束时间
            node.setEndTime(endStatus.getSecond());
            // 设置下一状态
            switch (endStatus.getFirst()) {
                case 0:
                    node.setStatus(PsRegionCapitalState.NORMAL);
                    node.getDeclareWarOrderMap().clear();
                    node.setPopularity(0);
                    break;
                case 1:
                    node.setStatus(PsRegionCapitalState.PVP);
                    node.setMonsterResetTime(endStatus.getSecond());
                    node.clearBattleMsgInfo();
                    resetDeclareWarOrder(node);
                    isChangePvp = true;
                    break;
                case 2:
                    node.setStatus(PsRegionCapitalState.PROTECTED);
                    resetMonster(node, now);
                    node.getDeclareWarOrderMap().clear();
                    node.setPopularity(0);
                    // 召回部队
                    disbandArmy(node);
                    break;
                case 3:
                    // 取下一天的pvp开始时间
                    endStatus = rccConfig.findEndTime(TimeUtil.getNextDay(now));
                    node.setEndTime(endStatus.getSecond());
                    node.setStatus(PsRegionCapitalState.NORMAL);
                    resetMonster(node, now);
                    break;
                default:
                    break;
            }
        }

        // 统计记录处理
        if (node.getStatus() == PsRegionCapitalState.PROTECTED && oldStatus == PsRegionCapitalState.PVP) {
            recordCalc(node, pvpIndex);
            siegeEnginesServiceImpl.clearAllByCapitalId(node.getPersistKey());
        }

        regionCapitalNodeDao.save(node);
        sceneService.update(node, null);
        tickSimpleNodes.put(node.getPersistKey(), node);

        logger.info("tick node:{} metaId:{} oldStatus:{} status:{} startTime:{} endTime:{}", node.getPersistKey(),
                node.getMetaId(), oldStatus, node.getStatus(), node.getStartTime(), node.getEndTime());

        // 报名联盟数量超过8个,则去战力排名前8
        if (isChangePvp) {
            try {
                // 该阶段报名联盟
                List<Long> applyIds = new ArrayList<>();
                for (var entry : node.getDeclareWarList().entrySet()) {
                    if (!entry.getValue().equals(pvpIndex)) {
                        continue;
                    }

                    applyIds.add(entry.getKey());
                }

                int maxLimit = 8;
                if (applyIds.size() > maxLimit) {
                    // 按战力排名
                    List<Alliance> applyAlliances = new ArrayList<>();
                    for (var allianceId : applyIds) {
                        Alliance alliance = allianceDao.findById(allianceId);
                        if (null == alliance) {
                            continue;
                        }

                        // 查找合适的位置插入
                        int i = 0;
                        for (; i < applyAlliances.size(); i++) {
                            if (alliance.getAllFightingPower() > applyAlliances.get(i).getAllFightingPower()) {
                                break;
                            }
                        }
                        applyAlliances.add(i, alliance);
                    }

                    StringBuilder sortResult = new StringBuilder();
                    for (var entry : applyAlliances) {
                        if (!sortResult.isEmpty()) {
                            sortResult.append(" ");
                        }
                        sortResult.append(" ").append(entry.getPersistKey()).append(",").append(entry.getAllFightingPower());
                    }
                    logger.info("tick applyList cut node:{} metaId:{} sortResult:{}", node.getPersistKey(), node.getMetaId(), sortResult);

                    // 后八全部取消报名
                    Long capitalId = node.getPersistKey();
                    for (int i = maxLimit; i< applyAlliances.size(); i++) {
                        Alliance alliance = applyAlliances.get(i);
                        Long allianceId = alliance.getPersistKey();
                        regionCapitalNodeDao.applyRemove(allianceId, capitalId);
                        // 取消联盟事务
                        cancelAffair(node, allianceId, pvpIndex);
                        // 删除已有的积分排名
                        node.removePveScore(allianceId);
                        // 同步信息
                        noticeChange(node);
                        // 向本盟广播
                        allianceManager.broadcast(allianceId, wrapApplyList(allianceId));

                        biLogUtil.regioncapitalApplyCancel(null, node.getMetaId(), allianceId);
                        logger.info("tick applyList cut cancel alliance:{} node:{} metaId:{} is success.", allianceId, node.getPersistKey(), node.getMetaId());

                        updateAllianceInWarFlag(allianceId, node, false);

                        siegeEnginesServiceImpl.changeCapitalBelong(allianceId, capitalId, node.getBelongAllianceId());

                        // 恢复宣战次数
                        if (alliance.getRegionCapitalApplyCount() < 2) {
                            alliance.setRegionCapitalApplyCount(alliance.getRegionCapitalApplyCount() + 1);
                            allianceDao.save(alliance);
                        }
                    }
                }
            } catch (ExpectedException ignored) {
            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("tick applyList cut error", e);
            }
        }
    }

    // 记录统计
    public void recordCalc(RegionCapitalNode node, Integer pvpIndex) {
        Set<RegionCapitalRecord> records = regionCapitalRecordService.getNoOverRecordsByCapitalId(node.getMetaId());
        Map<Long, Integer> declareWarMap = node.getDeclareWarList();
        Set<Long> declareWarAllianceSet = new HashSet<>();
        declareWarMap.forEach((declareWarAllianceId, index) -> {
            if (Objects.equals(index, pvpIndex)) {
                declareWarAllianceSet.add(declareWarAllianceId);
            }
        });
        if (node.getBelongAllianceId() != 0) {
            declareWarAllianceSet.add(node.getBelongAllianceId());
        }
        for (RegionCapitalRecord record : records) {
            if (!record.getCapitalId().equals(node.getMetaId())) {
                ErrorLogUtil.errorLog("recordCalc over error", "recordId", record.getId(), " capitalId", record.getCapitalId(), " metaId", node.getMetaId());
                continue;
            }
            if (record.getAllianceId() != null) {
//                Integer allianceIndex = declareWarMap.get(record.getAllianceId());
//                if (null == allianceIndex || allianceIndex <= 0) {
//                    allianceIndex = 1;
//                }
//                // 非这个宣战时间
//                if (!Objects.equals(allianceIndex, pvpIndex)) {
//                    continue;
//                }
            } else {
                continue;
            }
            if (node.getoServerId() != record.getServerId()) {
                continue;
            }
            boolean isWin = false;
            if (Objects.equals(node.getBelongAllianceId(), record.getAllianceId())) {
                isWin = true;
            }
            regionCapitalRecordService.over(record, isWin, declareWarAllianceSet);
        }
    }

    // 炮台
    public void artilleryTicker(RegionCapitalNode node, long now) {
        // 不是pvp
        if (node.getStatus() != PsRegionCapitalState.PVP) {
            return;
        }

        // 无人占领/本盟占领
        if (node.getBelongAllianceId() <= 0) {
            return;
        }

        // 功勋计算
        royalHonorAdd(node, true, false);

        if (node.getOccupyStartTime() <= 0) {
            return;
        }

        // 时间不足
        long belongDuration = now - node.getOccupyStartTime();
        if (belongDuration < node.getOccupyDuration()) {
            return;
        }

        var rccConfig = configService.getConfig(RegionCapitalConfig.class);
        node.setOccupyStartTime(now);
        node.setArtilleryCount(node.getArtilleryCount() + 1);
        var cannonCds = rccConfig.getCannonCDs();
        int cannonCdIndex = node.getArtilleryCount() >= cannonCds.length ? cannonCds.length - 1 : node.getArtilleryCount();
        node.setOccupyDuration(rccConfig.getCannonCDs()[cannonCdIndex]);
        double cannonDamage = rccConfig.getCannonDamages()[cannonCdIndex];

        // 开炮
        Map<Long, Map<String, Integer>> roleBadlyMap = new HashMap<>(); // <roleId, <soldierId, badly>>
        // 获取王城驻防部队
        var royalNode = getRoyalNode(node.getCurrentServerId());
        var armies = sceneService.getNodeArriveArmies(royalNode);
        for (var army : armies) {
            Map<String, Integer> badlyMap = new HashMap<>();
            var soldiers = army.getArmySoldiers();
            int badlySum = 0;
            long score = 0;
            for (var _soldier : soldiers.entrySet()) {
                var soldier = _soldier.getValue();
                if (soldier.getCount() <= 0) {
                    continue;
                }

                int badly = (int) Math.ceil(1.0 * soldier.getCount() * cannonDamage / 100);
                badlySum += badly;
                badlyMap.put(soldier.getSoldierMetaId(), badly);
                // 士兵减少
                soldier.subCount(badly);

                var soldierMeta = SoldierConfig.getSoldierMeta(_soldier.getKey());
                if (null != soldierMeta) {
                    score += kvkHonorService.trigger(army.getOwner(), HonorType.BE_KILLED_IN_ARTILLERY,
                            soldierMeta.getEra(), 0, badly);
                }
            }

            if (badlyMap.isEmpty()) {
                continue;
            }

            // 决战王城
            royalHonorAdd(node, army.getOwner(), score);

            roleBadlyMap.put(army.getOwner().getRoleId(), badlyMap);
            // 伤兵逻辑
            srvDep.getSoldierService().addWounded(army.getOwner(), badlyMap, SoldierUpdateReasonType.ARTILLERY, node.getPersistKey().toString(), true);

            // 邮件提醒
            List<String> params = new ArrayList<>();
            params.add(badlySum + "");
            var mail = mailCreator.createSystemMail(army.getRoleId(), node.getCurrentServerId(), rccConfig.getArtilleryAttackMailId(), params, null);
            if (null != mail) {
                mailSender.sendOneMail(mail);
            }

            // 已经没有活着的士兵了
            if (army.getSoldierCount() <= 0) {
                armyManager.returnArmy(army);
            }

            // 更新信息给前端
            armyService.updateArmyProgress(army);
        }

        // 炮台发射
        logger.info("artilleryTicker node:{} metaId:{} continueCount:{} roleBadlyMap:{}", node.getPersistKey(), node.getMetaId(),
                node.getArtilleryCount(), roleBadlyMap);

        // 炮台记录
        addArtilleryRecord(node, node.getBelongAllianceId());

        sceneService.update(node, null);
        sceneService.update(royalNode, null);
//        tickSimpleNodes.put(node.getPersistKey(), node);

        regionCapitalNodeDao.save(node);

        // galog
        try {
            var atkAlliance = allianceDao.findById(node.getBelongAllianceId());
            var meta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
            var defAlliance = allianceDao.findById(royalNode.getBelongAllianceId());
            if (null != defAlliance) {
                biLogUtil.royalArtilleryFire(node.getMetaId(), meta.getName(), atkAlliance.getId(), atkAlliance.getName(),
                        defAlliance.getId(), defAlliance.getName(), roleBadlyMap);
            } else {
                biLogUtil.royalArtilleryFire(node.getMetaId(), meta.getName(), atkAlliance.getId(), atkAlliance.getName(),
                        0, "", roleBadlyMap);
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("artilleryTicker bilog error", e);
        }
    }

    // 王城占领时间是否足够
    public void royalTicker(RegionCapitalNode node, long now) {
        // 轮询周六发邮件
        tickRoyalOpenMail(node);

        // 功勋计算
        royalHonorAdd(node, true, false);

        // 时间未到
        if (node.getEndTime() <= 0 || now < node.getEndTime()) {
            // PVP状态检测占领时长是否足够
            if (node.getStatus() == PsRegionCapitalState.PVP) {
                if (node.getBelongAllianceId() > 0) {
                    var occupyAllianceRecord = node.getOccupyAllianceRecord();
                    long readyDuration = occupyAllianceRecord.getOrDefault(node.getBelongAllianceId(), 0L);
                    long belongDuration = now - node.getOccupyStartTime() + readyDuration;
                    if (belongDuration >= node.getOccupyDuration()) {
                        // 王城被谁占领计算
                        royalFinish(node, now);
                    }
                }
            }

            return;
        }

        // 怪物是否全部死亡
        var oldStatus = node.getStatus();
        var rccConfig = configService.getConfig(RegionCapitalConfig.class);
        // 王城特殊处理
        var endStatus = rccConfig.findRoyalEndTime(now);
        if (endStatus.getFirst() == 0) {
            // 王城被谁占领计算
            royalFinish(node, now);
        } else {
            // 设置下一状态结束时间
            node.setEndTime(endStatus.getSecond());
            node.setStatus(PsRegionCapitalState.PVP);
            // 开启时,清理上次争夺时长,活动结束时还有显示
            node.clearRoyal();
            node.setBelongLastAllianceId(node.getBelongAllianceId());
            regionCapitalNodeDao.changeIndex(node, 0L);
            try {
                //同盟建筑失效
                allianceBuildingService.inActiveBuilding(node.getBelongLastAllianceId(), node.getCurrentServerId(), node.getRegionId());
            } catch (ExpectedException ignored) {
            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("royalTicker inActiveBuilding error", e);
            }
            allianceService.updateAllianceMemberPropTerritory(node.getBelongLastAllianceId());
            recalcProsperity(node, List.of(node.getBelongLastAllianceId()));
            // 清理驻防队伍
            removeDefenceArmies(node);
            // 炮台设置开始
            artilleryStateChange(node.getoServerId(), true);
            // 王城非第一次争夺没有怪物
            if (node.getOccupyCount() > 0) {
                var rcp = configService.getConfig(RegionCapitalPositionConfig.class);
                var royalMeta = rcp.get(node.getMetaId());
                node.setMonsterDeadCount(royalMeta.getNpcNum(true));
                node.setBossDeadCount(royalMeta.getBossNum(true));
            }
            //上任国王清理
            try {
                officialsService.kingTermEndClean(node.getCurrentServerId());
            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("tick kingTermEndClean error", e);
            }
        }

        regionCapitalNodeDao.save(node);
        sceneService.update(node, null);
        tickSimpleNodes.put(node.getPersistKey(), node);

        logger.info("royalTicker node:{} metaId:{} oldStatus:{} status:{} startTime:{} endTime:{}", node.getPersistKey(),
                node.getMetaId(), oldStatus, node.getStatus(), node.getStartTime(), node.getEndTime());
    }

    // 王城占领时间结算
    public void royalFinish(RegionCapitalNode node, long now) {
        // 解散向王城和炮台的集结及行军
        disbandArmy(node);

        var occupyAllianceRecord = node.getOccupyAllianceRecord();
        // 结算最后占领者时间
        if (node.getBelongAllianceId() > 0) {
            occupyAllianceRecord.merge(node.getBelongAllianceId(), now - node.getOccupyStartTime(), Long::sum);
        }
        Map.Entry<Long, Long> leader = null;
        for (var entry : occupyAllianceRecord.entrySet()) {
            if (null == leader) {
                leader = entry;
                continue;
            }

            if (leader.getValue() < entry.getValue()) {
                leader = entry;
            }
        }

        long oldAllianceId = node.getBelongLastAllianceId();
        // 没有人争抢
        if (null == leader) {
            // 争夺次数增加
            node.setOccupyCount(node.getOccupyCount() + 1);
            // 设置为上任国王
            regionCapitalNodeDao.changeIndex(node, node.getBelongLastAllianceId());
        } else {
            // 设置新国王
            regionCapitalNodeDao.changeIndex(node, leader.getKey());
        }
        long newAllianceId = node.getBelongAllianceId();
        logger.info("royalFinish metaId:{} newKing:{} lastKing:{} occupyRecord:{}", node.getMetaId(), newAllianceId, oldAllianceId, occupyAllianceRecord);
        recalcProsperity(node, List.of(oldAllianceId, newAllianceId));
        node.setLastAllianceBuildingHonourOutPutTime(TimeUtil.getNow());

        // 上报给诸侯争霸
        var newKineAlliance = allianceDao.findById(newAllianceId);
        if(newKineAlliance != null) {
            srvDep.getVassalService().reportRoyalFinish(newKineAlliance.getoServerId(), newKineAlliance);
        }

        // 清除太守信息
        if (newAllianceId != oldAllianceId) {
            allianceManager.clearGovernorByMetaId(oldAllianceId, node.getMetaId());
        }

        // 炮台清理
        artilleryStateChange(node.getoServerId(), false);

        var rccConfig = configService.getConfig(RegionCapitalConfig.class);
        // 切换到免战/直到下周日开启
        long endTime = rccConfig.getKingCityNextOpenTime(now);
        node.setEndTime(endTime);
        node.setStatus(PsRegionCapitalState.PROTECTED);

        // 切换归属
        belongChange(node, oldAllianceId, newAllianceId);
        // 进攻胜利发送邮件
        sendAttackSuccessMail(node, newAllianceId);
        // 统计记录处理
        recordCalc(node, 0);

        tickSimpleNodes.put(node.getPersistKey(), node);
        regionCapitalNodeDao.save(node);

        // 功勋排名处理
        if (Application.getServerType() == ServerType.KVK_SEASON) {
            if (node.getCurrentServerId() == Application.getServerId()) {
                royalActivityManager.onActivityFinishRank();
            }
        } else {
            royalActivityManager.onActivityFinishRank();
        }

        // 无人占领,重置怪物/进度信息
        if (node.getBelongAllianceId() <= 0) {
            node.setBossDeadCount(0);
            node.setMonsterDeadCount(0);
            node.setMonsterResetTime(0);
            // 清空积分
            node.getPveScore().clear();
            node.getCurRolePveScore().clear();
            regionCapitalNodeDao.save(node);

            logger.info("royalFinish node:{} metaId:{} king is empty reset monster", node.getPersistKey(), node.getMetaId());
        }

        //国王开始下一个任期
        try {
            officialsService.kingAppointCountDownBegin(node.getCurrentServerId());
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("royalFinish kingAppointCountDownBegin error", e);
        }

        // galog
        try {
            var meta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
            if (JavaUtils.bool(occupyAllianceRecord)) {
                for (var entry : occupyAllianceRecord.entrySet()) {
                    var alliance = allianceDao.findById(entry.getKey());
                    biLogUtil.royalFinish(node.getMetaId(), meta.getName(), alliance.getId(), alliance.getName(),
                            entry.getValue(), entry.getKey() == node.getBelongAllianceId(), occupyAllianceRecord.size());
                }
            } else {
                var alliance = allianceDao.findById(node.getBelongAllianceId());
                if (null != alliance) {
                    biLogUtil.royalFinish(node.getMetaId(), meta.getName(), alliance.getId(), alliance.getName(),
                            0, true, occupyAllianceRecord.size());
                }
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("artilleryTicker bilog error", e);
        }

        // 王盟切换 需要通知赛季备战
        seasonWarmUpActivityServiceImpl.onRoyalFinish(node);
    }

    // 炮台状态切换
    public void artilleryStateChange(int serverId, boolean isOpen) {
        var artillerys = getArtilleryNodes(serverId);
        if (!JavaUtils.bool(artillerys)) {
            return;
        }

        for (var entry : artillerys.values()) {
            // 炮台直接设置
            entry.setBelongAllianceId(0);
            // 部队返回
            disbandArmy(entry);
            // 清理数据
            entry.clearRoyal();
            if (isOpen) {
                entry.setStatus(PsRegionCapitalState.PVP);
            } else {
                entry.setStatus(PsRegionCapitalState.PROTECTED);
            }

            regionCapitalNodeDao.save(entry);

            tickSimpleNodes.put(entry.getPersistKey(), entry);
        }
    }

    // 取消成功
    public void giveupFinish(RegionCapitalNode node, long now) {
        // 没有在取消中
        if (node.getGiveupEndTime() <= 0) {
            return;
        }

        // 时间未到
        if (now < node.getGiveupEndTime()) {
            return;
        }

        // 联盟查找
        Alliance alliance = allianceDao.findById(node.getBelongAllianceId());
        if (null == alliance) {
            return;
        }

        // 重置时间
        node.setGiveupEndTime(0);
        // 移除buff
        calcOccupyProp(alliance);
        // 重算属性
        srvDep.getAllianceProsperityService().reCalcAllianceProsperity(alliance, false);
        // 放弃该州府
        long oldAllianceId = node.getBelongAllianceId();
        regionCapitalNodeDao.changeIndex(node, 0L);
        recalcProsperity(node, List.of(oldAllianceId));
        // 召回部队
        removeDefenceArmies(node);

        // 清除太守信息
        allianceManager.clearGovernorByMetaId(oldAllianceId, node.getMetaId());

        regionCapitalNodeDao.save(node);
        // aio
        sceneService.update(node, null);
        tickSimpleNodes.put(node.getPersistKey(), node);

        try {
            //同盟建筑失效
            allianceBuildingService.inActiveBuilding(oldAllianceId, node.getCurrentServerId(), node.getRegionId());
            //诸侯失效
            String lordMetaId = configService.getConfig(KingOfficialConfig.class).getRegion2lordMap().get(node.getMetaId());
            if (StringUtils.isNotBlank(lordMetaId)) {
                officialsService.inActiveLord(lordMetaId);
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("giveupFinish inActiveBuilding error", e);
        }
        allianceService.updateAllianceMemberPropTerritory(oldAllianceId);

        milestoneService.onMilestoneEventTrigger(MilestoneType.CITY_OCCUPIED_LEVEL, alliance.getId());

        // 取消被宣战事务
        allianceAffairService.cancelAffair(AllianceAffairType.REGION_CAPITAL_BEAPPLY, alliance.getId(), node.getMetaId());

        // 放弃邮件
        giveupEmail(alliance, node);

        logger.info("giveupFinish node:{} allianceId:{}", node.getMetaId(), oldAllianceId);
    }

    // 怪物是否重置
    public void resetMonster(RegionCapitalNode node, long now) {
        var rccConfig = configService.getConfig(RegionCapitalConfig.class);
        node.setBossDeadCount(0);
        node.setMonsterDeadCount(0);
        node.setMonsterResetTime(0);
        // 清空积分
        node.getPveScore().clear();
        node.getCurRolePveScore().clear();
        // 清除所有宣战信息,向前5分钟,为了取到上一阶段
        Integer pvpIndex = rccConfig.getPvpIndex(now - 5 * TimeUtil.MINUTE_MILLIS);
        applyListClear(node, pvpIndex);
        regionCapitalNodeDao.save(node);

        logger.info("resetMonster node:{} metaId:{} pvpIndex:{} reset monster end time", node.getPersistKey(), node.getMetaId(), pvpIndex);
    }

    // 普通城池结束
    public void normalFinish(RegionCapitalNode node, Alliance newAlliance) {
        // 召回部队
        disbandArmy(node);

        long newAllianceId = null == newAlliance ? 0 : newAlliance.getId();
        long oldAllianceId = node.getBelongAllianceId();
        Integer pvpIndex = node.getDeclareWarList().get(newAllianceId);

        // 重置怪物信息
        node.setBossDeadCount(0);
        node.setMonsterDeadCount(0);
//        node.setMonsterResetTime(0);
        // 重置放弃倒计时
        node.setGiveupEndTime(0);
        node.setLastAllianceBuildingHonourOutPutTime(TimeUtil.getNow());
        regionCapitalNodeDao.changeIndex(node, newAllianceId);

        // 清除太守信息
        if (newAllianceId != oldAllianceId) {
            allianceManager.clearGovernorByMetaId(oldAllianceId, node.getMetaId());
        }

        // 清除所有宣战信息
        applyListClear(node, null == pvpIndex ? 0 : pvpIndex, newAllianceId);

        // 争夺次数增加
        node.setOccupyCount(node.getOccupyCount() + 1);
        // 切换归属
        belongChange(node, oldAllianceId, newAllianceId);

        // 战斗记录统计
//        if (newAlliance != null) {
//            regionCapitalRecordService.over(newAllianceId, node.getMetaId(), node.getEndTime(), true);
//            regionCapitalRecordService.over(oldAllianceId, node.getMetaId(), node.getEndTime(), false);
//        } else {
//            regionCapitalRecordService.over(oldAllianceId, node.getMetaId(), node.getEndTime(), true);
//        }
//        for (Long _allianceId : node.getDeclareWarList().keySet()) {
//            if (newAllianceId == 0 || newAllianceId == _allianceId) {
//                continue;
//            }
//            // 统计输掉的
//            regionCapitalRecordService.over(_allianceId, node.getMetaId(), node.getEndTime(), false);
//        }

        //诸侯任命
        try {
            String lordMetaId = configService.getConfig(KingOfficialConfig.class).getRegion2lordMap().get(node.getMetaId());
            if (StringUtils.isNotBlank(lordMetaId)) {
                officialsService.lordAppointCountDownBegin(lordMetaId);
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("normalFinish lordAppointCountDownBegin error", e);
        }

        logger.info("pvpFinishForce node:{} nodeMeta:{} oldBelong:{} newBelong:{}", node.getPersistKey(), node.getMetaId(), oldAllianceId, newAllianceId);
    }

    public void belongChange(RegionCapitalNode node, long oldAllianceId, long newAllianceId) {
        Alliance newAlliance = allianceDao.findById(newAllianceId);
        if (null == newAlliance) {
            return;
        }

        // 占领buff
        calcOccupyProp(newAlliance);
        // 自动设置队长
        node.setAutoAppointAllianceId(newAllianceId);

        // 更新归属联盟的势力值
        recalcProsperity(node, List.of(oldAllianceId, newAllianceId));
        try {
            //旧同盟建筑失效
            allianceBuildingService.inActiveBuilding(oldAllianceId, node.getCurrentServerId(), node.getRegionId());
            //新同盟建筑生效
            allianceBuildingService.activeBuilding(newAllianceId, node.getCurrentServerId(), node.getRegionId());
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("pvpFinishForce activeBuilding error", e);
        }
        // 刷新属性
        allianceService.updateAllianceMemberPropTerritory(oldAllianceId);
        allianceService.updateAllianceMemberPropTerritory(newAlliance);
        // 日志记录
        var belongList = node.getBelongLogs();
        var belongInfo = RegionCapitalLog.create(newAlliance, belongList.size() + 1);
        belongList.add(belongInfo);

        // 聊天
        regionCapitalChatExport.onOccupy(newAlliance, node);

        // 联盟广播
        if (node.getType() == PsRegionCapitalType.ROYAL) {
            sendBelongNotice(node.getMetaId(), newAlliance.getPersistKey(), null, false);
        } else {
            sendBelongNotice(node.getMetaId(), newAlliance.getPersistKey(), null, node.getOccupyCount() <= 1);
        }

        // 发放奖励
        sendBelongRewardMail(node, newAlliance.getPersistKey());

        milestoneService.onMilestoneEventTrigger(MilestoneType.CITY_OCCUPIED_LEVEL, newAllianceId);
        srvDep.getAllianceMissionService().onMissionFinish(newAlliance, MissionType.REGION_CAPITAL_OCCUPY, 1);
        // 积分处理
        node.belongPveScore(newAllianceId);

        // 城池被攻陷后发同盟告知邮件
        if (oldAllianceId > 0 && oldAllianceId != newAllianceId) {
            sendDefenceFailedMail(node, oldAllianceId, newAlliance.getAliasName());
        }

        Integer pvpIndex = node.getDeclareWarList().get(newAllianceId);
        // duration需要做从A占领到B占领这段时间长度
        long now = TimeUtil.getNow();
        biLogUtil.regioncapitalBelong(node.getMetaId(), node.getLevel(), oldAllianceId, newAllianceId, pvpIndex, now - node.getOccupyStartTime() / 1000);
        node.setOccupyStartTime(now);
        // 处理
        if (oldAllianceId != newAllianceId) {
            siegeEnginesServiceImpl.changeCapitalBelong(newAllianceId, node.getPersistKey(), oldAllianceId);
        }
    }

    // 清除该节点当前时间段所有宣战信息
    public void applyListClear(RegionCapitalNode node, Integer pvpIndex) {
        // 取消联盟事务
        Set<Long> allianceIds = new HashSet<>();
        for (var entry : node.getDeclareWarList().entrySet()) {
            if (null == pvpIndex) {
                // 如果是null,则全部清除
                cancelAffair(node, entry.getKey(), entry.getValue());
                allianceIds.add(entry.getKey());
            } else if (entry.getValue().equals(pvpIndex)) {
                cancelAffair(node, entry.getKey(), entry.getValue());
                allianceIds.add(entry.getKey());
            }
        }

        // 向本盟广播
        for (var allianceId : allianceIds) {
            regionCapitalNodeDao.applyRemove(allianceId, node.getPersistKey());
            allianceManager.broadcast(allianceId, wrapApplyList(allianceId));
        }

        // 发生了变化,推送相关联盟宣战信息
        if (!allianceIds.isEmpty()) {
            for (var allianceId : node.getDeclareWarList().keySet()) {
                allianceManager.broadcast(allianceId, wrapApplyList(allianceId));
            }
        }
    }

    // 清除该节点当前时间段宣战信息
    public void applyListClear(RegionCapitalNode node, int pvpIndex, Long allianceId) {
        // 取消联盟事务
        cancelAffair(node, allianceId, pvpIndex);

        // 清除所有宣战信息
        var applyList = new HashSet<>(node.getDeclareWarList().keySet());
        regionCapitalNodeDao.applyRemove(allianceId, node.getPersistKey());
        // 向本盟广播
        for (var entry : applyList) {
            allianceManager.broadcast(entry, wrapApplyList(entry));
        }
    }

    // 被攻击
    public void beattack(RegionCapitalNode node) {
        if (node.getBelongAllianceId() <= 0) {
            return;
        }

        long now = TimeUtil.getNow();
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        if (node.getBeattackLastTime() + rcc.getRedAlertCD() > now) {
            return;
        }

        node.setBeattackLastTime(now);

        if (node.getType() == PsRegionCapitalType.ARTILLERY) {
            // 炮台屏蔽
            return;
        }

        // 聊天
        regionCapitalChatExport.onBeAttack(node.getBelongAllianceId(), node);
    }

    private boolean checkSiegeEnginesOccupy(SceneNode node, SceneNode targetNode) {
        // targetNode 为攻城器
        if (node.getNodeType() != SceneNodeType.ALLIANCE_SIEGE_ENGINES) {
            return true;
        }
        if (!node.getPersistKey().equals(targetNode.getPersistKey())) {
            return true;
        }
        return false;
    }

    /**
     * targetNode 为州府类型
     *
     * @param node
     * @param targetNode
     * @return
     */
    private boolean checkRegionCapitalOccupy(SceneNode node, SceneNode targetNode) {
        // 州府和炮台  不能与攻城器同时存在
        if (node.getNodeType() == SceneNodeType.ALLIANCE_SIEGE_ENGINES) {
            return true;
        }
        if (!(node instanceof RegionCapitalNode sourceRegionCapitalNode)) {
            return false;
        }
        RegionCapitalNode targetRegionCapitalNode = (RegionCapitalNode) targetNode;
        if (targetRegionCapitalNode.getType() == PsRegionCapitalType.ARTILLERY) {
            // 炮台 + 王城的组合不行
            if (sourceRegionCapitalNode.getType() == PsRegionCapitalType.ROYAL) {
                return true;
            } else if (sourceRegionCapitalNode.getType() == PsRegionCapitalType.ARTILLERY &&
                    !sourceRegionCapitalNode.getPersistKey().equals(targetRegionCapitalNode.getPersistKey())) {
                // 炮台A + 炮台B 不行
                return true;
            }
        } else if (targetRegionCapitalNode.getType() == PsRegionCapitalType.ROYAL &&
                sourceRegionCapitalNode.getType() == PsRegionCapitalType.ARTILLERY) {
            // 王城 + 炮台不行
            return true;
        }
        return false;
    }

    /**
     * 需求：同时可以进攻多个州府和王城，
     * 打攻城器不能打其他
     * 打炮台可以打其他州府
     *
     * @param role
     * @param node
     * @return
     */
    public boolean isOccupyRoyal(Role role, SceneNode node) {
        Collection<ArmyInfo> armyInfos = armyDao.findByRoleId(role.getRoleId());
        if (!JavaUtils.bool(armyInfos)) {
            return false;
        }
        for (ArmyInfo armyInfo : armyInfos) {
            if (armyInfo.getWorkType() == ArmyWorkType.RETURN) {
                continue;
            }
            SceneNode targetNode = armyInfo.getTargetNode();
            if (targetNode == null) {
                continue;
            }
            switch (targetNode.getNodeType()) {
                case ALLIANCE_SIEGE_ENGINES -> {
                    if (checkSiegeEnginesOccupy(node, targetNode)) {
                        return true;
                    }
                    break;
                }
                case REGION_CAPITAL -> {
                    if (checkRegionCapitalOccupy(node, targetNode)) {
                        return true;
                    }
                    break;
                }
                case CITY -> {
                    // 这个node一定是州府或者攻城器
                    if (armyInfo.getArmyType() == ArmyType.JOIN) {
                        var rallyContext = armyInfo.getRallyContext();
                        if (null == rallyContext) {
                            break;
                        }
                        // 获得队长Army信息
                        var leaderArmy = armyDao.findById(rallyContext.getLeaderArmyId());
                        if (null == leaderArmy) {
                            break;
                        }
                        ArmyType armyType = leaderArmy.getArmyType();
                        // 是否是对州府或攻城器的集结
                        SceneNode tNode = leaderArmy.getTargetNode();
                        if ((armyType != ArmyType.RALLY_REGION_CAPITAL && armyType != ArmyType.RALLY_SIEGE_ENGINES) ||
                                null == tNode) {
                            break;
                        }

                        if (tNode.getNodeType() == SceneNodeType.ALLIANCE_SIEGE_ENGINES) {
                            if (checkSiegeEnginesOccupy(node, tNode)) {
                                return true;
                            }
                        } else if (tNode.getNodeType() == SceneNodeType.REGION_CAPITAL) {
                            if (checkRegionCapitalOccupy(node, tNode)) {
                                return true;
                            }
                        }

                    }
                    break;
                }
            }
        }
        return false;
    }

    // 重算占领后buff
    private void calcOccupyProp(Alliance alliance) {
        allianceCalcPropManager.updateProps(alliance, ICalcProp.AllianceType.REGION_CAPITAL);
        List<AllianceMember> members = allianceMemberManager.getMembers(alliance.getId());
        for (AllianceMember member : members) {
            Role role = roleManager.getRole(member.getPersistKey());
            if (role != null) {
                roleCalcPropManager.addChangeType(role, ICalcProp.RoleType.REGION_CAPITAL);
            }
        }
    }

    // 获取洛阳城的联盟的盟主id
    public long getRoyalCapitalLeaderId(int serverId) {
        RegionCapitalNode royalNode = getRoyalNode(serverId);
        if (royalNode == null) {
            return 0;
        }

        if (!JavaUtils.bool(royalNode.getBelongAllianceId())) {
            return 0;
        }

        // 洛阳处于争夺状态,获取不到占领者
        if (royalNode.getStatus() == PsRegionCapitalState.PVP) {
            return 0;
        }

        Alliance alliance = allianceDao.findById(royalNode.getBelongAllianceId());
        if (null == alliance) {
            return 0;
        }

        AllianceLeader allianceLeader = alliance.getAllianceLeader();
        return allianceLeader.getId();
    }

    // 驻防顺序调整
    public void stationSetOrder(Role role, long id, long armyId) {
        if (!functionSwitchService.isOpen(FunctionType.REGION_CAPITAL.getId(), role)) {
            logger.info("stationSetOrder switch close roleId {} ", role.getRoleId());
            return;
        }

        RegionCapitalNode node = regionCapitalNodeDao.findById(id);
        if (node == null) {
            ErrorLogUtil.errorLog("stationSetOrder node not find", "node", id);
            return;
        }

        var leaderArmy = armyDao.findById(node.getLeaderId());
        // 权限判断
        Long allianceId = role.getAllianceId();
        if ((null == leaderArmy || !leaderArmy.getRoleId().equals(role.getRoleId())) && !allianceManager.checkPermission(Auth.GVG_STATION_SET_ORDER, role.getId())) {
            ErrorLogUtil.errorLog("stationSetOrder role isn't leader or permission", "roleId", role.getId(), "allianceId", allianceId);
            return;
        }

        if (!Objects.equals(node.getBelongAllianceId(), allianceId)) {
            ErrorLogUtil.errorLog("stationSetOrder isn't belong or temp", "roleId", role.getId(), "allianceId", allianceId, "nodeId", id, "metaId", node.getMetaId());
            return;
        }

        var armyArriveCamps = sceneService.getNodeArriveArmies(node);
        ArmyInfo targetArmy = null;
        for (var army : armyArriveCamps) {
            if (army.getPersistKey() == armyId) {
                targetArmy = army;
                break;
            }
        }

        if (null == targetArmy) {
            ErrorLogUtil.errorLog("stationSetOrder isn't find or not arrive", "roleId", role.getId(), "allianceId", allianceId, "nodeId", id, "metaId", node.getMetaId());
            return;
        }

        // 出战顺序修改
        node.setLeaderId(armyId);
        regionCapitalNodeDao.save(node);

        // aoi通知 具体信息发生了变化
        sceneService.update(node, null);

        biLogUtil.regioncapitalSetOrder(role, node.getMetaId(), role.getAllianceId(), targetArmy.getRoleId());
        logger.info("stationSetOrder armyId:{} role:{} alliance:{} node:{} metaId:{}.",
                armyId, role.getRoleId(), allianceId, node.getPersistKey(), node.getMetaId());
    }

    // 驻防自动任命队长
    public void stationAuto(Role role, long id, boolean isAuto) {
        if (!functionSwitchService.isOpen(FunctionType.REGION_CAPITAL.getId(), role)) {
            logger.info("stationAuto switch close roleId {} ", role.getRoleId());
            return;
        }

        RegionCapitalNode node = regionCapitalNodeDao.findById(id);
        if (node == null) {
            ErrorLogUtil.errorLog("stationAuto node not find", "nodeId", id);
            return;
        }

        // 权限判断
        Long allianceId = role.getAllianceId();
        if (!allianceManager.checkPermission(Auth.GVG_STATION_SET_ORDER, role.getId())) {
            ErrorLogUtil.errorLog("stationAuto role isn't leader or permission", "role", role.getId(), "allianceId", allianceId);
            return;
        }

        // 是否我们联盟占领
        if (!Objects.equals(node.getBelongAllianceId(), allianceId)) {
            ErrorLogUtil.errorLog("stationAuto node meta isn't belong or temp", "node", id, "meta", node.getMetaId(), "role", role.getId(), "allianceId", allianceId);
            return;
        }

        // 自动任命
        if (isAuto) {
            node.setAutoAppointAllianceId(allianceId);
        } else {
            node.setAutoAppointAllianceId(null);
        }
        // 重新构筑驻防队列顺序
        if (resetStationOrder(node, role.getAllianceId())) {
            // 驻防信息发生变化
            allianceWarService.broadcastUpdate(node, PsAllianceWarInfoUpdateReason.STATION_LEADER);
        }
        regionCapitalNodeDao.save(node);
        // aoi通知 具体信息发生了变化
        sceneService.update(node, null);
        noticeAlliance(node, null);

        logger.info("stationAuto role:{} alliance:{} node:{} metaId:{} auto:{}.", role.getRoleId(),
                allianceId, node.getPersistKey(), node.getMetaId(), node.getAutoAppointAllianceId());
    }

    // 重新构筑驻防队列顺序
    public boolean resetStationOrder(RegionCapitalNode node, Long allianceId) {
        var sortArriveCamps = sceneService.getNodeArriveArmies(node);
        if (sortArriveCamps.isEmpty()) {
            // 没有人了,队长设置为0
            if (node.getLeaderId() > 0) {
                node.setLeaderId(0);
                logger.info("resetStationOrder alliance:{} node:{} metaId:{} leaderId:{}.", allianceId, node.getPersistKey(), node.getMetaId(), node.getLeaderId());
                regionCapitalNodeDao.save(node);
                return true;
            }

            return false;
        }
//        else {
//            // 没有队长,设置一个默认的
//            if (node.getLeaderId() <= 0) {
//                node.setLeaderId(sortArriveCamps.getFirst().getPersistKey());
//                regionCapitalNodeDao.save(node);
//                logger.info("resetStationOrder alliance:{} node:{} metaId:{} first member is leaderId:{}.", allianceId, node.getPersistKey(), node.getMetaId(), node.getLeaderId());
//                return true;
//            }
//        }

        // 队长是否不在列表中
        boolean isExist = false;
        for (var army : sortArriveCamps) {
            if (army.getPersistKey().equals(node.getLeaderId())) {
                isExist = true;
                break;
            }
        }

        // 不自动构建
        boolean isAuto = Objects.equals(node.getAutoAppointAllianceId(), allianceId);
        if (isExist && !isAuto) {
            return false;
        }

        sortArriveCamps.sort((ele1, ele2) -> {
            long power1 = 0, power2 = 0;
            for (var hero : ele1.getHeros()) {
                var h = heroService.getHero(ele1.getRoleId(), hero);
                if (null != h) {
                    power1 += h.getPower();
                }
            }
            for (var hero : ele2.getHeros()) {
                var h = heroService.getHero(ele2.getRoleId(), hero);
                if (null != h) {
                    power2 += h.getPower();
                }
            }

            return Long.compare(power2, power1);
        });

        // 顺序是否发生变化
        boolean isChange = false;
        if (!sortArriveCamps.isEmpty()) {
            if (!sortArriveCamps.getFirst().getPersistKey().equals(node.getLeaderId())) {
                isChange = true;
            }
        }

        // 顺序需要变化
        if (!isChange) {
            return false;
        }

        // 出战顺序修改
        long oldLeaderId = node.getLeaderId();
        node.setLeaderId(sortArriveCamps.getFirst().getPersistKey());
        regionCapitalNodeDao.save(node);

        logger.info("resetStationOrder alliance:{} node:{} metaId:{} change leaderId:{} isExist:{}-{} isAuto:{}-{}.",
                allianceId, node.getPersistKey(), node.getMetaId(), node.getLeaderId(), isExist, oldLeaderId, node.getAutoAppointAllianceId(), allianceId);
        return true;
    }

    // 尝试获取队长
    public long ableLeader(RegionCapitalNode node) {
        // 没有归属,原样返回
        if (node.getBelongAllianceId() <= 0) {
            return node.getLeaderId();
        }

        var sortArriveCamps = sceneService.getNodeArriveArmies(node);
        if (!JavaUtils.bool(sortArriveCamps)) {
            return node.getLeaderId();
        }
        for (var army : sortArriveCamps) {
            if (null == army) {
                continue;
            }

            if (army.getPersistKey().equals(node.getLeaderId())) {
                return node.getLeaderId();
            }
        }

        sortArriveCamps.sort((ele1, ele2) -> {
            long power1 = 0, power2 = 0;
            for (var hero : ele1.getHeros()) {
                var h = heroService.getHero(ele1.getRoleId(), hero);
                if (null != h) {
                    power1 += h.getPower();
                }
            }
            for (var hero : ele2.getHeros()) {
                var h = heroService.getHero(ele2.getRoleId(), hero);
                if (null != h) {
                    power2 += h.getPower();
                }
            }

            return Long.compare(power2, power1);
        });

        // 出战顺序修改
        long oldLeaderId = node.getLeaderId();
        node.setLeaderId(sortArriveCamps.getFirst().getPersistKey());
        regionCapitalNodeDao.save(node);

        logger.info("ableLeader node:{} metaId:{} change oldLeaderId:{} leaderId:{} reset.",
                node.getPersistKey(), node.getMetaId(), oldLeaderId, node.getLeaderId());
        return node.getLeaderId();
    }

    // 召回
    public void stationRecall(Role role, long id) {
        if (!functionSwitchService.isOpen(FunctionType.REGION_CAPITAL.getId(), role)) {
            logger.info("stationRecall switch close roleId {} ", role.getRoleId());
            return;
        }

        RegionCapitalNode node = regionCapitalNodeDao.findById(id);
        if (node == null) {
            ErrorLogUtil.errorLog("stationRecall node not find", "nodeId", id);
            return;
        }

        var armyCamps = sceneService.getNodeArriveArmies(node);
        ArmyInfo targetArmy = null;
        for (var armyCamp : armyCamps) {
            if (armyCamp.getRoleId().equals(role.getRoleId())) {
                targetArmy = armyCamp;
                break;
            }
        }

        if (null == targetArmy) {
            ErrorLogUtil.errorLog("stationRecall targetArmy isn't find or not arrive", "roleId", role.getRoleId(), "nodeId", id, "metaId", node.getMetaId());
            return;
        }

        stationRecall(targetArmy);
    }

    // 找回某个城池中的所有部队
    public void removeDefenceArmies(RegionCapitalNode node) {
        var armies = sceneService.getNodeArriveArmies(node);
        for (var army : armies) {
            if (army.getArmyType().isRegionCapitalAtk()) {
                armyManager.returnArmy(army);
            }
        }
        regionCapitalNodeDao.save(node);
    }

    // 解散该节点的集结及行军
    public void disbandArmy(RegionCapitalNode node) {
        var armies = sceneService.getNodeArmies(node);
        for (var army : armies) {
            if (army.getArmyType().isRegionCapitalAtk()) {
                armyManager.returnArmyImmediately(army);
            }
        }
        regionCapitalNodeDao.save(node);
    }

    // 召回
    public void stationRecall(ArmyInfo army) {
        Role role = army.getOwner();
        SceneNode snode = sceneService.getSceneNode(army.getCurrentServerId(), army.getEndPoint());
        if (!(snode instanceof RegionCapitalNode node)) {
            ErrorLogUtil.errorLog("stationRecall node not find", "node", army.getEndPoint());
            return;
        }

        armyManager.returnArmy(army);

        biLogUtil.regioncapitalRecall(role, node.getMetaId(), role.getAllianceId(), army.getRoleId());
        logger.info("stationRecall armyId:{} role:{} alliance:{} node:{} metaId:{}.",
                army.getPersistKey(), role.getRoleId(), role.getAllianceId(), node.getPersistKey(), node.getMetaId());

        // 通知
        siegeEnginesServiceImpl.regionCapitalGarrisonUpdate(node);
    }

    // 遣返
    public void stationReturn(Role role, long id, long armyId) {
        if (!functionSwitchService.isOpen(FunctionType.REGION_CAPITAL.getId(), role)) {
            logger.info("stationReturn switch close roleId {} ", role.getRoleId());
            return;
        }

        RegionCapitalNode node = regionCapitalNodeDao.findById(id);
        if (node == null) {
            ErrorLogUtil.errorLog("stationReturn node not find", "node", id);
            return;
        }

        var leaderArmy = armyDao.findById(node.getLeaderId());
        // 权限判断
        Long allianceId = role.getAllianceId();
        if ((null == leaderArmy || !leaderArmy.getRoleId().equals(role.getRoleId())) && !allianceManager.checkPermission(Auth.GVG_STATION_SET_ORDER, role.getId())) {
            ErrorLogUtil.errorLog("stationReturn isn't leader or permission", "roleId", role.getRoleId(), "allianceId", allianceId);
            return;
        }

        if (!Objects.equals(node.getBelongAllianceId(), allianceId)) {
            ErrorLogUtil.errorLog("stationReturn isn't belong or temp", "node", id, "meta", node.getMetaId(),
                    "role", role.getId(), "alliance", allianceId);
            return;
        }

        var armyCamps = sceneService.getNodeArriveArmies(node);
        ArmyInfo targetArmy = null;
        for (var army : armyCamps) {
            if (army.getPersistKey() == armyId) {
                targetArmy = army;
                break;
            }
        }

        if (null == targetArmy) {
            ErrorLogUtil.errorLog("stationReturn isn't find or not arrive", "roleId", role.getRoleId(),
                    "allianceId", allianceId, "armyId", armyId, "nodeId", id, "metaId", node.getMetaId());
            return;
        }

        armyManager.returnArmy(targetArmy);

        biLogUtil.regioncapitalReturn(role, node.getMetaId(), role.getAllianceId(), targetArmy.getRoleId());
        logger.info("stationReturn armyId:{} role:{} alliance:{} node:{} metaId:{} armyRole:{}", targetArmy.getPersistKey(),
                role.getRoleId(), role.getAllianceId(), node.getPersistKey(), node.getMetaId(), targetArmy.getRoleId());
    }

    // 部队返回逻辑
    public void onStationReturn(RegionCapitalNode node, ArmyInfo army) {
        Role role = army.getOwner();
        if (null == role) {
            return;
        }

        // 队长可能发生变化
        resetStationOrder(node, role.getAllianceId());
        // 驻防信息发生变化
        allianceWarService.broadcastUpdate(node, PsAllianceWarInfoUpdateReason.STATION_RECALL);

        // 结算功勋
        royalHonorArmyAdd(node, army, true);

        // aoi通知 具体信息发生了变化
        sceneService.update(node, null);

        regionCapitalNodeDao.save(node);
    }

    // 报名成功邮件
    public void enrollEmail(Alliance alliance, RegionCapitalPositionConfig.RegionCapitalPositionMeta nodeMeta, RegionCapitalNode node, String pvpIndex) {
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        String applyMailId = rcc.getApplyMailId();
        int serverId = alliance.getCurrentServerId();
        List<AbstractEmail> mailList = new ArrayList<>();
        var members = allianceMemberManager.getMembers(alliance.getId());
        for (var member : members) {
            var systemMail = mailCreator.createRegionCapitalApplyMail(member.getPersistKey(), serverId, applyMailId, nodeMeta.getName(),
                    nodeMeta.getLevel(), alliance.getRegionCapitalWarPlan(), nodeMeta.getCenterPoint(), node.getCurrentServerId(), pvpIndex);
            mailList.add(systemMail);
        }

        // 被宣战方
        if (node.getBelongAllianceId() > 0) {
            String defenseMailID = rcc.getDefenseMailID();
            var otherMembers = allianceMemberManager.getMembers(node.getBelongAllianceId());
            for (var member : otherMembers) {
                var systemMail = mailCreator.createRegionCapitalBeApplyMail(member.getPersistKey(), serverId, defenseMailID,
                        alliance.getAliasName(), nodeMeta.getName(), nodeMeta.getCenterPoint(), node.getCurrentServerId(), pvpIndex);
                mailList.add(systemMail);
            }
        }
        mailSender.sendBatchMail(mailList);
    }

    // 放弃成功邮件
    public void giveupEmail(Alliance alliance, RegionCapitalNode node) {
        var meta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
        if (null == meta) {
            return;
        }

        var rcc = configService.getConfig(RegionCapitalConfig.class);
        String giveupMailId = rcc.getGiveupMailId();
        int serverId = alliance.getCurrentServerId();
        List<AbstractEmail> mailList = new ArrayList<>();
        var members = allianceMemberManager.getMembers(alliance.getId());
        for (var member : members) {
            var systemMail = mailCreator.createRegionCapitalGiveupMail(member.getPersistKey(), serverId, giveupMailId, meta.getLevel(), meta.getName());
            mailList.add(systemMail);
        }
        mailSender.sendBatchMail(mailList);
    }

    // 获取体力消耗
    public int getStaminaCost(RegionCapitalNode node) {
        if (node.getOccupyCount() > 0) {
            // 被占领过,不扣体力
            return 0;
        }

        var rcpConfig = configService.getConfig(RegionCapitalPositionConfig.class);
        var capitalMeta = rcpConfig.get(node.getMetaId());
        if (null == capitalMeta) {
            return 0;
        }

        var npcConfig = configService.getConfig(NpcConfig.class);
        if (!node.isBossDeadAll(capitalMeta)) {
            NpcConfig.NpcMeta npcMeta = npcConfig.get(capitalMeta.getBossNpc());
            if (null == npcMeta) {
                return 0;
            }

            return npcMeta.getSG_cost();
        }

        NpcConfig.NpcMeta npcMeta = npcConfig.get(capitalMeta.getNpc());
        if (null == npcMeta) {
            return 0;
        }

        return npcMeta.getSG_cost();
    }

    // 开启
    public void GmOpen(Role role, int serverId, Long allianceId, String metaId, int openTime) {
        // 没有联盟
        if (null == allianceId) {
            ErrorLogUtil.errorLog("GmOpen allianceId is null", "metaId", metaId);
            return;
        }

        var node = regionCapitalNodeDao.getNodeByMetaId(serverId, metaId);
        if (null == node) {
            ErrorLogUtil.errorLog("GmOpen node is null", "allianceId", allianceId, "metaId", metaId);
            return;
        }

        long now = TimeUtil.getNow();
        int dayOfHour = TimeUtil.getHour(now);
        node.setEndTime(now + openTime * TimeUtil.MINUTE_MILLIS);
        RegionCapitalConfig rcc = configService.getConfig(RegionCapitalConfig.class);
        // 王城
        if (PsRegionCapitalType.ROYAL != node.getType()) {
            rcc.fixPvpStart(dayOfHour * TimeUtil.HOUR_MILLIS, Math.min(24 - dayOfHour, 4) * TimeUtil.HOUR_MILLIS);

            // 没有报名该州府
            Integer pvpIndex = node.getDeclareWarList().get(allianceId);
            if (null == pvpIndex) {
                ErrorLogUtil.errorLog("GmOpen isn't apply", "allianceId", allianceId, "metaId", metaId);
                return;
            }

            rcc.fixDeclarePVPTimes(pvpIndex);
        } else {
            int dayWeek = TimeUtil.getDayOfWeek(now);
            rcc.fixKingCityOpenTime(dayWeek, dayOfHour, Math.min(24 - dayOfHour, 3));
        }

        // 同步客户端这个配置
        sendConfig(role);

        logger.info("GmOpen metaId:{} allianceId:{}", metaId, allianceId);
    }

    public void GmPveCount(int serverId, int bossNum, int npcNum) {
        if (bossNum <= 0) {
            bossNum = 0;
            if (npcNum <= 0) {
                return;
            }
        }

        var rcpConfig = configService.getConfig(RegionCapitalPositionConfig.class);
        var nodeList = regionCapitalNodeDao.findByCurrentServerId(serverId);
        for (var node : nodeList) {
            var meta = rcpConfig.get(node.getMetaId());
            int deadCount = meta.getBossNum(node.getBelongAllianceId() > 0) - bossNum;
            if (deadCount < 0) deadCount = 0;
            node.setBossDeadCount(deadCount);
            deadCount = meta.getNpcNum(node.getBelongAllianceId() > 0) - npcNum;
            if (deadCount < 0) deadCount = 0;
            node.setMonsterDeadCount(deadCount);
            regionCapitalNodeDao.save(node);
        }
    }

    public void GmApplyLimit(Role role, int limit, String metaId) {
        var config = configService.getConfig(RegionCapitalPositionConfig.class);
        if (JavaUtils.bool(metaId)) {
            var meta = config.get(metaId);
            if (null == meta) {
                return;
            }

            meta.setAllianceLimit(limit);
        } else {
            for (var entry : config.getMetaMap().entrySet()) {
                entry.getValue().setAllianceLimit(limit);
            }
        }

        // 同步客户端这个配置
        sendConfig(role);

        logger.info("metaId:{}, limit:{}", metaId, limit);
    }

    public void GmApply(int serverId, String metaId, Long allianceId, int pvpIndex) {
        if (pvpIndex <= 0) pvpIndex = 1;
        if (null == allianceId || allianceId <= 0) {
            ErrorLogUtil.errorLog("apply alliance not", "allianceId", allianceId);
            return;
        }

        // 联盟是否存在
        var alliance = allianceManager.getAllianceById(allianceId);
        if (null == alliance) {
            ErrorLogUtil.errorLog("apply alliance not find", "allianceId", allianceId);
            return;
        }

        // 州府是否存在
        var capital = regionCapitalNodeDao.getNodeByMetaId(serverId, metaId);
        if (null == capital) {
            ErrorLogUtil.errorLog("apply metaId isn't find", "metaId", metaId);
            return;
        }

        var rcpConfig = configService.getConfig(RegionCapitalPositionConfig.class);
        var meta = rcpConfig.get(capital.getMetaId());
        if (null == meta) {
            ErrorLogUtil.errorLog("apply capital meta isn't find", "metaId", metaId);
            return;
        }

        // 已经报名
        if (capital.getDeclareWarList().containsKey(allianceId)) {
            ErrorLogUtil.errorLog("apply capital meta is enrolled", "capital", capital.getPersistKey(), "metaId", meta.getId());
            return;
        }

        // 有报名的州府先取消
        regionCapitalNodeDao.applyRemove(allianceId);
        // 报名新的
        regionCapitalNodeDao.applyAdd(allianceId, capital.getPersistKey(), pvpIndex);
        // 联盟事务
        updateAffair(capital, allianceId, pvpIndex);
        // 同步信息
        noticeChange(capital);

        // 向本盟广播
        allianceManager.broadcast(allianceId, wrapApplyList(allianceId));

        biLogUtil.regioncapitalApply(null, meta.getId(), allianceId, pvpIndex);

        // 处理联盟宣战标志
        updateAllianceInWarFlag(allianceId, capital, true);
    }

    // 设置联盟归属
    public void GMBelong(int serverId, Long allianceId, String metaId, boolean isTemp) {
        var alliance = allianceDao.findById(allianceId);
        if (null == alliance) {
            return;
        }

        var capital = regionCapitalNodeDao.getNodeByMetaId(serverId, metaId);
        if (null == capital) {
            return;
        }

        if (isTemp) {
            // 只设置临时归属,不切换Dao里的映射关系
            capital.setBelongAllianceId(allianceId);
            capital.setOccupyStartTime(TimeUtil.getNow());
            var rcconfig = configService.getConfig(RegionCapitalConfig.class);
            // 王城设置持续时间
            capital.setOccupyDuration(rcconfig.getKingCityOccupyTime());
        } else {
            if (PsRegionCapitalType.ROYAL == capital.getType()) {
                capital.setBelongLastAllianceId(allianceId);
                royalFinish(capital, TimeUtil.getNow());
            } else {
                normalFinish(capital, alliance);
            }

            milestoneService.onMilestoneEventTrigger(MilestoneType.CITY_OCCUPIED_LEVEL, allianceId);
        }

        regionCapitalNodeDao.save(capital);
        // 同步信息
        noticeChange(capital);

        logger.info("GMBelong capital:{} meta:{} status:{} allianceId:{} isTemp:{}", capital.getPersistKey(), metaId, capital.getStatus(), allianceId, isTemp);
    }

    public void GmApplyCount(long roleId, int count) {
        var role = roleDao.findById(roleId);
        if (null == role) {
            return;
        }

        Alliance alliance = allianceDao.findById(role.getAllianceId());
        if (null == alliance) {
            return;
        }

        alliance.setRegionCapitalApplyCount(count);

        logger.info("GmApplyCount role:{}, allianceId:{}, count:{}", roleId, role.getAllianceId(), count);
    }

    // 聊天调试
    public void GMChatOccupy(long roleId, String metaId) {
        var role = roleDao.findById(roleId);
        if (null == role) {
            return;
        }

        Alliance alliance = allianceDao.findById(role.getAllianceId());
        if (null == alliance) {
            return;
        }

        var capital = regionCapitalNodeDao.getNodeByMetaId(alliance.getCurrentServerId(), metaId);
        if (null == capital) {
            capital = regionCapitalNodeDao.getNodeByMetaId(Application.getServerId(), metaId);
            if (null == capital) {
                return;
            }
        }

        regionCapitalChatExport.onOccupy(alliance, capital);

        logger.info("GMChatOccupy capital:{} metaId:{} allianceId:{}", capital.getPersistKey(), metaId, role.getAllianceId());
    }

    // 州府占领时积分排行
    public void belongScoreRank(Role role, String metaId, String ext) {
        // 州府是否存在
        var capital = regionCapitalNodeDao.getNodeByMetaId(role.getCurrentServerId(), metaId);
        if (null == capital) {
            ErrorLogUtil.errorLog("belongScoreRank capital isn't find", "metaId", metaId);
            return;
        }

        // 是否属于本联盟
        if (!Objects.equals(capital.getBelongAllianceId(), role.getAllianceId())) {
            ErrorLogUtil.errorLog("belongScoreRank capital metaId belong isn't same", "capital", capital.getPersistKey(), "metaId", capital.getMetaId(), "belong", capital.getBelongAllianceId(), "allianceId", role.getAllianceId());
            return;
        }

        GcRankList msg = new GcRankList();
        msg.setType(PsRankType.REGION_CAPITAL_PVE);
        msg.setExt(ext);
        int rank = 1;
        for (var item : capital.getBelongRolePveScore()) {
            PsRankMember rankMember = new PsRankMember(rank, item.getSecond());
            long roleId = item.getFirst();
            Role memberRole = roleDao.findById(roleId);
            if (null == memberRole) {
                continue;
            }
            if (role.getRoleId().equals(memberRole.getRoleId())) {
                msg.setOwnerRank(rank);
                msg.setOwnerValue(item.getSecond());
            }

            rankMember.setRoleId(memberRole.getPersistKey());
            rankMember.setName(memberRole.getName());
            rankMember.setHead(memberRole.getHead());
            rankMember.setRoleInfo(memberRole.toPsRoleInfo());
            rankMember.setSex((byte) memberRole.getSex());
            rankMember.setServerId(memberRole.getoServerId());
            rankMember.setFightPower(memberRole.getFightPower());
            if (memberRole.getAllianceId() != null) {
                rankMember.setAllianceId(memberRole.getAllianceId());
            }
            //公会信息
            Alliance alliance = allianceDao.findById(memberRole.getAllianceId());
            if (alliance != null) {
                rankMember.setAllianceName(alliance.getName());
                rankMember.setAllianceAlias(alliance.getAliasName());
                rankMember.setFlagInfo(alliance.toPsAllianceFlagInfo());
            }
            msg.addToMembers(rankMember);
            rank++;
        }

        role.send(msg);
    }

    // 获得占领历史记录
    public List<PsRegionCapitalBelong> getBelongHistory(long capitalId) {
        List<PsRegionCapitalBelong> belongList = new ArrayList<>();
        // 州府是否存在
        var capital = regionCapitalNodeDao.findById(capitalId);
        if (null == capital) {
            ErrorLogUtil.errorLog("capital isn't find", "capital", capitalId);
            return belongList;
        }

        for (var entry : capital.getBelongLogs()) {
            belongList.add(entry.toInfo());
        }

        return belongList;
    }

    // 炮台历史记录
    public void artilleryHistory(Role role, long capitalId) {
        // 州府是否存在
        var targetCapital = regionCapitalNodeDao.findById(capitalId);
        if (null == targetCapital) {
            ErrorLogUtil.errorLog("artilleryHistory isn't find", "roleId", role.getRoleId(), "nodeId", capitalId);
            return;
        }

        GcRegionCapitalArtilleryHistory msg = new GcRegionCapitalArtilleryHistory();
        for (var entry : targetCapital.getArtilleryRecord()) {
            msg.addToOptList(entry.toInfo());
        }
        role.send(msg);
    }

    // 领取tips奖励
    public void tipsReward(Role role, Integer index) {
        if (!regionCapitalHandler.isRoleActivityOpen(role)) {
            return;
        }
        var roleRegCap = roleRegionCapitalDao.findById(role.getRoleId());
        Set<Integer> tipsRewards = roleRegCap.getTipsRewards();
        // 返回已经领取信息
        if (null == index || index <= 0) {
            GcRoleRegionCapitalTips msg = new GcRoleRegionCapitalTips();
            tipsRewards.forEach(msg::addToTipIndexs);
            role.send(msg);
            return;
        }

        if (tipsRewards.contains(index)) {
            ErrorLogUtil.errorLog("tipsReward role index is rewared", "roleId", role.getRoleId(), "index", index);
            return;
        }

        var rcc = configService.getConfig(RegionCapitalConfig.class);
        if (index > rcc.getPageReward().size()) {
            ErrorLogUtil.errorLog("tipsReward role index is over", "roleId", role.getRoleId(), "index", index);
            return;
        }

        List<SimpleItem> items = dropService.drop(rcc.getPageReward().get(index - 1));
        // 添加物品
        srvDep.getItemService().give(role, items, LogReasons.ItemLogReason.REGION_CAPITAL_TIPS_REWARD);

        roleRegCap.getTipsRewards().add(index);
        roleRegionCapitalDao.save(roleRegCap);

        GcRoleRegionCapitalTips msg = new GcRoleRegionCapitalTips();
        roleRegCap.getTipsRewards().forEach(msg::addToTipIndexs);
        items.forEach(o -> msg.addToRewards(o.toPsObject()));
        role.send(msg);
    }

    // 生成邮件头
    public String mailTitle(ArmyInfo attackerMainArmy, boolean isPvp, boolean isAck) {
        RegionCapitalNode capitalNode = (RegionCapitalNode) attackerMainArmy.getTargetNode();
        if (null == capitalNode) {
            return "";
        }

        var capitalName = "@" + capitalNode.getMetaName() + "@";
        FightContext fightContext = attackerMainArmy.getFightContext();
        List<String> params = Lists.newArrayList();
        //pvp部分
        if (isPvp) {
            Long atkMainId = fightContext.getAttacker().getMainRoleId();
            Long defMainId = fightContext.getDefender().getMainRoleId();
            Role atkMainRole = roleDao.findById(atkMainId);
            Role defMainRole = roleDao.findById(defMainId);
            Alliance defAlliance = allianceDao.findById(defMainRole.getAllianceId());

            if (attackerMainArmy.getRallyContext() != null) {
                if (fightContext.isWin()) {
                    //pvp州府战集结攻方胜利
                    if (isAck) {
                        //进攻方
                        if (null != defAlliance) {
                            params.add(defAlliance.getAliasName());
                            params.add(capitalName);
                        }
                        return MailUtils.buildTitle("ui/images/mail/mail_icon5", "MAIL_PVP_JIJIE_ATTACK_WIN_TITLE",
                                "MAIL_PVP_JIJIE_ATTACK_WIN_SUBTITLE_2", params);
                    }
                    //防守失败
                    params.add(atkMainRole.getName());
                    params.add(capitalName);
                    return MailUtils.buildTitle("ui/images/mail/mail_icon8", "MAIL_PVP_JIJIE_DEFENCE_LOSE_TITLE",
                            "MAIL_PVP_DEFENCE_LOSE_SUBTITLE_2", params);
                }
                //pvp州府战集结攻方失败
                if (isAck) {
                    //进攻方
                    if (null != defAlliance) {
                        params.add(defAlliance.getAliasName());
                        params.add(capitalName);
                    }
                    return MailUtils.buildTitle("ui/images/mail/mail_icon6", "MAIL_PVP_JIJIE_ATTACK_LOSE_TITLE",
                            "MAIL_PVP_JIJIE_ATTACK_LOSE_SUBTITLE_2", params);
                }
                //防守胜利
                params.add(atkMainRole.getName());
                params.add(capitalName);
                return MailUtils.buildTitle("ui/images/mail/mail_icon7", "MAIL_PVP_JIJIE_DEFENCE_WIN_TITLE",
                        "MAIL_PVP_DEFENCE_WIN_SUBTITLE_2", params);
            }
            if (fightContext.isWin()) {
                //pvp州府战单体攻方胜利
                if (isAck) {
                    //进攻方
                    if (null != defAlliance) {
                        params.add(defAlliance.getName());
                    }
                    return MailUtils.buildTitle("ui/images/mail/mail_icon5", "MAIL_PVP_ATTACK_WIN_TITLE",
                            "MAIL_PVP_ATTACK_WIN_SUBTITLE_2", params);
                }
                //防守方
                params.add(atkMainRole.getName());
                params.add(capitalName);
                return MailUtils.buildTitle("ui/images/mail/mail_icon8", "MAIL_PVP_DEFENCE_LOSE_TITLE",
                        "MAIL_PVP_DEFENCE_LOSE_SUBTITLE_2", params);
            }
            //pvp州府战单体攻方失败
            if (isAck) {
                //进攻方
                if (null != defAlliance) {
                    params.add(defAlliance.getName());
                }
                return MailUtils.buildTitle("ui/images/mail/mail_icon6", "MAIL_PVP_ATTACK_LOSE_TITLE",
                        "MAIL_PVP_ATTACK_LOSE_SUBTITLE_2", params);
            }
            //防守方
            params.add(atkMainRole.getName());
            params.add(capitalName);
            return MailUtils.buildTitle("ui/images/mail/mail_icon7", "MAIL_PVP_DEFENCE_WIN_TITLE",
                    "MAIL_PVP_DEFENCE_WIN_SUBTITLE_2", params);
        }

        //pve部分
        if (attackerMainArmy.getRallyContext() != null) {
            if (fightContext.isWin()) {
                //pve州府战集结攻方胜利
                if (isAck) {
                    params.add(capitalName);
                    return MailUtils.buildTitle("ui/images/building/building_citywall_1_4", "MAIL_JIJIE_WIN_TITLE",
                            "MAIL_JIJIE_WIN_SUBTITLE_2", params);
                }
            }
            //pve州府战集结攻方失败
            if (isAck) {
                params.add(capitalName);
                return MailUtils.buildTitle("ui/images/building/building_citywall_1_4", "MAIL_JIJIE_LOSE_TITLE",
                        "MAIL_JIJIE_LOSE_SUBTITLE_2", params);
            }
        }

        //pve州府战单体攻方失败
        if (fightContext.isWin()) {
            if (isAck) {
                params.add(capitalName);
                return MailUtils.buildTitle("ui/images/building/building_citywall_1_4", "MAIL_PVE_WIN_TITLE",
                        "MAIL_PVE_WIN_SUBTITLE_2", params);
            }
        }

        //pve州府战单体攻方失败
        if (isAck) {
            params.add(capitalName);
            return MailUtils.buildTitle("ui/images/building/building_citywall_1_4", "MAIL_PVE_LOSE_TITLE",
                    "MAIL_PVE_LOSE_SUBTITLE_2", params);
        }

        return "";
    }

    // 决战王城功勋计算
    public void royalHonorAdd(SceneNode node, Role role, long score) {
        if (!(node instanceof RegionCapitalNode capitalNode)) {
            return;
        }

        if (PsRegionCapitalType.ROYAL != capitalNode.getType() && PsRegionCapitalType.ARTILLERY != capitalNode.getType()) {
            return;
        }

        try {
            var meta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
            royalActivityManager.calculateHonorScore(role, score, meta.getMetaServerType(), node);
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("calculateHonorScore error", e);
        }
    }

    // pve时Honor积分增加
    public void pveHonorTrigger(ArmyInfo armyInfo) {
        RegionCapitalNode node = (RegionCapitalNode) armyInfo.getTargetNode();
        if (null == node) {
            return;
        }

        var meta = node.getNpcMeta();
        if (meta == null) {
            return;
        }

        // 参与玩家
        List<ArmyInfo> joinArmyList = new ArrayList<>();
        joinArmyList.add(armyInfo);
        RallyContext rallyCtx = armyInfo.getRallyContext();
        if (rallyCtx != null) {
            for (Long joinArmyId : rallyCtx.getJoinArmyIdList()) {
                var joinArmy = armyManager.findById(joinArmyId);
                if (null == joinArmy) {
                    continue;
                }

                joinArmyList.add(joinArmy);
            }
        }

        var fightResult = armyInfo.getFightContext().getFightResult();
        Long allianceId = armyInfo.getOwner().getAllianceId();

        for (var army : joinArmyList) {
            long score = 0;
            Long roleId = army.getRoleId();
            var soldiers = fightResult.getKillCount(true, false, army.getRoleId().toString());
            if (null != soldiers) {
                for (var soldier : soldiers.entrySet()) {
                    var soldierMeta = SoldierConfig.getSoldierMeta(soldier.getKey());
                    if (soldierMeta != null) {
                        score += kvkHonorService.trigger(
                                army.getOwner(),
                                HonorType.KILL_CAPITAL_PVE,
                                soldierMeta.getEra(),
                                0,
                                soldier.getValue());
                    }
                }

                soldiers = fightResult.getKillCount(true, true, army.getRoleId().toString());
                if (null != soldiers) {
                    for (var soldier : soldiers.entrySet()) {
                        var soldierMeta = SoldierConfig.getSoldierMeta(soldier.getKey());
                        if (soldierMeta != null) {
                            score += kvkHonorService.trigger(army.getOwner(), HonorType.BE_KILLED_IN_CAPITAL,
                                    soldierMeta.getEra(), 0, soldier.getValue());
                        }
                    }
                }

                // 统计
                if (allianceId != null) {
                    regionCapitalRecordService.addRecordScore(allianceId, roleId, node.getoServerId(), node.getMetaId(),
                            RegionCapitalRecordType.HONOR, node.getEndTime(), score);
                }

                // 决战王城
                royalHonorAdd(node, army.getOwner(), score);
            }
        }
    }

    // pve时Honor积分增加
    public void pvpHonorTrigger(ArmyInfo armyInfo) {
        RegionCapitalNode node = (RegionCapitalNode) armyInfo.getTargetNode();
        if (null == node) {
            return;
        }

        // 进攻方参与玩家
        List<ArmyInfo> joinArmyList = new ArrayList<>();
        joinArmyList.add(armyInfo);
        RallyContext rallyCtx = armyInfo.getRallyContext();
        if (rallyCtx != null) {
            for (Long joinArmyId : rallyCtx.getJoinArmyIdList()) {
                var joinArmy = armyManager.findById(joinArmyId);
                if (null == joinArmy) {
                    continue;
                }

                joinArmyList.add(joinArmy);
            }
        }
        Long allianceId = armyInfo.getOwner().getAllianceId();

        var fightResult = armyInfo.getFightContext().getFightResult();
        for (var army : joinArmyList) {
            Long roleId = army.getRoleId();
            long score = 0;
            var soldiers = fightResult.getKillCount(true, false, army.getRoleId().toString());
            if (null != soldiers) {
                for (var soldier : soldiers.entrySet()) {
                    var soldierMeta = SoldierConfig.getSoldierMeta(soldier.getKey());
                    if (soldierMeta != null) {
                        score += kvkHonorService.trigger(army.getOwner(), HonorType.KILL_CAPITAL_PVP,
                                soldierMeta.getEra(), 0, soldier.getValue());
                    }
                }
            }

            soldiers = fightResult.getKillCount(true, true, army.getRoleId().toString());
            if (null != soldiers) {
                for (var soldier : soldiers.entrySet()) {
                    var soldierMeta = SoldierConfig.getSoldierMeta(soldier.getKey());
                    if (soldierMeta != null) {
                        score += kvkHonorService.trigger(army.getOwner(), HonorType.BE_KILLED_IN_CAPITAL,
                                soldierMeta.getEra(), 0, soldier.getValue());
                    }
                }
            }

            // 统计
            if (allianceId != null) {
                regionCapitalRecordService.addRecordScore(allianceId, roleId, node.getoServerId(), node.getMetaId(),
                        RegionCapitalRecordType.HONOR, node.getEndTime(), score);
            }

            // 决战王城
            royalHonorAdd(node, army.getOwner(), score);
        }

        // 防御方参与玩家
        var arriveArmys = sceneService.getNodeArriveArmies(node);
        for (var army : arriveArmys) {
            Role role = srvDep.getRoleDao().findById(army.getRoleId());
            if (null == role) {
                continue;
            }
            long score = 0;
            Long roleId = role.getRoleId();
            Long armyAllianceId = role.getAllianceId();

            var soldiers = fightResult.getKillCount(false, false, String.valueOf(army.getRoleId()));
            if (null != soldiers) {
                for (var soldier : soldiers.entrySet()) {
                    var soldierMeta = SoldierConfig.getSoldierMeta(soldier.getKey());
                    if (soldierMeta != null) {
                        score += kvkHonorService.trigger(role, HonorType.KILL_CAPITAL_PVP, soldierMeta.getEra(), 0, soldier.getValue());
                    }
                }
            }

            soldiers = fightResult.getKillCount(false, true, String.valueOf(army.getRoleId()));
            if (null != soldiers) {
                for (var soldier : soldiers.entrySet()) {
                    var soldierMeta = SoldierConfig.getSoldierMeta(soldier.getKey());
                    if (soldierMeta != null) {
                        score += kvkHonorService.trigger(role, HonorType.BE_KILLED_IN_CAPITAL, soldierMeta.getEra(), 0, soldier.getValue());
                    }
                }
            }
            // 统计
            if (armyAllianceId != null) {
                regionCapitalRecordService.addRecordScore(armyAllianceId, roleId, node.getoServerId(), node.getMetaId(),
                        RegionCapitalRecordType.HONOR, node.getEndTime(), score);
            }

            // 决战王城
            royalHonorAdd(node, army.getOwner(), score);
        }
    }

    // 持续占领王城和炮台增加功勋
    // isTick tick是种保底计算,一般争夺激烈的城池基本用不上
    // isClear 计算完后清除
    public void royalHonorAdd(SceneNode node, boolean isTick, boolean isClear) {
        if (!(node instanceof RegionCapitalNode capitalNode)) {
            return;
        }

        if (PsRegionCapitalType.ROYAL != capitalNode.getType() && PsRegionCapitalType.ARTILLERY != capitalNode.getType()) {
            return;
        }

        if (capitalNode.getStatus() != PsRegionCapitalState.PVP) {
            return;
        }

        // 频率计算
        long now = TimeUtil.getNow();
        // 清除时,强制结算一次
        if (isTick && !isClear) {
            // 每分钟一次
            if (capitalNode.getHonorLastTime() + TimeUtil.MINUTE_MILLIS > now) {
                return;
            }
        }

        capitalNode.setHonorLastTime(now);
        regionCapitalNodeDao.save(capitalNode);

        // 防守方
        var defArmys = sceneService.getNodeArriveArmies(node);
        if (!JavaUtils.bool(defArmys)) {
            return;
        }

        for (var army : defArmys) {
            royalHonorArmyAdd(capitalNode, army, isClear);
        }

        // 全部清除
        if (isClear) {
            capitalNode.getRoleHonorLastTime().clear();
        }
    }

    // 某只部队决战王城功勋值
    // isClear 计算完后清除
    public void royalHonorArmyAdd(RegionCapitalNode capitalNode, ArmyInfo army, boolean isClear) {
        if (PsRegionCapitalType.ROYAL != capitalNode.getType() && PsRegionCapitalType.ARTILLERY != capitalNode.getType()) {
            return;
        }

        if (capitalNode.getStatus() != PsRegionCapitalState.PVP) {
            return;
        }

        long now = TimeUtil.getNow();
        Role role = army.getOwner();
        SoldierConfig soldierConfig = configService.getConfig(SoldierConfig.class);
        var honorLastTime = capitalNode.getRoleHonorLastTime();
        Long lastTime = honorLastTime.get(role.getRoleId());
        if (null == lastTime || lastTime <= 0) {
            // 还没有开始计算
            if (!isClear) {
                honorLastTime.put(role.getRoleId(), now);
            }
            return;
        }

        long duraiont = now - lastTime;
        if (duraiont < TimeUtil.SECONDS_MILLIS) {
            // 不足1秒则继续
            return;
        }

        // 取整数秒,余数放回去下次继续
        long secCount = duraiont / TimeUtil.SECONDS_MILLIS;
        honorLastTime.put(role.getRoleId(), now - duraiont % TimeUtil.SECONDS_MILLIS);
        long power = 0;
        for (var soldier : army.getArmySoldiers().values()) {
            SoldierConfig.SoldierMeta soldierMeta = soldierConfig.get(soldier.getSoldierMetaId());
            power += (long) soldierMeta.getPower() * soldier.getCount();
        }

        long arg = (long) (1.0 * power / 1000 * secCount);
        long score = kvkHonorService.trigger(role, HonorType.OCCUPY_CAPITAL_TIME, capitalNode.getLevel(), 0, arg);

        // 决战王城
        royalHonorAdd(capitalNode, army.getOwner(), score);

        // 清除标记
        if (isClear) {
            honorLastTime.remove(role.getRoleId());
        }
    }

    // 触发州府战开启邮件
    public void openMailTips(Role role) {
        RegionCapitalConfig regionCapitalConfig = configService.getConfig(RegionCapitalConfig.class);
        if (role.getLevel() != regionCapitalConfig.getGuideMailFurnaceLevel()) {
            return;
        }

        List<Long> guideDate = regionCapitalConfig.getGuideMailDate();
        long openTime = srvDep.getServerInfoService().getServerOpenTime();
        long startTime = openTime + guideDate.get(0);
        long endTime = openTime + guideDate.get(1);
        long now = TimeUtil.getNow();
        if (startTime > now || endTime < now) {
            return;
        }

        Activity activity = activityDao.findActivityByActivityType(ActivityType.REGION_CAPITAL);
        if (null == activity) {
            return;
        }

        var activityMeta = configService.getConfig(ActivityListConfig.class).getMetaById(activity.getMetaId());
        if (null == activityMeta) {
            return;
        }

        String mailBanner = activityMeta.getMailBanner();
        var mail = mailCreator.createRegionCapitalTips(role.getPersistKey(), role.getCurrentServerId(),
                regionCapitalConfig.getGuideMailID(), List.of(mailBanner));
        mailSender.sendOneMail(mail);
    }

    // 失效联盟处理
    public void unActiveAlliance(Alliance alliance) {
        regionCapitalNodeDao.applyRemove(alliance.getId());
        // 清理领土战相关行军
        List<AllianceMember> fullMembers = allianceMemberManager.getMembers(alliance.getId());
        for (AllianceMember allianceMember : fullMembers) {
            srvDep.getArmyServiceImpl().returnArmys(allianceMember.getPersistKey());
        }

        Collection<RegionCapitalNode> nodes = regionCapitalNodeDao.findByAllianceId(alliance.getId());
        if (!JavaUtils.bool(nodes)) {
            return;
        }

        for (var node : nodes) {
            if (node.getBelongAllianceId() != alliance.getId()) {
                continue;
            }

            // 移除buff
            calcOccupyProp(alliance);
            // 放弃该州府
            regionCapitalNodeDao.changeIndex(node, 0L);
            recalcProsperity(node, List.of(alliance.getId()));
            // 同步信息
            noticeChange(node);
        }

        milestoneService.onMilestoneEventTrigger(MilestoneType.CITY_OCCUPIED_LEVEL, alliance.getId());
    }

    // 处理联盟战争标志
    public void updateAllianceInWarFlag(Long allianceId, RegionCapitalNode node, boolean add) {
        Alliance alliance = allianceDao.findById(allianceId);
        if (alliance == null) {
            return;
        }
        if (add) {
            // 添加，添加的时候需要看被宣战的联盟
            alliance.setCapitalWarFlag(node.getPersistKey());
            if (node.getBelongAllianceId() != 0) {
                Alliance belongAlliance = allianceDao.findById(node.getBelongAllianceId());
                if (belongAlliance != null) {
                    belongAlliance.setCapitalWarFlag(node.getPersistKey());
                    allianceDao.save(belongAlliance);
                }
            }
        } else {
            // 删除，需要查看当前是否有归属联盟，如果宣战列表空了，则需要把归属联盟的标志也取消
            alliance.unsetCapitalWarFlag(node.getPersistKey());
            if (node.getDeclareWarList().isEmpty()) {
                if (node.getBelongAllianceId() != 0) {
                    Alliance belongAlliance = allianceDao.findById(node.getBelongAllianceId());
                    if (belongAlliance != null) {
                        belongAlliance.unsetCapitalWarFlag(node.getPersistKey());
                        allianceDao.save(belongAlliance);
                    }
                }
            }
        }
        allianceDao.save(alliance);
    }

    // 添加事务
    public void updateAffair(RegionCapitalNode node, Long allianceId, int pvpIndex) {
        // 联盟事务
        String metaId = node.getMetaId() + "_" + pvpIndex;
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        var startTime = rcc.getPvpIndexStartTime(TimeUtil.getNow(), pvpIndex);
        allianceAffairService.updateAffair(AllianceAffairType.REGION_CAPITAL_APPLY, allianceId, node.getCurrentServerId(),
                metaId, startTime.getFirst(), node.getX(), node.getY(), startTime.getSecond());
        if (node.getBelongAllianceId() > 0) {
            allianceAffairService.updateAffair(AllianceAffairType.REGION_CAPITAL_BEAPPLY, node.getBelongAllianceId(), node.getCurrentServerId(),
                    metaId, startTime.getFirst(), node.getX(), node.getY(), startTime.getSecond());
        }
    }

    // 取消事务
    public void cancelAffair(RegionCapitalNode node, Long allianceId, int pvpIndex) {
        String metaId = node.getMetaId() + "_" + pvpIndex;
        allianceAffairService.cancelAffair(AllianceAffairType.REGION_CAPITAL_APPLY, allianceId, metaId);
        // 是否为空了
        if (node.getDeclareWarList().isEmpty()) {
            if (node.getBelongAllianceId() > 0) {
                allianceAffairService.cancelAffair(AllianceAffairType.REGION_CAPITAL_BEAPPLY, node.getBelongAllianceId(), metaId);
            }
        }
    }

    // 是否可以参与州府战相关操作
    public boolean canOpt(Role role) {
        // 开关关闭,则返回true
        if (!functionSwitchService.isOpen(FunctionType.ALLIANCE_EXIT_REGION_CAPITAL_LIMIT.getId(), role)) {
            return true;
        }

        // 有该buff则不能参与
        var buffs = buffManager.getBuffs(role.getRoleId(), BuffSourceType.ALLIANCE_EXIT);
        if (JavaUtils.bool(buffs)) {
            return false;
        }

        return true;
    }

    // 判断 玩家城堡 是否在 联盟领地 范围内
    public boolean isInAllianceDomain(Role role) {
        var allianceId = role.getAllianceId();
        var alliance = allianceManager.getAllianceById(allianceId);
        if (alliance == null) return false; // 没有联盟

        RoleCity roleCity = roleCityManager.getRoleCity(role.getPersistKey());
        if (roleCity == null || null == roleCity.getPosition()) return false; // 没有城堡

        var mapData = worldService.getWorld().getMapData(role.getCurrentServerId());
        int roleRegionId = mapData.getGrid(roleCity.getPosition()).getRegionId(); // 玩家所在的 RegionId

        var belongList = regionCapitalNodeDao.findByAllianceId(allianceId);
        for (var belongNode : belongList) {
            int regionId = mapData.getGrid(belongNode.getPosition()).getRegionId();
            if (roleRegionId == regionId) {
                return true;
            }
        }
        return false;
    }

    // 判断 点是否在联盟领地范围内
    public boolean isPointInAllianceDomain(Role role, Point point) {
        var mapData = worldService.getWorld().getMapData(role.getCurrentServerId());
        int roleRegionId = mapData.getGrid(point).getRegionId(); // 玩家所在的 RegionId

        var belongList = regionCapitalNodeDao.findByAllianceId(role.getAllianceId());
        for (var belongNode : belongList) {
            int regionId = mapData.getGrid(belongNode.getPosition()).getRegionId();
            if (roleRegionId == regionId) {
                return true;
            }
        }
        return false;
    }

    // 判断 玩家城堡 是否在 联盟领地 范围内
    public boolean isInAllianceDomain(int serverId, Point point) {
        if (null == point) {
            return false;
        }

        SceneNode node = sceneService.getSceneNode(serverId, point);
        if (!(node instanceof RoleCity roleCity)) {
            return false;
        }

        Role role = roleDao.findById(roleCity.getRoleId());
        if (null == role) {
            return false;
        }

        var allianceId = role.getAllianceId();
        var alliance = allianceManager.getAllianceById(allianceId);
        if (alliance == null) {
            // 没有联盟
            return false;
        }

        var mapData = worldService.getWorld().getMapData(serverId);
        int roleRegionId = mapData.getGrid(point).getRegionId(); // 玩家所在的 RegionId

        var belongList = regionCapitalNodeDao.findByAllianceId(allianceId);
        for (var belongNode : belongList) {
            int regionId = mapData.getGrid(belongNode.getPosition()).getRegionId();
            if (roleRegionId == regionId) {
                return true;
            }
        }

        return false;
    }

    // 攻击方是否在领地内且被攻击方是否和攻击方相邻
    public boolean isInAllianceConnected(int serverId, Point atkPoint, Point defPoint) {
        if (null == atkPoint || null == defPoint) {
            return false;
        }

        SceneNode node = sceneService.getSceneNode(serverId, atkPoint);
        if (!(node instanceof RoleCity roleCity)) {
            return false;
        }

        Role role = roleDao.findById(roleCity.getRoleId());
        if (null == role) {
            return false;
        }

        var allianceId = role.getAllianceId();
        var alliance = allianceManager.getAllianceById(allianceId);
        if (alliance == null) {
            // 没有联盟
            return false;
        }

        var mapData = worldService.getWorld().getMapData(serverId);
        // 玩家所在的 RegionId
        int atkRegionId = mapData.getGrid(atkPoint).getRegionId();
        int defRegionId = mapData.getGrid(defPoint).getRegionId();

        boolean isAtkIn = false, isDefIn = false;
        var belongList = regionCapitalNodeDao.findByAllianceId(allianceId);
        for (var belongNode : belongList) {
            int regionId = mapData.getGrid(belongNode.getPosition()).getRegionId();
            // 进攻方必须在领地内
            if (atkRegionId == regionId) {
                isAtkIn = true;
            }

            // 防守方在我的领地内
            if (regionId == defRegionId) {
                isDefIn = true;
            } else {
                // 防守方和我的领地相邻
                if (mapData.isConnected(regionId, defRegionId)) {
                    isDefIn = true;
                }
            }
        }

        return isAtkIn && isDefIn;
    }

    private void handleBlock(int serverId, List<Point> pointList) {
        var mapData = worldService.getWorld().getMapData(serverId);
        while (!pointList.isEmpty()) {
            var point = pointList.removeFirst();
            var grid = mapData.getGrid(point);
            if (grid == null || grid.getRegionId() > 0) {
                continue;
            }
            pointList.add(point.getXShift(1));
            pointList.add(point.getXShift(-1));
            pointList.add(point.getYShift(1));
            pointList.add(point.getYShift(-1));
            grid.setRegionId(Math.abs(grid.getRegionId()));
            logger.info("阻挡处理 server={} x={} y={} regionId={}", serverId, point.getX(), point.getY(), grid.getRegionId());
        }
    }

    public RegionCapitalNode getRegionCapital(int serverId, int regionId) {
        for (var node : regionCapitalNodeDao.findByCurrentServerId(serverId)) {
            // 炮台不做处理
            if (node.getType() == PsRegionCapitalType.ARTILLERY) {
                continue;
            }
            if (node.getRegionId() == regionId) {
                return node;
            }
        }
        return null;
    }

    /**
     * 主要检查一个玩家打别人的城市时，目标能不能被攻击
     * 目标所在的区域的州府被目标的联盟占领，且进攻方没有对该州府宣战的时候 ，无法进攻
     *
     * @param attacker
     * @param target
     * @return
     */
    public boolean isCanAttack(Role attacker, RoleCity target) {
        if (Application.getServerType() != ServerType.KVK_SEASON || attacker.getCurrentServerId() != Application.getServerId()) {
            // 只有在赛季服且K服上 这条规则才生效
            return true;
        }
        if (target == null) {
            return false;
        }
        var attackAllianceId = attacker.getAllianceId();
        var targetRole = roleDao.findById(target.getRoleId());
        var targetAllianceId = targetRole.getAllianceId();
        if (!JavaUtils.bool(targetAllianceId)) {
            return true;
        }
        var capitalNode = getRegionCapital(target.getCurrentServerId(), target.getRegionId());
        if (capitalNode == null) {
            return true;
        }

        // 该州府不是被目标联盟占领的
        if (!Objects.equals(capitalNode.getBelongAllianceId(), targetAllianceId)) {
            return true;
        }

        // 王城不判断宣战
        if (capitalNode.getType() == PsRegionCapitalType.ROYAL) {
            if (capitalNode.getStatus() == PsRegionCapitalState.PVP) {
                return true;
            }
            return false;
        }

        // 目标在联盟州府保护区，需要宣战才能打
        return attackAllianceId != null && capitalNode.getDeclareWarList().containsKey(attackAllianceId);
    }

    // 黑土地战斗死兵转伤兵比例计算
    public boolean isDead2BadlyRatioByBlack(int serverId, Point point, Long roleId) {
        // 是否在州府战区域
        var rcpc = configService.getConfig(RegionCapitalPositionConfig.class);
        var regMeta = rcpc.isOverlap(serverId, point);
        if (null == regMeta) {
            return false;
        }

        Role role = roleDao.findById(roleId);
        if (null == role) {
            return false;
        }

        RegionCapitalNode capital = regionCapitalNodeDao.getNodeByMetaId(role.getCurrentServerId(), regMeta.getId());
        if (null == capital) {
            return false;
        }

        // 没有联盟,肯定不生效
        if (!JavaUtils.bool(role.getAllianceId())) {
            return false;
        }

        // 该州府是否是已拥有的
        boolean isSuit = Objects.equals(capital.getBelongAllianceId(), role.getAllianceId());
        // 该州府是否是已报名的
        isSuit |= capital.getDeclareWarList().containsKey(role.getAllianceId());
        // 王城,需要判断是否相邻
        if (capital.getType() == PsRegionCapitalType.ROYAL) {
            isSuit |= isAdjacent(role.getAllianceId(), capital);
        }
        return isSuit;
    }

    // 是否在宣战期间
    public boolean isOccupyDuration(Alliance alliance) {
        // 1. 判断当前时间是否正常
        long now = TimeUtil.getNow();
        RegionCapitalConfig rcc = configService.getConfig(RegionCapitalConfig.class);
        // 王城争夺日,是否可以进攻王城
        if (rcc.checkRoyalOccupy(now)) {
            // 是否可以争夺
            var oRoyal = getRoyalNode(alliance.getoServerId());
            var kRoyal = getRoyalNode(Application.getServerId());
            if (!isAdjacent(alliance.getId(), oRoyal) || !isAdjacent(alliance.getId(), kRoyal)) {
                ErrorLogUtil.errorLog("isOccupyDuration able atk royal", "allianceId", alliance.getId());
                return true;
            }
        }

        // 是否在限制时间
        if (!rcc.isAllianceLimitTime(now)) {
            return false;
        }

//        // 2. 判断联盟是否宣战
//        // 已报名列表
//        var applyList = regionCapitalNodeDao.applyList(allianceId);
//        if (null != applyList.get(allianceId)) {
//            return true;
//        }
//        // 3. 查看是否被宣战
//        Collection<RegionCapitalNode> nodes = regionCapitalNodeDao.findByAllianceId(allianceId);
//        if (nodes != null && !nodes.isEmpty()) {
//            for (RegionCapitalNode node : nodes) {
//                if (!node.getDeclareWarList().isEmpty()) {
//                    return true;
//                }
//            }
//        }
        return alliance.isInCapitalWarFlag();
    }

    // 驻防州府处理
    public void onExitAlliance(Long roleId) {
        var armies = armyDao.findByRoleId(roleId);
        if (armies == null) {
            return;
        }
        for (var army : armies) {
            if (army.getArmyType() == ArmyType.STATION_REGION_CAPITAL ||
                    army.getArmyType() == ArmyType.ATTACK_REGION_CAPITAL ||
                    army.getArmyType() == ArmyType.RALLY_REGION_CAPITAL) {
                armyManager.returnArmy(army);
            }
        }
    }

    public PsMapRegionCapital toPsRegionCapitalInfo(RegionCapitalNode node) {
        PsMapRegionCapital info = new PsMapRegionCapital();
        info.setId(node.getPersistKey());
        info.setMetaId(node.getMetaId());
        info.setBelongAllianceId(node.getBelongAllianceId());
        info.setState(node.getStatus());
        info.setEndTime(node.getEndTime());
        info.setGiveupEndTime(node.getGiveupEndTime());
        info.setAutoAppointLeader(Objects.equals(node.getBelongAllianceId(), node.getAutoAppointAllianceId()));

        info.setTroopMax(getStationTroopMaxNum(node));
        info.setSoldierMax(getStationSoldierMaxNum(node));
        info.setStationArmys(stationArmys(node));
        if (node.getBelongAllianceId() > 0) {
            var alliance = allianceManager.getAllianceById(node.getBelongAllianceId());
            if (null != alliance) {
                Role role = roleManager.getRole(alliance.getLeaderId());
                if (null != role) {
                    RoleSimpleInfo roleSimpleInfo = new RoleSimpleInfo(role, null);
                    info.setLeaderInfo(roleSimpleInfo.toPsSimpleInfo());
                }
            }
        }
        for (var entry : node.getDeclareWarList().entrySet()) {
            var pvpIndex = entry.getValue();
            if (pvpIndex <= 0) {
                pvpIndex = 1;
            }
            info.putToApplyIds(entry.getKey().toString(), pvpIndex);
        }

        info.setOccupyCount(node.getOccupyCount());
        info.setBossDeadCount(node.getBossDeadCount());
        info.setMonsterDeadCount(node.getMonsterDeadCount());
        for (var entry : node.getPveScore()) {
            info.addToScoreRank(new PsRegionCapitalScoreItem(entry.getFirst(), entry.getSecond()));
        }

        // 怪物刷新倒计时计算
        long now = TimeUtil.getNow();
        // 通常情况下,取第二天开始时间
        long refreshTime = TimeUtil.getEndOfDay(now);
        if (node.getMonsterResetTime() > 0) {
            // 如果有怪物要刷新,则取两者较小的
            if (refreshTime > node.getMonsterResetTime()) {
                refreshTime = node.getMonsterResetTime();
            }
        }
        info.setMonsterResetTime(refreshTime);

        // 太守
        long governorId = allianceManager.findGovernorByMetaId(node.getBelongAllianceId(), node.getMetaId());
        if (governorId > 0) {
            var role = worldService.getWorld().getRole(governorId);
            if (null != role) {
                info.setGovernorInfo(role.toPsRoleInfo(RoleInfoBitMapEnum.governorShowInfo));
            }
        }

        // 王城/炮台攻击倒计时设置
        if (node.getType() == PsRegionCapitalType.ARTILLERY || node.getType() == PsRegionCapitalType.ROYAL) {
            if (PsRegionCapitalState.PVP == node.getStatus()) {
                if (node.getOccupyStartTime() > 0) {
                    info.setEndTime(node.getOccupyStartTime() + node.getOccupyDuration());
                }
            }
        }
        if (node.getType() == PsRegionCapitalType.ARTILLERY) {
            if (node.getOccupyStartTime() <= 0) {
                info.setArtilleryFireCount(-1);
            } else {
                info.setArtilleryFireCount(node.getArtilleryCount());
            }
        }

        // 普通城池PVP当前pvpIndex
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        Integer pvpIndex = rcc.getPvpIndex(now);
        info.setPvpIndex(null == pvpIndex ? -1 : pvpIndex);

        for (var entry : node.getOccupyAllianceRecord().entrySet()) {
            info.putToOccupyAllianceTime(entry.getKey().toString(), entry.getValue());
        }
        info.setOccupyStartTime(node.getOccupyStartTime());

        // 安全区设置
        Integer safeServerId = kvkSeasonService.getSafeRegionServerId(node.getCurrentServerId(), node.getX(), node.getY());
        if (null != safeServerId) {
            info.setSafeServerId(safeServerId);
        }

        try {
            if (node.getType() == PsRegionCapitalType.COUNTY && node.getStatus() == PsRegionCapitalState.PVP) {
                // 判断当前州府是否处于弹幕需要的交战状态，同时争夺大于2
                List<Long> pvpAlliances = new ArrayList<>();
                if (node.getBelongAllianceId() > 0) {
                    pvpAlliances.add(node.getBelongAllianceId());
                }
                for (var entry : node.getDeclareWarList().entrySet()) {
                    if (Objects.equals(pvpIndex, entry.getValue())) {
                        pvpAlliances.add(entry.getKey());
                    }
                }
                if (pvpAlliances.size() >= 2) {
                    if (node.getDeclareWarOrderMap().values().size() < 2) {
                        resetDeclareWarOrder(node);
                    }
                    info.setIsAtWar(true);
                    Map<String, Integer> map = new HashMap<>();
                    for (Long allianceId : pvpAlliances) {
                        // 需求州府所有者需要是第一个
                        if (allianceId != null && allianceId.equals(node.getBelongAllianceId())) {
                            map.put(String.valueOf(allianceId), 0);
                        } else {
                            map.put(String.valueOf(allianceId), node.getDeclareWarOrderMap().getOrDefault(allianceId, 0));
                        }
                    }
                    info.setAtWarAllianceMap(map);
                    info.setPopularity(node.getPopularity());
                }
            } else if (node.getType() == PsRegionCapitalType.ROYAL && node.getStatus() == PsRegionCapitalState.PVP) {
                info.setIsAtWar(true);
                info.setPopularity(node.getPopularity());
                Map<String, Integer> map = new HashMap<>();
                var neighbours = royalNeighbour(node.getoServerId());
                int i = 0;
                for (var neighbourNode : neighbours) {
                    if (neighbourNode.getBelongAllianceId() > 0) {
                        map.put(String.valueOf(neighbourNode.getBelongAllianceId()), i++);
                    }
                }
                info.setAtWarAllianceMap(map);
            } else {
                info.setIsAtWar(false);
            }
        } catch (ExpectedException ignored) {
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("toPsRegionCapitalInfo error", e);
        }

        return info;
    }

    public PsRegionCapitalSimple toSimpleInfo(RegionCapitalNode node) {
        PsRegionCapitalSimple info = new PsRegionCapitalSimple();
        info.setId(node.getPersistKey());
        info.setMetaId(node.getMetaId());
        info.setBelongAllianceId(node.getBelongAllianceId());
        info.setState(node.getStatus());
        info.setEndTime(node.getEndTime());
        info.setGiveupEndTime(node.getGiveupEndTime());
        info.setServerId(node.getCurrentServerId());
        for (var entry : node.getDeclareWarList().entrySet()) {
            var pvpIndex = entry.getValue();
            if (pvpIndex <= 0) {
                pvpIndex = 1;
            }
            info.putToApplyIds(entry.getKey().toString(), pvpIndex);
        }
        info.setAutoAppointLeader(Objects.equals(node.getBelongAllianceId(), node.getAutoAppointAllianceId()));

        // 太守
        info.setGovernorName(allianceManager.getGovernorName(node.getBelongAllianceId(), node.getMetaId()));

        // 王城/炮台攻击倒计时设置
        if (node.getType() == PsRegionCapitalType.ARTILLERY || node.getType() == PsRegionCapitalType.ROYAL) {
            if (PsRegionCapitalState.PVP == node.getStatus()) {
                if (node.getOccupyStartTime() > 0) {
                    info.setEndTime(node.getOccupyStartTime() + node.getOccupyDuration());
                }
            }
        }

        // 普通城池PVP当前pvpIndex
        var rcc = configService.getConfig(RegionCapitalConfig.class);
        Integer pvpIndex = rcc.getPvpIndex(TimeUtil.getNow());
        info.setPvpIndex(null == pvpIndex ? -1 : pvpIndex);

        for (var entry : node.getOccupyAllianceRecord().entrySet()) {
            info.putToOccupyAllianceTime(entry.getKey().toString(), entry.getValue());
        }
        info.setOccupyStartTime(node.getOccupyStartTime());

        // 安全区设置
        Integer safeServerId = kvkSeasonService.getSafeRegionServerId(node.getCurrentServerId(), node.getX(), node.getY());
        if (null != safeServerId) {
            info.setSafeServerId(safeServerId);
        }

        return info;
    }

    public void reconToRole(RegionCapitalNode node, ReconInfo reconInfo) {
        ReconRoleInfo roleInfo = reconInfo.getReconRoleInfo();
        Alliance alliance = allianceService.getAllianceById(node.getBelongAllianceId());
        if (alliance != null) {
            roleInfo.setAllianceShortName(alliance.getAliasName());
            roleInfo.setAllianceName(alliance.getName());
        }

        long realLeaderId = ableLeader(node);
        ArmyInfo army = armyDao.findById(realLeaderId);
        if (army == null) {
            return;
        }
        Role role = roleManager.getRole(army.getRoleId());
        roleInfo.setRoleId(role.getPersistKey());
        roleInfo.setPlayerName(role.getName());
        roleInfo.setHeadIcon(role.getHead());
        roleInfo.setRoleInfo(role.toRoleInfo());

        ReconArmyInfo reconArmyInfo = new ReconArmyInfo();
        for (var entry : army.getArmySoldiers().entrySet()) {
            reconArmyInfo.getSoldierMap().put(entry.getKey(), entry.getValue().getCount());
        }
        List<Hero> heroList = com.google.api.client.util.Lists.newArrayList();
        for (var entry : army.getHeros()) {
            var hero = srvDep.getHeroService().getHero(role, entry);
            heroList.add(hero);
            reconArmyInfo.getHeroInfoList().add(HeroOutput.toInfo(hero));
        }
        FightProp fightProp = new FightProp();
        fightProp.setRoleProps(role.getNumberProps());
        fightProp.setHeroBuffValues(FightArmy.heroProps(heroList, fightProp));
        reconInfo.setEffectMap(FightUnitPropCalculate.calcReconProp(fightProp, node.getNodeType()));
        reconInfo.setReconArmyInfo(reconArmyInfo);
    }

    public void reconToNpc(RegionCapitalNode node, ReconInfo reconInfo) {
        ReconRoleInfo roleInfo = reconInfo.getReconRoleInfo();
        var capitalMeta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
        NpcConfig.NpcMeta npcMeta = null;
        if (!node.isBossDeadAll(capitalMeta)) {
            // 打boss
            npcMeta = configService.getConfig(NpcConfig.class).get(capitalMeta.getBossNpc());
        } else {
            if (!node.isMonsterDeadAll(capitalMeta)) {
                // 打怪
                npcMeta = configService.getConfig(NpcConfig.class).get(capitalMeta.getNpc());
            }
        }

        // 没有找到npc
        if (null == npcMeta) {
            return;
        }

        roleInfo.setRoleId(Long.parseLong(npcMeta.getId()));
        roleInfo.setPlayerName("@" + npcMeta.getName() + "@");
        roleInfo.setHeadIcon(npcMeta.getIcon());
        roleInfo.setRoleInfo(DefaultRoleInfo.toRoleInfo());

        ReconArmyInfo reconArmyInfo = new ReconArmyInfo();
        for (var entry : npcMeta.getSoldierList()) {
            reconArmyInfo.getSoldierMap().put(entry.getId(), entry.getCount());
        }
        reconInfo.setReconArmyInfo(reconArmyInfo);

        var props = npcMeta.getSlgProps();
        if (null != props) {
            FightProp fightProp = new FightProp();
            for (var entry : props) {
                Prop prop = Prop.findById(entry.getId());
                if (null == prop) {
                    continue;
                }

                fightProp.addRoleProp(prop, entry.getCount() / 10000);
            }
            reconInfo.setEffectMap(FightUnitPropCalculate.calcReconProp(fightProp, node.getNodeType()));
        }
    }

    public void toAllianceWar(RegionCapitalNode capitalNode, PsAllianceWarFightUnit unitInfo) {
        unitInfo.setNodeType(PsAllianceWarNodeType.REGION_CAPITAL);
        unitInfo.setMetaId(capitalNode.getMetaId());

        var armys = sceneService.getNodeArriveArmies(capitalNode);
        unitInfo.setReinforeCapacity(getStationSoldierMaxNum(capitalNode));
        int ret = 0;
        for (var army : armys) {
            ret += army.getSoldierCount();
        }
        unitInfo.setReinforeNum(ret);

        // 填充联盟信息
        unitInfo.setAllianceId(capitalNode.getBelongAllianceId());

        if (!armys.isEmpty()) {
            // 先添加队长
            for (var entry : armys) {
                if (!entry.getPersistKey().equals(capitalNode.getLeaderId())) {
                    continue;
                }

                Role leader = entry.getOwner();
                unitInfo.setRoleId(leader.getPersistKey());
                unitInfo.setName(leader.getName());
                unitInfo.setHead(leader.getHead());
                unitInfo.addToAllMemberRoleId(leader.getPersistKey());
                unitInfo.addToAllMemberHeads(leader.getHead());
                unitInfo.addToAllMemberInfo(leader.toPsRoleInfo());
                break;
            }

            // 再添加其他成员
            for (var entry : armys) {
                if (entry.getPersistKey().equals(capitalNode.getLeaderId())) {
                    continue;
                }

                Role role = roleManager.getRole(entry.getRoleId());
                unitInfo.addToAllMemberHeads(role.getHead());
                unitInfo.addToAllMemberRoleId(role.getPersistKey());
                unitInfo.addToAllMemberInfo(role.toPsRoleInfo());
            }
        }
    }

    public FightArmy createFightArmy(RegionCapitalNode node) {
        var armyCreator = Application.getBean(FightArmyCreator.class);
        var arrives = sceneService.getNodeArriveArmies(node);
        FightArmy defender = null;
        if (node.getType() == PsRegionCapitalType.ROYAL || node.getType() == PsRegionCapitalType.ARTILLERY) {
            var capitalMeta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
            if (!node.isBossDeadAll(capitalMeta)) {
                // 打boss
                defender = armyCreator.createFightArmy(false, capitalMeta.getBossNpc(), SceneNodeType.REGION_CAPITAL, node.getContinuePveCount());
            } else if (!node.isMonsterDeadAll(capitalMeta)) {
                // 打怪
                defender = armyCreator.createFightArmy(false, capitalMeta.getNpc(), SceneNodeType.REGION_CAPITAL, node.getContinuePveCount());
            } else {
                // 一定有人驻防,无人驻防的情况在arrive中判断了
                long realLeaderId = ableLeader(node);
                defender = armyCreator.createFightArmy(arrives, realLeaderId, node.getNodeType());
            }
        } else {
            if (!arrives.isEmpty()) {
                // 有人驻防
                long realLeaderId = ableLeader(node);
                defender = armyCreator.createFightArmy(arrives, realLeaderId, node.getNodeType());
            } else {
                var capitalMeta = configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
                if (node.isBossDeadAll(capitalMeta)) {
                    // 打怪
                    defender = armyCreator.createFightArmy(false, capitalMeta.getNpc(), SceneNodeType.REGION_CAPITAL, node.getContinuePveCount());
                } else {
                    // 打boss
                    defender = armyCreator.createFightArmy(false, capitalMeta.getBossNpc(), SceneNodeType.REGION_CAPITAL, node.getContinuePveCount());
                }
            }
        }

        defender.setMapNodeMetaId(node.getMetaId());
        return defender;
    }

    /**
     * 设置参战联盟顺序，处理弹幕 客户端染色等
     */
    public void resetDeclareWarOrder(RegionCapitalNode node) {
        node.getDeclareWarOrderMap().clear();
        node.setPopularity(0);
        // 获取当前的阶段
        int index = 0;
        if (node.getBelongAllianceId() > 0) {
            node.getDeclareWarOrderMap().put(node.getBelongAllianceId(), index++);
        }
        if (index == 0) {
            index++;
        }
        var rccConfig = configService.getConfig(RegionCapitalConfig.class);
        Integer pvpIndex = rccConfig.getPvpIndex(node.getEndTime());
        // 获取当前的阶段
        for (var entry : node.getDeclareWarList().entrySet()) {
            if (entry.getValue().equals(pvpIndex)) {
                node.getDeclareWarOrderMap().put(entry.getKey(), index++);
            }
        }
    }

    /**
     * 发送弹幕信息
     *
     * @param armyInfo
     * @param node
     */
    public void sendDanmuBattleReport(ArmyInfo armyInfo, RegionCapitalNode node, boolean isAttackerWin) {
        Role role = armyInfo.getOwner();
        if (role == null) {
            return;
        }
        FightLostInfo defenderLostInfo = armyInfo.getFightContext().getFightResult().getDefenderLostInfo();
        var armyDefender = armyInfo.getFightContext().getDefender();
        if (defenderLostInfo == null || armyDefender.getTargetId() == null) {
            return;
        }
        Long mainDefendRoleId = armyDefender.getTargetId();
        Role mainDefendRole = roleDao.findById(mainDefendRoleId);
        if (mainDefendRole == null) {
            return;
        }
        // 1. 击杀数量大于 配置
        RegionCapitalConfig config = configService.getConfig(RegionCapitalConfig.class);
        // 获取所有击杀数量
        int count = 0;
        if (isAttackerWin) {
            // 进攻方需要查看防守方被击杀
            count = armyInfo.getFightContext().getFightResult().getKillAllCount(false, true);
        } else {
            count = armyInfo.getFightContext().getFightResult().getKillAllCount(true, true);
        }

        if (count < config.getAlertDefeatNum()) {
            return;
        }
        float defeatNum = MathUtils.round((float) count / 10000, 2);
        // 2. 处理数据
        if (isAttackerWin) {
            regionCapitalChatExport.onReportDanmu(node, role, mainDefendRole, defeatNum);
        } else {
            regionCapitalChatExport.onReportDanmu(node, mainDefendRole, role, defeatNum);
        }
    }


    /**
     * 弹幕开关，修改热度
     *
     * @param role
     * @param nodeId
     * @param state
     */
    public void switchDanmu(Role role, long nodeId, String state) {
        RegionCapitalNode node = regionCapitalNodeDao.findById(nodeId);
        if (null == node) {
            return;
        }
        node.incrPopularity("off".equals(state) ? -1 : 1);
//        regionCapitalNodeDao.save(node);
        GcRegionCapitalDanmuNotify msg = new GcRegionCapitalDanmuNotify();
        msg.setPopularity(node.getPopularity());
        msg.setNodeId(node.getPersistKey());
        sceneService.viewForEachAsync(node.getCurrentServerId(), node.getPosition(), p -> {
            p.send(msg);
        });
    }

    /**
     * 被攻城器械攻击
     *
     * @param siegeEnginesNode
     */
    public void attackBySiegeEngines(SiegeEnginesNode siegeEnginesNode) {
        RegionCapitalNode node = regionCapitalNodeDao.findById(siegeEnginesNode.getCapitalNodeId());
        if (node == null) {
            return;
        }
        // 不是pvp
        if (!node.isAttack()) {
            return;
        }
        // 本盟占领不进攻
        if (Objects.equals(node.getBelongAllianceId(), siegeEnginesNode.getAllianceId())) {
            return;
        }

        var declareWarList = node.getDeclareWarList();
        var pvpIndex = declareWarList.get(siegeEnginesNode.getAllianceId());
        if (null == pvpIndex) {
            return;
        }
        var rccConfig = configService.getConfig(RegionCapitalConfig.class);
        // 时间段是否正确
        if (!rccConfig.checkDeclarePVPTime(pvpIndex, TimeUtil.getNow())) {
            return;
        }

        // 开炮
        Map<Long, Map<String, Integer>> roleBadlyMap = new HashMap<>();
        // 获取王城驻防部队
        List<ArmyInfo> armyInfoList = sceneService.getNodeArriveArmies(node);
        AllianceBuildingPositionConfig.AllianceBuildingPosition meta =
                configService.getConfig(AllianceBuildingPositionConfig.class).get(siegeEnginesNode.getMetaId());

        int allBadly = 0;
        for (var army : armyInfoList) {
            Map<String, Integer> badlyMap = new HashMap<>();
            var soldiers = army.getArmySoldiers();
            long score = 0;
            int badlySum = 0;
            for (var _soldier : soldiers.entrySet()) {
                var soldier = _soldier.getValue();
                if (soldier.getCount() <= 0) {
                    continue;
                }

                int badly = (int) Math.ceil(1.0 * soldier.getCount() * meta.getProperty4() / 100);
                badlySum += badly;
                badlyMap.put(soldier.getSoldierMetaId(), badly);
                // 士兵减少
                soldier.subCount(badly);
                var soldierMeta = SoldierConfig.getSoldierMeta(_soldier.getKey());
                if (null != soldierMeta) {
                    score += kvkHonorService.trigger(army.getOwner(), HonorType.BE_KILLED_IN_ARTILLERY,
                            soldierMeta.getEra(), 0, badly);
                }
            }

            if (badlyMap.isEmpty()) {
                continue;
            }

            roleBadlyMap.put(army.getOwner().getRoleId(), badlyMap);
            // 伤兵逻辑
            srvDep.getSoldierService().addWounded(army.getOwner(), badlyMap, SoldierUpdateReasonType.ENGINES, siegeEnginesNode.getPersistKey().toString(), true);

            // 更新信息给前端
            armyService.updateArmyProgress(army);

            // 邮件提醒
            List<String> params = new ArrayList<>();
            params.add(badlySum + "");
            var mail = mailCreator.createSystemMail(army.getRoleId(), node.getCurrentServerId(), rccConfig.getArtilleryAttackMailId(), params, null);
            if (null != mail) {
                mailSender.sendOneMail(mail);
            }

            if (army.getSoldierCount() <= 0) {
                armyManager.returnArmy(army);
            }
            allBadly += badlySum;
        }
        List<ArmyInfo> siegeArmyInfos = sceneService.getNodeArriveArmies(siegeEnginesNode);
        Set<Long> siegeGarrisonRoleIds = siegeArmyInfos.stream().map(ArmyInfo::getRoleId).collect(Collectors.toSet());
        // 本次是否击杀怪物
        boolean isKillNpc = false;
        // 攻击NPC
        if (meta.getProperty3() > 0) {
            long allScore = 0;
            isKillNpc = true;
            RegionCapitalPositionConfig.RegionCapitalPositionMeta nodeMeta =
                    configService.getConfig(RegionCapitalPositionConfig.class).get(node.getMetaId());
            // boss是否死了
            if (!node.isBossDeadAll(nodeMeta)) {
                int bossNum = nodeMeta.getBossNum(node.getBelongAllianceId() > 0);
                int killMonsterNum = 0;
                int killBossNum = meta.getProperty3();
                if (bossNum < meta.getProperty3() + node.getBossDeadCount()) {
                    killBossNum = bossNum - node.getBossDeadCount();
                    killMonsterNum = meta.getProperty3() - killBossNum;
                }
                // boss被打死
                node.setBossDeadCount(node.getBossDeadCount() + killBossNum);
                if (killMonsterNum > 0 && !node.isMonsterDeadAll(nodeMeta)) {
                    node.setMonsterDeadCount(node.getMonsterDeadCount() + killMonsterNum);
                }
                // 积分
                if (killBossNum > 0) {
                    allScore += (long) nodeMeta.getNpcScore()[0] * killBossNum;
                }
                if (killMonsterNum > 0) {
                    allScore += (long) nodeMeta.getNpcScore()[1] * killMonsterNum;
                }

            } else if (!node.isMonsterDeadAll(nodeMeta)) {
                int monsterNum = nodeMeta.getNpcNum(node.getBelongAllianceId() > 0);
                int killMonster = monsterNum < node.getMonsterDeadCount() + meta.getProperty3() ?
                        monsterNum - node.getMonsterDeadCount() : meta.getProperty3();
                // 减少怪物被击杀次数
                node.setMonsterDeadCount(node.getMonsterDeadCount() + killMonster);
                allScore += (long) nodeMeta.getNpcScore()[1] * killMonster;

            } else {
                isKillNpc = false;
            }
            RegionCapitalNode.addRankScore(node.getPveScore(), siegeEnginesNode.getAllianceId(), allScore);
            // 如果存在驻防平分
            if (allScore > 0 && !siegeGarrisonRoleIds.isEmpty()) {
                int num = siegeGarrisonRoleIds.size();
                long s = allScore / num;
                var roleRankScore = node.getCurRolePveScore().computeIfAbsent(siegeEnginesNode.getAllianceId(),
                        v -> new ArrayList<>());
                for (Long rId : siegeGarrisonRoleIds) {
                    RegionCapitalNode.addRankScore(roleRankScore, rId, s);
                }
            }
        }
        regionCapitalNodeDao.save(node);
        // 炮台发射
        logger.info("attackBySiegeEngines node:{} metaId:{} roleBadlyMap:{}", node.getPersistKey(), node.getMetaId(), roleBadlyMap);

        // 如果击杀了最后一个怪物，则计算其胜利
        if (isKillNpc && node.isDeadAll()) {
            // 占领
            Long newAllianceId = 0L;
            // 判断谁的积分高
            var scoreRank = node.getPveScore();
            if (JavaUtils.bool(scoreRank)) {
                newAllianceId = scoreRank.getFirst().getFirst();
            }

            Alliance newAlliance = allianceDao.findById(newAllianceId);
            normalFinish(node, newAlliance);
            // 全服广播
            broadcast(node);
        }

        regionCapitalNodeDao.save(node);
        // aoi通知 具体信息发生了变化
        sceneService.update(node, null);
        // 攻击通知
        Alliance alliance = allianceDao.findById(siegeEnginesNode.getAllianceId());
        if (alliance != null) {
            biLogUtil.siegeEnginesFire(alliance.getPersistKey(), alliance.getName(),
                    siegeEnginesNode.getPersistKey(), allBadly);
        }
    }

    /**
     * 联盟是否在此城池的战争中
     *
     * @return
     */
    public boolean isAllianceAtWar(Long allianceId, Long capitalNodeId) {
        if (allianceId == null) {
            return false;
        }
        RegionCapitalNode node = regionCapitalNodeDao.findById(capitalNodeId);
        if (node == null) {
            return false;
        }
        // 是否报名了这个州府
        Map<Long, Integer> declareWarList = node.getDeclareWarList();
        Integer pvpIndex = declareWarList.get(allianceId);
        if (null == pvpIndex) {
            return false;
        }
        RegionCapitalConfig config = configService.getConfig(RegionCapitalConfig.class);
        // 是否在当前阶段
        return Objects.equals(pvpIndex, config.getPvpIndex(TimeUtil.getNow()));
    }


    public boolean isWithinRegionCapital(int serverId, RegionCapitalNode regionCapitalNode, Point point) {
        if (regionCapitalNode == null) {
            return false;
        }
        if (regionCapitalNode.getCurrentServerId() != serverId) {
            return false;
        }
        RegionCapitalPositionConfig config = configService.getConfig(RegionCapitalPositionConfig.class);
        RegionCapitalPositionConfig.RegionCapitalPositionMeta meta = config.get(regionCapitalNode.getMetaId());
        if (!meta.isOverlap(point)) {
            return false;
        }

        return true;
    }

    // 清理联盟k服数据
    public void clearAllianceForKVK(Alliance alliance) {
        var rcpConfig = configService.getConfig(RegionCapitalPositionConfig.class);
        var metas = rcpConfig.getMetaByServer(MetaServerType.KVK);
        for (var meta : metas) {
            try {
                // k服分配奖励清理
                alliance.removeRegionAllocationReward(meta.getId());
                // k服太守清理
                allianceManager.clearGovernorByMetaId(alliance.getPersistKey(), meta.getId());
                allianceDao.save(alliance);
                logger.info("clearAllianceForKVK alliance:{} metaId:{}", alliance.getPersistKey(), meta.getId());
            } catch (ExpectedException ignored) {
            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("clearAllianceForKVK error", e);
            }
        }
    }
}
