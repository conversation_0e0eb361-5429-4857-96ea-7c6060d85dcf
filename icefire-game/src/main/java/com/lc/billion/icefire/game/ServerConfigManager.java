package com.lc.billion.icefire.game;

import com.lc.billion.icefire.core.application.Env;
import com.lc.billion.icefire.game.biz.service.impl.alipay.config.AlipayReportConfig;
import com.lc.billion.icefire.game.biz.service.impl.bilog.config.BILogConfig;
import com.lc.billion.icefire.game.support.alert.conf.AlertConfig;
import com.lc.billion.icefire.gvgcontrol.biz.GVGBattleServerCreateConfig;
import com.lc.billion.icefire.tvtcontrol.biz.TvtBattleServerCreateConfig;
import com.longtech.ls.config.ServerTypeConfig;
import com.longtech.ls.config.WorldMapConfig;
import com.simfun.sgf.common.ConfigLoader;
import lombok.Getter;

import java.io.File;

/**
 * 
 *
 * <AUTHOR>
 */
public class ServerConfigManager {

	private static final ServerConfigManager instance = new ServerConfigManager();

	public static ServerConfigManager getInstance() {
		return instance;
	}

	private ZookeeperConfig zookeeperConfig;

	private MongoConfig mongoConfig;

	// private ConfigCenter configCenter;

	private GameConfig gameConfig;

	// private RPCConfig rpcConfig;

	private WorldMapConfig worldMapConfig;

	private BILogConfig biLogConfig;

	private ServerTypeConfig serverTypeConfig;

	private AlertConfig alertConfig;

	// private NewDbConfig newDbConfig;

	private GVGBattleServerCreateConfig gvgBattleServerCreateConfig;

	private TvtBattleServerCreateConfig tvtBattleServerCreateConfig;

	/**
	 * 支付宝上报所需配置
	 */
	@Getter
	private AlipayReportConfig alipayReportConfig;

	/**
	 * 应用基础配置
	 */
	private ApplicationConfig applicationConfig;

	public String configDir = "conf" + File.separator;

	public void loadConfig() {

		this.zookeeperConfig = ConfigLoader.loadConfigJson(ZookeeperConfig.class, configDir + GameConstants.ZOOKEEPER_CONFIG_NAME);

		this.mongoConfig = ConfigLoader.loadConfigJson(MongoConfig.class, configDir + GameConstants.MONGO_CONFIG_NAME);
		// GameConfigListener gameConfigListener = new GameConfigListener();
		// this.configCenter = new ConfigCenter(this.zookeeperConfig.getConnectString(), this.zookeeperConfig.getConfigPath(), gameConfigListener);

		this.gameConfig = ConfigLoader.loadConfigJson(GameConfig.class, configDir + GameConstants.GAME_CONFIG_NAME);
        // 方便测试在容器启动文件docker-compose.yml中修改运行环境参数
        this.gameConfig.initFromEnv();
		// this.rpcConfig = ConfigLoader.loadConfigJson(RPCConfig.class, GameConstants.RPC_CONFIG_NAME);
		serverTypeConfig = ConfigLoader.loadConfigJson(ServerTypeConfig.class, configDir + GameConstants.MAP_CONFIG_NAME);
		this.biLogConfig = ConfigLoader.loadConfigJson(BILogConfig.class, configDir + GameConstants.BI_CONFIG_NAME);
		// newDbConfig = ConfigLoader.loadConfigJson(NewDbConfig.class, GameConstants.NEW_DB_CONFIG_NAME);
		// newDbConfig.checkConfig();

		gvgBattleServerCreateConfig = ConfigLoader.loadConfigJson(GVGBattleServerCreateConfig.class, configDir + GameConstants.GVG_BATTLE_CREATE_CONFIG_NAME);

		alertConfig = ConfigLoader.loadConfigJson(AlertConfig.class, configDir + GameConstants.ALERT_CONFIG_NAME);

		tvtBattleServerCreateConfig = ConfigLoader.loadConfigJson(TvtBattleServerCreateConfig.class, configDir + GameConstants.TVT_BATTLE_CREATE_CONFIG_NAME);

		alipayReportConfig = ConfigLoader.loadConfigJson(AlipayReportConfig.class, configDir + GameConstants.ALIPAY_REPORT_CONFIG_NAME);

		applicationConfig = ConfigLoader.loadConfigJson(ApplicationConfig.class, configDir + GameConstants.APPLICATION_CONFIG_NAME);

	}

	public void initWorldMapConfig() {
		worldMapConfig = new WorldMapConfig(serverTypeConfig, Application.getAllServerIds());
	}

	public GameConfig getGameConfig() {
		return gameConfig;
	}

	// public void setGameConfig(GameConfig gameConfig) {
	// this.gameConfig = gameConfig;
	// }

	//
	// public RPCConfig getRpcConfig() {
	// return rpcConfig;
	// }

	public ZookeeperConfig getZookeeperConfig() {
		return zookeeperConfig;
	}

	public WorldMapConfig getWorldMapConfig() {
		return worldMapConfig;
	}

	public BILogConfig getBiLogConfig() {
		return biLogConfig;
	}

	public ServerTypeConfig getServerTypeConfig() {
		return serverTypeConfig;
	}

	public GVGBattleServerCreateConfig getGvgBattleServerCreateConfig() {
		return gvgBattleServerCreateConfig;
	}

	public TvtBattleServerCreateConfig getTvtBattleServerCreateConfig() {
		return tvtBattleServerCreateConfig;
	}

	public MongoConfig getMongoConfig() {
		return mongoConfig;
	}

	public ApplicationConfig getApplicationConfig() {
		return applicationConfig;
	}

	// public ConfigCenter getConfigCenter() {
	// return configCenter;
	// }

	// public void reportCurrentGameServerStatus(int registedRole, int inMemoryRole,
	// int onlineRole) {
	// if (null != this.configCenter) {
	// this.configCenter.updateCurrentGameServerStatus(registedRole, inMemoryRole,
	// onlineRole);
	// }
	// }

	// public void closeConfigCenter() {
	// if (null != this.configCenter) {
	// this.configCenter.close();
	// }
	// }

	// public NewDbConfig getNewDbConfig() {
	// return newDbConfig;
	// }

	public AlertConfig getAlertConfig() {
		return alertConfig;
	}

	public static boolean isDev() {
		return getInstance().getGameConfig().getEnv() == Env.DEV;
	}

	public static boolean isProd() {
		return getInstance().getGameConfig().getEnv() == Env.PROD;
	}
}
