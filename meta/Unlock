[{"id": "1", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_1", "condition": "2|-1", "icon": "main_Btn_Down_Tanxian", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_1", "relation": "", "version": ""}, {"id": "2", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_2", "condition": "2|2", "icon": "zhujiemian_xia_icon1", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_2", "relation": "", "version": ""}, {"id": "3", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_3", "condition": "2|2", "icon": "main_Btn_Down_Bag", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_3", "relation": "", "version": ""}, {"id": "4", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_4", "condition": "11|3", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_4", "relation": "", "version": ""}, {"id": "5", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_5", "condition": "2|7", "icon": "main_Btn_Down_Alliance", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_5", "relation": "", "version": ""}, {"id": "6", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_6", "condition": "2|7", "icon": "cmn_unlock_icon_world", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_6", "relation": "", "version": ""}, {"id": "7", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_7", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "8", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_8", "condition": "2|5", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "9", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_9", "condition": "1", "icon": "", "unlockMethod": "1", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "10", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_10", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "11", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_11", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "12", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_12", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "13", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_13", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "14", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_14", "condition": "20|10", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "15", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_15", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "16", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_16", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "17", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_17", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "18", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_18", "condition": "11|1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_18", "relation": "", "version": ""}, {"id": "19", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_19", "condition": "2|5", "icon": "main_Btn_Circle", "unlockMethod": "2", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "20", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_20", "condition": "18|2", "icon": "main_Btn_Circle", "unlockMethod": "2", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_20", "relation": "", "version": ""}, {"id": "21", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_21", "condition": "4|331901|1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "22", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_22", "condition": "6|6,4|330501|1", "icon": "main_Btn_Circle", "unlockMethod": "2", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "1", "version": ""}, {"id": "23", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_23", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "24", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_24", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "25", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_25", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "26", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_26", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "27", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_27", "condition": "2|2", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "28", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_28", "condition": "2|2", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "29", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_29", "condition": "2|3", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "30", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_30", "condition": "2|7", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "31", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_31", "condition": "3|5", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "32", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_32", "condition": "2|7", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_32", "relation": "", "version": ""}, {"id": "33", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_33", "condition": "2|5", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "34", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_34", "condition": "2|5", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "35", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_35", "condition": "2|6", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "36", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_36", "condition": "2|4", "icon": "main_Icon_store", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "37", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_37", "condition": "2|7", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "38", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_38", "condition": "20|10", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "39", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_39", "condition": "2|7", "icon": "cmn_unlock_icon_tower", "unlockMethod": "2", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_39", "relation": "", "version": ""}, {"id": "40", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_40", "condition": "2|15", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_40", "relation": "", "version": ""}, {"id": "41", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_41", "condition": "17|6", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "42", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_42", "condition": "2|3", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "43", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_43", "condition": "2|6", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "44", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_44", "condition": "11|25", "icon": "", "unlockMethod": "", "isShowLockIcon": "", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_44", "relation": "1", "version": ""}, {"id": "45", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_45", "condition": "11|3", "icon": "", "unlockMethod": "", "isShowLockIcon": "", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_45", "relation": "", "version": ""}, {"id": "46", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_46", "condition": "2|5", "icon": "Main_ActIcon_7days", "unlockMethod": "2", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "47", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_47", "condition": "2|5", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_47", "relation": "", "version": ""}, {"id": "48", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_48", "condition": "2|6", "icon": "Main_ActIcon_Normal", "unlockMethod": "2", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "49", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_49", "condition": "2|5", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "50", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_50", "condition": "11|3", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_50", "relation": "", "version": ""}, {"id": "52", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_52", "condition": "2|10", "icon": "cmn_unlock_icon_ranklist", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_52", "relation": "", "version": ""}, {"id": "51", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_51", "condition": "2|11", "icon": "Main_ActIcon_Gift_Kvk", "unlockMethod": "2", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_51", "relation": "", "version": ""}, {"id": "53", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_53", "condition": "14|3007001", "icon": "", "unlockMethod": "0", "isShowLockIcon": "", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_53", "relation": "", "version": ""}, {"id": "54", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_54", "condition": "2|10", "icon": "cmn_unlock_icon_business", "unlockMethod": "0", "isShowLockIcon": "", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_54", "relation": "", "version": ""}, {"id": "55", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_55", "condition": "2|8", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "wechatmissions13", "relation": "", "version": ""}, {"id": "56", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_56", "condition": "2|6", "icon": "activity_tab_aoshiqunxiong", "unlockMethod": "2", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_56", "relation": "", "version": ""}, {"id": "57", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_57", "condition": "2|3", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "58", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_58", "condition": "2|3", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "59", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_59", "condition": "2|2", "icon": "", "unlockMethod": "0", "isShowLockIcon": "", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "60", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_60", "condition": "2|23", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "LordTreasureTips001", "relation": "", "version": ""}, {"id": "61", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_61", "condition": "2|25", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "LordTreasureTips002", "relation": "", "version": ""}, {"id": "62", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_62", "condition": "2|22", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "LordTreasureTips001", "relation": "", "version": ""}, {"id": "63", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_63", "condition": "2|16", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "LordTreasureTips001", "relation": "", "version": ""}, {"id": "64", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_64", "condition": "8|10501001,20|10", "icon": "icon_item_10501001", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_64", "relation": "1", "version": ""}, {"id": "65", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_65", "condition": "2|3,14|2004001", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "1", "version": ""}, {"id": "66", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_66", "condition": "0", "icon": "Main_ActIcon_share", "unlockMethod": "2", "isShowLockIcon": "1", "switchName": "shareActivity", "LFuncExplain": "", "relation": "1", "version": ""}, {"id": "67", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_67", "condition": "21", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "1", "version": ""}, {"id": "68", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_68", "condition": "2|10", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_67", "relation": "", "version": ""}, {"id": "69", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_69", "condition": "2|19", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_69", "relation": "", "version": ""}, {"id": "70", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_70", "condition": "2|20", "icon": "", "unlockMethod": "0", "isShowLockIcon": "", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "71", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_71", "condition": "6|17,4|330601|1", "icon": "main_Btn_Circle", "unlockMethod": "2", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "1", "version": ""}, {"id": "72", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_72", "condition": "4|330601|4", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "73", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_73", "condition": "11|300000", "icon": "", "unlockMethod": "", "isShowLockIcon": "", "switchName": "", "LFuncExplain": "Unlock_FuncExplain_73", "relation": "", "version": ""}, {"id": "74", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_74", "condition": "2|3", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "peoplePropertyGroup", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "75", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_75", "condition": "18|10", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "76", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_76", "condition": "18|15,12|7", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "1", "version": ""}, {"id": "77", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_77", "condition": "6|111004,4|330601|1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "1", "version": ""}, {"id": "78", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_78", "condition": "6|111004,4|330601|1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "1", "version": ""}, {"id": "79", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_79", "condition": "22|2", "icon": "", "unlockMethod": "0", "isShowLockIcon": "", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "80", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_80", "condition": "1", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "81", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_81", "condition": "22|3", "icon": "", "unlockMethod": "0", "isShowLockIcon": "", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "82", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_82", "condition": "18|5", "icon": "", "unlockMethod": "0", "isShowLockIcon": "1", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}, {"id": "83", "isTest": "", "season": "", "configEditor": "", "LName": "Unlock_Name_83", "condition": "20|5", "icon": "", "unlockMethod": "0", "isShowLockIcon": "", "switchName": "", "LFuncExplain": "", "relation": "", "version": ""}]