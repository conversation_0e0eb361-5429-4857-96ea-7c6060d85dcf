[{"id": "1100", "levelId": "1", "enemyList": "SK_WZS_npc_001", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "1", "winEventParams": "1", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600001", "abTest": "0", "unlockGridList": "0", "unlockBigGrid": "", "unlockNpc": "0", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "0", "unlockLevelLimit": ""}, {"id": "1200", "levelId": "2", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "1", "winEventParams": "2", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600001", "abTest": "0", "unlockGridList": "0", "unlockBigGrid": "", "unlockNpc": "0", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "0", "unlockLevelLimit": ""}, {"id": "1300", "levelId": "3", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "1", "winEventParams": "3", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600001", "abTest": "0", "unlockGridList": "0", "unlockBigGrid": "", "unlockNpc": "0", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "0", "unlockLevelLimit": ""}, {"id": "1400", "levelId": "4", "enemyList": "", "enemyAction": "", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "1", "winEventParams": "4", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600001", "abTest": "0", "unlockGridList": "0", "unlockBigGrid": "", "unlockNpc": "0", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "0", "unlockLevelLimit": ""}, {"id": "1500", "levelId": "5", "enemyList": "SK_WZS_npc_001", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "3", "winEventParams": "0;ui/images/maincity/operate_err", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600002", "abTest": "0", "unlockGridList": "14", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "2", "unlockLevelLimit": ""}, {"id": "1600", "levelId": "6", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "5", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600002", "abTest": "0", "unlockGridList": "14", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "2", "unlockLevelLimit": ""}, {"id": "1700", "levelId": "7", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "14", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "2", "unlockLevelLimit": "2"}, {"id": "1800", "levelId": "8", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "7", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600002", "abTest": "0", "unlockGridList": "14", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "2", "unlockLevelLimit": ""}, {"id": "1900", "levelId": "9", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "3", "winEventParams": "0;ui/images/maincity/operate_injection", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600008", "abTest": "0", "unlockGridList": "14", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes2", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "2", "unlockLevelLimit": ""}, {"id": "2000", "levelId": "10", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "14", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes1", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "2", "unlockLevelLimit": "3"}, {"id": "2100", "levelId": "11", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "10", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600002", "abTest": "0", "unlockGridList": "14", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "2", "unlockLevelLimit": ""}, {"id": "2200", "levelId": "12", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "14", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "2", "unlockLevelLimit": ""}, {"id": "2300", "levelId": "13", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "14", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "2", "unlockLevelLimit": ""}, {"id": "2400", "levelId": "14", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "1", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "3", "unlockLevelLimit": ""}, {"id": "2500", "levelId": "15", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "4", "winEventParams": "", "affiliatedList": "16,17,18,19", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "3", "unlockLevelLimit": ""}, {"id": "2600", "levelId": "16", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "5", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "3", "unlockLevelLimit": ""}, {"id": "2700", "levelId": "17", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "5", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "3", "unlockLevelLimit": ""}, {"id": "2800", "levelId": "18", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "5", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "3", "unlockLevelLimit": ""}, {"id": "2900", "levelId": "19", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "5", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600006", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes1", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "3", "unlockLevelLimit": ""}, {"id": "3000", "levelId": "20", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "19", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600002", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "3", "unlockLevelLimit": ""}, {"id": "3100", "levelId": "21", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "13", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "3", "unlockLevelLimit": "4"}, {"id": "3200", "levelId": "22", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "21", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600002", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "3", "unlockLevelLimit": ""}, {"id": "3300", "levelId": "23", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "3", "winEventParams": "0;ui/images/maincity/operate_injection", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600008", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes2", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "3", "unlockLevelLimit": ""}, {"id": "3400", "levelId": "24", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "25", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "3", "unlockLevelLimit": ""}, {"id": "3500", "levelId": "25", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "13", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "30", "unlockBigGrid": "2", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "4", "unlockLevelLimit": ""}, {"id": "3600", "levelId": "26", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "13", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "30", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "4", "unlockLevelLimit": ""}, {"id": "3700", "levelId": "27", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "26", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "30", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "4", "unlockLevelLimit": ""}, {"id": "3800", "levelId": "28", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "3", "winEventParams": "0;ui/images/maincity/operate_injection", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600008", "abTest": "0", "unlockGridList": "30", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes2", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "4", "unlockLevelLimit": ""}, {"id": "3900", "levelId": "29", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "28", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "30", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "4", "unlockLevelLimit": ""}, {"id": "4000", "levelId": "30", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "24", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "35", "unlockBigGrid": "3", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "5", "unlockLevelLimit": ""}, {"id": "4100", "levelId": "31", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "30", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "35", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "5", "unlockLevelLimit": "5"}, {"id": "4200", "levelId": "32", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "24", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "35", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "5", "unlockLevelLimit": ""}, {"id": "4300", "levelId": "33", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "24", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "35", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "5", "unlockLevelLimit": ""}, {"id": "4400", "levelId": "34", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "24", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "35", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "5", "unlockLevelLimit": ""}, {"id": "4500", "levelId": "35", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "24", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "40", "unlockBigGrid": "4", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "6", "unlockLevelLimit": ""}, {"id": "4600", "levelId": "36", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "35", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "40", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "6", "unlockLevelLimit": "6"}, {"id": "4700", "levelId": "37", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "24", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "40", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "6", "unlockLevelLimit": ""}, {"id": "4800", "levelId": "38", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "3", "winEventParams": "0;ui/images/maincity/operate_injection", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600008", "abTest": "0", "unlockGridList": "40", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes2", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "6", "unlockLevelLimit": ""}, {"id": "4900", "levelId": "39", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "38", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "40", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "6", "unlockLevelLimit": ""}, {"id": "5000", "levelId": "40", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "33", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "47", "unlockBigGrid": "5", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "7", "unlockLevelLimit": ""}, {"id": "5100", "levelId": "41", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "33", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "47", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "7", "unlockLevelLimit": ""}, {"id": "5200", "levelId": "42", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "41", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "47", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "7", "unlockLevelLimit": ""}, {"id": "5300", "levelId": "43", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "33", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "47", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "7", "unlockLevelLimit": ""}, {"id": "5400", "levelId": "44", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "3", "winEventParams": "0;ui/images/maincity/operate_injection", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600008", "abTest": "0", "unlockGridList": "47", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes2", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "7", "unlockLevelLimit": ""}, {"id": "5500", "levelId": "45", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "44", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "47", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "7", "unlockLevelLimit": ""}, {"id": "5600", "levelId": "46", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "33", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "47", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "7", "unlockLevelLimit": ""}, {"id": "5700", "levelId": "47", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "33", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "53", "unlockBigGrid": "6", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "8", "unlockLevelLimit": ""}, {"id": "5800", "levelId": "48", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "47", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "53", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "8", "unlockLevelLimit": "7"}, {"id": "5900", "levelId": "49", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "39", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "53", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "8", "unlockLevelLimit": ""}, {"id": "6000", "levelId": "50", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "49", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "53", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "8", "unlockLevelLimit": ""}, {"id": "6100", "levelId": "51", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "39", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "53", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "8", "unlockLevelLimit": ""}, {"id": "6200", "levelId": "52", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "39", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "53", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "8", "unlockLevelLimit": ""}, {"id": "6300", "levelId": "53", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "39", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "61", "unlockBigGrid": "7", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "9", "unlockLevelLimit": ""}, {"id": "6400", "levelId": "54", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "4", "winEventParams": "", "affiliatedList": "55,56,57,58", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "", "abTest": "0", "unlockGridList": "61", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "9", "unlockLevelLimit": ""}, {"id": "6500", "levelId": "55", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "5", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "", "abTest": "0", "unlockGridList": "61", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "9", "unlockLevelLimit": ""}, {"id": "6600", "levelId": "56", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "5", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "", "abTest": "0", "unlockGridList": "61", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "9", "unlockLevelLimit": ""}, {"id": "6700", "levelId": "57", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "5", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "", "abTest": "0", "unlockGridList": "61", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "9", "unlockLevelLimit": ""}, {"id": "6800", "levelId": "58", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "5", "winEventParams": "", "affiliatedList": "", "showModelLimt": "", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600006", "abTest": "0", "unlockGridList": "61", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes1", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "9", "unlockLevelLimit": ""}, {"id": "6900", "levelId": "59", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "58", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600003", "abTest": "0", "unlockGridList": "61", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "9", "unlockLevelLimit": ""}, {"id": "7000", "levelId": "60", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "43", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "61", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "9", "unlockLevelLimit": ""}, {"id": "7100", "levelId": "61", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "43", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "66", "unlockBigGrid": "8", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "10", "unlockLevelLimit": ""}, {"id": "7200", "levelId": "62", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "43", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "66", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "10", "unlockLevelLimit": "8"}, {"id": "7300", "levelId": "63", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "43", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "66", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "10", "unlockLevelLimit": ""}, {"id": "7400", "levelId": "64", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "63", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600004", "abTest": "0", "unlockGridList": "66", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "10", "unlockLevelLimit": ""}, {"id": "7500", "levelId": "65", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "43", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "66", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "10", "unlockLevelLimit": ""}, {"id": "7600", "levelId": "66", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "60", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "71", "unlockBigGrid": "9", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "11", "unlockLevelLimit": ""}, {"id": "7700", "levelId": "67", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "60", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "71", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "11", "unlockLevelLimit": ""}, {"id": "7800", "levelId": "68", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "67", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600004", "abTest": "0", "unlockGridList": "71", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "11", "unlockLevelLimit": ""}, {"id": "7900", "levelId": "69", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "60", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "71", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "11", "unlockLevelLimit": ""}, {"id": "8000", "levelId": "70", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "60", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "71", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "11", "unlockLevelLimit": ""}, {"id": "8100", "levelId": "71", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "60", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "81", "unlockBigGrid": "10", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "12", "unlockLevelLimit": ""}, {"id": "8200", "levelId": "72", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "60", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "81", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "12", "unlockLevelLimit": "9"}, {"id": "8300", "levelId": "73", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "72", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600004", "abTest": "0", "unlockGridList": "81", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "12", "unlockLevelLimit": ""}, {"id": "8400", "levelId": "74", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "65", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "81", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "12", "unlockLevelLimit": ""}, {"id": "8500", "levelId": "75", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "74", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600004", "abTest": "0", "unlockGridList": "81", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "12", "unlockLevelLimit": ""}, {"id": "8600", "levelId": "76", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "65", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "81", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "12", "unlockLevelLimit": ""}, {"id": "8700", "levelId": "77", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "76", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600004", "abTest": "0", "unlockGridList": "81", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "12", "unlockLevelLimit": ""}, {"id": "8800", "levelId": "78", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "65", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "81", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "12", "unlockLevelLimit": ""}, {"id": "8900", "levelId": "79", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "65", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "81", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "12", "unlockLevelLimit": ""}, {"id": "9000", "levelId": "80", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "79", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600004", "abTest": "0", "unlockGridList": "81", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "12", "unlockLevelLimit": ""}, {"id": "9100", "levelId": "81", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "65", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "86", "unlockBigGrid": "11", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "13", "unlockLevelLimit": ""}, {"id": "9200", "levelId": "82", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "78", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600004", "abTest": "0", "unlockGridList": "86", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "13", "unlockLevelLimit": ""}, {"id": "9300", "levelId": "83", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "78", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "86", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "13", "unlockLevelLimit": ""}, {"id": "9400", "levelId": "84", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "78", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "86", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "13", "unlockLevelLimit": ""}, {"id": "9500", "levelId": "85", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "78", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "86", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "13", "unlockLevelLimit": ""}, {"id": "9600", "levelId": "86", "enemyList": "npc_baoxiang", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "78", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600007", "abTest": "0", "unlockGridList": "91", "unlockBigGrid": "12", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes3", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "14", "unlockLevelLimit": ""}, {"id": "9700", "levelId": "87", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "86", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600004", "abTest": "0", "unlockGridList": "91", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "14", "unlockLevelLimit": ""}, {"id": "9800", "levelId": "88", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "78", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "91", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "14", "unlockLevelLimit": ""}, {"id": "9900", "levelId": "89", "enemyList": "SK_WZS_npc_003", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "88", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600004", "abTest": "0", "unlockGridList": "91", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "14", "unlockLevelLimit": ""}, {"id": "10000", "levelId": "90", "enemyList": "npc_kapai", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "78", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600005", "abTest": "0", "unlockGridList": "91", "unlockBigGrid": "", "unlockNpc": "1", "levelName": "", "levelDsc": "", "levelIcon": "", "unlockFog": "14", "unlockLevelLimit": ""}, {"id": "10100", "levelId": "91", "enemyList": "SK_WZS_npc_002", "enemyAction": "idle", "unlockTypeList": "", "unlockParams": "", "lockTipsLangKey": "1", "winEventType": "2", "winEventParams": "", "affiliatedList": "", "showModelLimt": "78", "winEventModel": "", "winEventAction": "", "dropGroupId": "3600009", "abTest": "0", "unlockGridList": "0", "unlockBigGrid": "13", "unlockNpc": "1", "levelName": "innerpvelevelcount", "levelDsc": "innerpvenDes4", "levelIcon": "ui/images/building_common/new/linshi_box", "unlockFog": "14", "unlockLevelLimit": ""}]