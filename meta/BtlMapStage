[{"id": "1", "FieldType": "1", "PassCondition": "3", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-2~2,22~25,1001101,1,1|-2~2,22~25,1001102,2,1|-2~2,22~25,1001103,1,1|-2.5~1.5,36~42,1001101,8,1|0~0,40~40,1001107,1,1|-2.5~1.5,43~48,1001102,8,1|-2.5~1.5,50~60,1001103,8,1|0~0,58~58,1001109,1,1|1.5~4,70~75,1001101,11,1|-4~-1.5,70~75,1001102,12,1|-1.5~1.5,70~75,1001103,10,1|1~3,75~75,1001100,1,1", "Building": "0,15,1001201|2.3,15,1001202|0,32,1001203|-2.3,40,1001204|2.3,50,1001205|-2.3,63,1001206|0,63,1001207|2.3,63,1001208"}, {"id": "2", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "10,200", "MapOffSetX": "0", "Monster": "-4~-2.5,25~31,1002104,5,1|-4~-2.5,32~38,1002102,6,1|-4~-2.5,39~45,1002101,5,1|-4~-2.5,45~50,1002107,2,1|-4~-1,62~66,1002103,8,1|-2.5~0,62~66,1002102,8,1|-4~-2.5,66~68,1002109,1,1|-2.5~0,66~68,1002108,1,1|-2.5~0,69~72,1002103,12,1|-3.5~-1,75~80,1002101,10,1|-2.5~0,75~80,1002102,10,1|-4~-2,75~80,1002103,10,1|-2~-1.5,81~83,1002105,1,1", "Building": "2.7,15,1002201|2.7,25,1002202|2.7,30,1002203|2.7,35,1002204|2.7,40,1002205|2.7,45,1002206|2.7,55,1002207|2.7,60,1002208|2.7,65,1002209|2.7,70,1002210|-2.7,30,1002211|-4,55,1002212|-0.5,70,1002213|-4,80,1002214|-0.5,80,1002215"}, {"id": "3", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "0~0,20~20,1003101,1,1|-1.5~1.5,29~40,1003102,12,1|0~0,42~42,1003108,1,1|-1.5~1.5,48~55,1003103,13,1|0~0,51~51,1003109,1,1|-1.5~1.5,58~61,1003103,9,1|0~3,63~66,1003104,10,1|-1.5~1.5,72~75,1003101,10,1|-2.5~-2.5,73~73,1003107,1,1|1.5~3.5,75~78,1003102,12,1|2~2,78~78,1003108,1,1|-4~-1.5,80~84,1003103,10,1|0~0,85~85,1003106,1,1", "Building": "-2.3,15,1003201|0,15,1003202|2.3,15,1003203|0,28,1003204|-2.3,42,1003205|2.3,42,1003206|-2.3,56,1003207|0,56,1003208|2.3,56,1003209|-2.3,70,1003210|0,70,1003211"}, {"id": "4", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "0~0,20~20,1004101,1,1|-1.5~1.5,29~40,1004102,12,1|0~0,42~42,1004108,1,1|-1.5~1.5,48~55,1004103,13,1|0~0,51~51,1004109,1,1|-1.5~1.5,58~61,1004103,9,1|0~3,63~66,1004104,10,1|-1.5~1.5,72~75,1004101,10,1|-2.5~-2.5,73~73,1004107,1,1|1.5~3.5,75~78,1004102,12,1|2~2,78~78,1004108,1,1|-4~-1.5,80~84,1004103,10,1|0~0,85~85,1004106,1,1", "Building": "-2.3,15,1004201|0,15,1004202|2.3,15,1004203|0,28,1004204|-2.3,42,1004205|2.3,42,1004206|-2.3,56,1004207|0,56,1004208|2.3,56,1004209|-2.3,70,1004210|0,70,1004211"}, {"id": "5", "FieldType": "2", "PassCondition": "2", "Speed": "3", "MapSize": "8,160", "MapOffSetX": "0", "Monster": "0~0,16~16,1005101,1,0|0~0,20~20,1005101,1,0|-3~3,24~24,1005101,3,0|-1~1,37~40,1005102,12,0|-3~-1.5,44~46,1005103,12,0|-1~1,50~53,1005104,12,0|1.5~3,60~63,1005105,8,0|-1~1,76~79,1005102,12,0|-1~1,83~85,1005103,12,0|1.5~3,86~87,1005104,12,0|1.5~3,92~94,1005105,8,0|-1~1,93~95,1005106,8,0", "Building": "-2.3,20,1005201|2.3,20,1005202|-2.3,40,1005203|2.3,40,1005204|-2.3,62,1005205|2.3,69,1005206|-2.3,75,1005207"}, {"id": "6", "FieldType": "3", "PassCondition": "1", "Speed": "1", "MapSize": "30,30", "MapOffSetX": "92", "Monster": "-12~12,-5~20,1001101,100,0|0~0,10~10,1001100,1,0", "Building": ""}, {"id": "7", "FieldType": "2", "PassCondition": "2", "Speed": "3", "MapSize": "8,120", "MapOffSetX": "0", "Monster": "0~0,12~12,1005101,1,0|0~0,15~15,1005101,1,0|-3~3,19~19,1005101,3,0|-1~1,26~30,1005102,12,0|-3~-1.5,33~36,1005103,12,0|-1~1,40~43,1005104,12,0|1.5~3,46~50,1005105,8,0|-1~1,56~59,1005102,12,0|-1~1,63~65,1005103,12,0", "Building": "-2.3,10,1005201|2.3,10,1005202|-2.3,25,1005203|2.3,25,1005204|-2.3,48,1005205|2.3,48,1005206|-2.3,60,1005207"}, {"id": "8", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-2~2,22~25,1007101,1,1|-2~2,22~25,1007102,2,1|-2~2,22~25,1007103,1,1|-2.5~1.5,36~42,1007101,8,1|0~0,40~40,1007107,1,1|-2.5~1.5,43~48,1007102,8,1|-2.5~1.5,50~60,1007103,8,1|0~0,58~58,1007109,1,1|1.5~4,70~75,1007101,11,1|-4~-1.5,70~75,1007102,12,1|-1.5~1.5,70~75,1007103,10,1|1~3,75~75,1007106,1,1", "Building": ""}, {"id": "9", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-2~2,22~25,1007101,1,1|-2~2,22~25,1007102,2,1|-2~2,22~25,1007103,1,1|-2.5~1.5,36~42,1007101,8,1|0~0,40~40,1007107,1,1|-2.5~1.5,43~48,1007102,8,1|-2.5~1.5,50~60,1007103,8,1|0~0,58~58,1007109,1,1|1.5~4,70~75,1007101,11,1|-4~-1.5,70~75,1007102,12,1|-1.5~1.5,70~75,1007103,10,1|1~3,75~75,1007106,1,1", "Building": ""}, {"id": "10", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-2~2,22~25,1007101,1,1|-2~2,22~25,1007102,2,1|-2~2,22~25,1007103,1,1|-2.5~1.5,36~42,1007101,8,1|0~0,40~40,1007107,1,1|-2.5~1.5,43~48,1007102,8,1|-2.5~1.5,50~60,1007103,8,1|0~0,58~58,1007109,1,1|1.5~4,70~75,1007101,11,1|-4~-1.5,70~75,1007102,12,1|-1.5~1.5,70~75,1007103,10,1|1~3,75~75,1007106,1,1", "Building": "0,15,1001203"}, {"id": "11", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "0~0,20~20,1011101,1,1|-1.5~1.5,29~40,1011102,12,1|0~0,42~42,1011108,1,1|-1.5~1.5,48~55,1011103,13,1|0~0,51~51,1011109,1,1|-1.5~1.5,58~61,1011103,9,1|0~3,63~66,1011104,10,1|-1.5~1.5,72~75,1011101,10,1|-2.5~-2.5,73~73,1011107,1,1|1.5~3.5,75~78,1011102,12,1|2~2,78~78,1011108,1,1|-4~-1.5,80~84,1011103,10,1|0~0,85~85,1011106,1,1", "Building": "-2.3,15,1011201|0,15,1011202|2.3,15,1011203|0,28,1011204|-2.3,42,1011205|2.3,42,1011206|-2.3,56,1011207|0,56,1011208|2.3,56,1011209|-2.3,70,1011210|0,70,1011211"}, {"id": "12", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "0~0,20~20,1012101,1,1|-1.5~1.5,29~40,1012102,12,1|0~0,42~42,1012108,1,1|-1.5~1.5,48~55,1012103,13,1|0~0,51~51,1012109,1,1|-1.5~1.5,58~61,1012103,9,1|0~3,63~66,1012104,10,1|-1.5~1.5,72~75,1012101,10,1|-2.5~-2.5,73~73,1012107,1,1|1.5~3.5,75~78,1012102,12,1|2~2,78~78,1012108,1,1|-4~-1.5,80~84,1012103,10,1|0~0,85~85,1012106,1,1", "Building": "-2.3,15,1012201|0,15,1012202|2.3,15,1012203|0,28,1012204|-2.3,42,1012205|2.3,42,1012206|-2.3,56,1012207|0,56,1012208|2.3,56,1012209|-2.3,70,1012210|0,70,1012211"}, {"id": "13", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "0~0,20~20,1013101,1,1|-1.5~1.5,29~40,1013102,12,1|0~0,42~42,1013108,1,1|-1.5~1.5,48~55,1013103,13,1|0~0,51~51,1013109,1,1|-1.5~1.5,58~61,1013103,9,1|0~3,63~66,1013104,10,1|-1.5~1.5,72~75,1013101,10,1|-2.5~-2.5,73~73,1013107,1,1|1.5~3.5,75~78,1013102,12,1|2~2,78~78,1013108,1,1|-4~-1.5,80~84,1013103,10,1|0~0,85~85,1013106,1,1", "Building": "-2.3,15,1013201|0,15,1013202|2.3,15,1013203|0,28,1013204|-2.3,42,1013205|2.3,42,1013206|-2.3,56,1013207|0,56,1013208|2.3,56,1013209|-2.3,70,1013210|0,70,1013211"}, {"id": "14", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "0~0,20~20,1014101,1,1|-1.5~1.5,29~40,1014102,12,1|0~0,42~42,1014108,1,1|-1.5~1.5,48~55,1014103,13,1|0~0,51~51,1014109,1,1|-1.5~1.5,58~61,1014103,9,1|0~3,63~66,1014104,10,1|-1.5~1.5,72~75,1014101,10,1|-2.5~-2.5,73~73,1014107,1,1|1.5~3.5,75~78,1014102,12,1|2~2,78~78,1014108,1,1|-4~-1.5,80~84,1014103,10,1|0~0,85~85,1014106,1,1", "Building": "-2.3,15,1014201|0,15,1014202|2.3,15,1014203|0,28,1014204|-2.3,42,1014205|2.3,42,1014206|-2.3,56,1014207|0,56,1014208|2.3,56,1014209|-2.3,70,1014210|0,70,1014211"}, {"id": "15", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "0~0,20~20,1015101,1,1|-1.5~1.5,29~40,1015102,12,1|0~0,42~42,1015108,1,1|-1.5~1.5,48~55,1015103,13,1|0~0,51~51,1015109,1,1|-1.5~1.5,58~61,1015103,9,1|0~3,63~66,1015104,10,1|-1.5~1.5,72~75,1015101,10,1|-2.5~-2.5,73~73,1015107,1,1|1.5~3.5,75~78,1015102,12,1|2~2,78~78,1015108,1,1|-4~-1.5,80~84,1015103,10,1|0~0,85~85,1015106,1,1", "Building": "-2.3,15,1015201|0,15,1015202|2.3,15,1015203|0,28,1015204|-2.3,42,1015205|2.3,42,1015206|-2.3,56,1015207|0,56,1015208|2.3,56,1015209|-2.3,70,1015210|0,70,1015211"}]