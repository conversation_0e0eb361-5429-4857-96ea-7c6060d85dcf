package com.lc.billion.icefire.core;

import java.nio.charset.Charset;

/**
 * 
 *
 * <AUTHOR>
 */
public class Constants {

	/** 默认编码 */
	public static final String DEFAULT_ENCODING = "UTF-8".intern();

	/** 字符串默认值 */
	public static final String DEFAULT_STRING_VAL = "".intern();

	/** {@link IndexedEnum} 的实现枚举的空值 */
	public static final int TYPE_NULL = 0;

	/** 表示false的数值 */
	public static final int FALSE_NUM = 0;
	/** 表示true的数值 */
	public static final int TRUE_NUM = 1;

	/** 整数默认值 */
	public static final int DEFAULT_INT_VAL = 0;
	/** 短整数默认值 */
	public static final short DEFAULT_SHORT_VAL = 0;
	/** 字符默认值 */
	public static final char DEFAULT_CHAR_VAL = 0;
	/** 字节默认值 */
	public static final byte DEFAULT_BYTE_VAL = 0;
	/** 长整默认值 */
	public static final long DEFAULT_LONG_VAL = 0L;
	/** 布尔默认值 */
	public static final boolean DEFAULT_BOOL_VAL = false;
	/** 单精度浮点默认值 */
	public static final float DEFAULT_FLOAT_VAL = 0.0F;
	/** 双精度浮点默认值 */
	public static final double DEFAULT_DOUBLE_VAL = 0.0D;

	/** 时间相关参数 */
	public static final long SEC = 1000L; // 秒
	public static final long MIN = 1000L * 60; // 分
	public static final long HOUR = MIN * 60; // 时
	public static final long DAY = HOUR * 24; // 天
	public static final long WEEK = DAY * 7; // 周

	/** 默认日期格式 */
	public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd".intern();
	/** 默认时间格式 */
	public static final String DEFAULT_TIME_FORMAT = "HH:mm:ss".intern();
	/** 默认日期时间格式(精确到秒) */
	public static final String DEFAULT_DATETIME_FORMAT = (DEFAULT_DATE_FORMAT + " " + DEFAULT_TIME_FORMAT).intern();
	/** DB表名用的特殊格式 */
	public static final String DBTABLE_DATE_FORMAT = "yyyyMMdd".intern();

	/** 判断DB连接有效超时时间 */
	public static final int DB_CONN_VALID_TIMEOUT = 1;

	public static final String DEFAULT_CHARSET_NAME = "UTF-8".intern();

	public static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");

	public static final int REGIONID_OFFSET = 10000;

	/** Distr中的常量 */

	// 配置前缀
	public static final String PREFIX_NODE_ADDR = "node.addr";
	public static final String PREFIX_PORT_STARTUP = "port.startwith";
	public static final String PREFIX_SERV_STARTUP = "serv.startwith";

	// 游戏服务器Node前缀
	public static final String NODE_WORLD_PREFIX = "world";
	// 默认主控游戏服务Node
	public static final String NODE_DEFAULT = "world0";
	// 默认主控游戏服务Port
	public static final String PORT_DEFAULT = "port0";
	// 扩展游戏服务Port
	public static final String PORT_EXT = "port1";
	// 整合服务(中枢)
	public static final String SERV_SEAM = "seam";

	// 数据库Node名称
	public static final String NODE_DB = "db";
	// 平台服Node名称
	public static final String NODE_PF = "platform"; // NodeID

	public static final String PORT_LOG = "log"; // 日志服务Port名称
	public static final String PORT_DB_LOG = "dbLog"; // 日志DB默认Port名称

	public static final String SERV_LOG = "log"; // 日志服务ID
	public static final String SERV_LOG_DB = "logDb"; // 日志DB服务ID

	public static final int PORT_STARTUP_NUM_PUSH = 5; // 推送服务实例数
	public static final String PORT_PUSH_PREFIX = "gift"; // Push服务Port前缀
	public static final String SERV_PUSH = "push"; // 推送服务ID

	public static final String PUSH_CONFIG = "push.properties"; // 推送配置文件

	public static final String IBATIS_CONFIG = "log_ibatis_config.xml"; // ibatis配置文件名

	public static long createTableTaskDelay = 1000 * 60 * 24; // 定时创建每日日志表任务启动的延迟时间
	public static long createTableTaskPeriod = 1000 * 60 * 24; // 定时创建每日日志表任务的执行周期
	/** 登录检测验证 */
	public static String LOGIN_VERIFICATION_TOKEN = "login_verification_token_";
    /** 登录设备黑名单 */
    public static String LOGIN_DEVICE_BLACKLIST = "login_device_blacklist";
	/** redisKey:新手服目标服务器人数 */
	public static String REDIS_KEY_TARGET_SERVER_USER_COUNT= "target_server_user_count";
	/**
	 * redisKey:归因指标服务器注册人数
	 */
	public static String REDIS_KEY_SERVER_USER_COUNT_BY_SUBCHANNEL = "server_user_count_by_subchannel";

    /** 私域拉新用户redis key */
    public static String SI_YU_LINK_NEW_USER_REDIS_KEY = "siyulink_new_user";
    /** 私域拉新用户奖励已经发的redis key */
    public static String SI_YU_LINK_NEW_USER_REWARDED_REDIS_KEY = "siyulink_new_user_rewarded";
    public final static String SI_YU_LINK_NEW_USER_TYPE_1 = "sgbh1";
    public final static String SI_YU_LINK_NEW_USER_TYPE_2 = "sgbh2";

    /**
     * 邀请码
     */
    public static String INVITE_CODE = "INVITE_CODE";
    /**
     * 邀请码绑定信息
     */
    public static String INVITE_CODE_BIND = "INVITE_CODE_BIND";

	/**
	 * 获取归因指标服务器注册人数redis key
	 *
	 * @param serverId
	 * @param subChannel
	 * @return
	 */
	public static String getRedisKeyServerUserCountBySubChannel(int serverId, String subChannel) {
		return REDIS_KEY_SERVER_USER_COUNT_BY_SUBCHANNEL + "_" + serverId + "_" + subChannel;
	}

}
